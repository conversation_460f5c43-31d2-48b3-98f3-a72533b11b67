/**
 * 编辑器常量定义
 */

/**
 * 编辑器版本信息
 */
export const EDITOR_VERSION = '1.0.0'
export const EDITOR_NAME = 'DL-Engine Editor'
export const EDITOR_DESCRIPTION = 'Digital Learning Engine 编辑器'

/**
 * 文件类型常量
 */
export const FILE_TYPES = {
  SCENE: '.scene',
  MODEL: ['.gltf', '.glb', '.fbx', '.obj'],
  TEXTURE: ['.jpg', '.jpeg', '.png', '.webp', '.tga', '.hdr', '.exr'],
  AUDIO: ['.mp3', '.wav', '.ogg', '.m4a'],
  VIDEO: ['.mp4', '.webm', '.ogv'],
  SCRIPT: ['.js', '.ts', '.json'],
  MATERIAL: '.mat'
} as const

/**
 * 编辑器事件常量
 */
export const EDITOR_EVENTS = {
  SCENE_LOADED: 'scene:loaded',
  SCENE_SAVED: 'scene:saved',
  SCENE_CHANGED: 'scene:changed',
  SELECTION_CHANGED: 'selection:changed',
  TRANSFORM_CHANGED: 'transform:changed',
  TOOL_CHANGED: 'tool:changed',
  MODE_CHANGED: 'mode:changed',
  ASSET_IMPORTED: 'asset:imported',
  ASSET_DELETED: 'asset:deleted',
  ERROR_OCCURRED: 'error:occurred',
  WARNING_OCCURRED: 'warning:occurred'
} as const

/**
 * 本地存储键名
 */
export const STORAGE_KEYS = {
  EDITOR_LAYOUT: 'dl-editor-layout',
  EDITOR_SETTINGS: 'dl-editor-settings',
  RECENT_PROJECTS: 'dl-editor-recent-projects',
  USER_PREFERENCES: 'dl-editor-user-preferences'
} as const

/**
 * API端点常量
 */
export const API_ENDPOINTS = {
  SCENES: '/api/scenes',
  ASSETS: '/api/assets',
  PROJECTS: '/api/projects',
  USERS: '/api/users',
  UPLOAD: '/api/upload'
} as const

/**
 * 默认值常量
 */
export const DEFAULTS = {
  CAMERA_POSITION: [0, 5, 10] as const,
  CAMERA_TARGET: [0, 0, 0] as const,
  CAMERA_FOV: 75,
  CAMERA_NEAR: 0.1,
  CAMERA_FAR: 1000,
  GRID_SIZE: 10,
  GRID_DIVISIONS: 10,
  SNAP_INCREMENT: 1,
  ROTATION_SNAP: 15,
  SCALE_SNAP: 0.1
} as const

/**
 * 限制常量
 */
export const LIMITS = {
  MAX_ENTITIES: 10000,
  MAX_TEXTURE_SIZE: 4096,
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
  MAX_HISTORY_ITEMS: 100,
  MAX_RECENT_PROJECTS: 10
} as const

/**
 * 颜色常量
 */
export const COLORS = {
  X_AXIS: '#ff0000',
  Y_AXIS: '#00ff00',
  Z_AXIS: '#0000ff',
  SELECTION: '#ffff00',
  HOVER: '#ff8800',
  GRID: '#888888',
  GRID_CENTER: '#444444'
} as const

/**
 * 图标常量
 */
export const ICONS = {
  FOLDER: '📁',
  MODEL: '🎲',
  TEXTURE: '🖼️',
  MATERIAL: '🎨',
  AUDIO: '🎵',
  VIDEO: '🎬',
  SCRIPT: '📜',
  SCENE: '🌍',
  CAMERA: '📷',
  LIGHT: '💡',
  MESH: '📦',
  GROUP: '📁'
} as const
