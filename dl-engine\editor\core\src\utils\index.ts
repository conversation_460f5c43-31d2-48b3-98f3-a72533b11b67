/**
 * 编辑器工具函数
 */

import { Vector3, Quaternion, Euler } from 'three'

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`
}

/**
 * 格式化时间
 */
export function formatTime(date: Date): string {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/**
 * 生成唯一ID
 */
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

/**
 * 深拷贝对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as any
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as any
  if (typeof obj === 'object') {
    const clonedObj = {} as any
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  return obj
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 检查文件类型
 */
export function getFileType(filename: string): string {
  const ext = filename.toLowerCase().split('.').pop() || ''
  
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'tga', 'hdr', 'exr']
  const modelExts = ['gltf', 'glb', 'fbx', 'obj', 'dae', '3ds', 'ply']
  const audioExts = ['mp3', 'wav', 'ogg', 'm4a', 'aac', 'flac']
  const videoExts = ['mp4', 'webm', 'ogv', 'avi', 'mov', 'wmv']
  const scriptExts = ['js', 'ts', 'json', 'txt', 'md']
  
  if (imageExts.includes(ext)) return 'texture'
  if (modelExts.includes(ext)) return 'model'
  if (audioExts.includes(ext)) return 'audio'
  if (videoExts.includes(ext)) return 'video'
  if (scriptExts.includes(ext)) return 'script'
  if (ext === 'mat') return 'material'
  if (ext === 'scene') return 'scene'
  
  return 'unknown'
}

/**
 * 向量工具函数
 */
export const VectorUtils = {
  /**
   * 向量转数组
   */
  toArray(vector: Vector3): [number, number, number] {
    return [vector.x, vector.y, vector.z]
  },
  
  /**
   * 数组转向量
   */
  fromArray(array: [number, number, number]): Vector3 {
    return new Vector3(array[0], array[1], array[2])
  },
  
  /**
   * 向量格式化显示
   */
  format(vector: Vector3, precision: number = 2): string {
    return `(${vector.x.toFixed(precision)}, ${vector.y.toFixed(precision)}, ${vector.z.toFixed(precision)})`
  }
}

/**
 * 四元数工具函数
 */
export const QuaternionUtils = {
  /**
   * 四元数转欧拉角
   */
  toEuler(quaternion: Quaternion): Euler {
    const euler = new Euler()
    euler.setFromQuaternion(quaternion)
    return euler
  },
  
  /**
   * 欧拉角转四元数
   */
  fromEuler(euler: Euler): Quaternion {
    const quaternion = new Quaternion()
    quaternion.setFromEuler(euler)
    return quaternion
  },
  
  /**
   * 四元数格式化显示
   */
  format(quaternion: Quaternion, precision: number = 2): string {
    return `(${quaternion.x.toFixed(precision)}, ${quaternion.y.toFixed(precision)}, ${quaternion.z.toFixed(precision)}, ${quaternion.w.toFixed(precision)})`
  }
}

/**
 * 数学工具函数
 */
export const MathUtils = {
  /**
   * 角度转弧度
   */
  degToRad(degrees: number): number {
    return degrees * (Math.PI / 180)
  },
  
  /**
   * 弧度转角度
   */
  radToDeg(radians: number): number {
    return radians * (180 / Math.PI)
  },
  
  /**
   * 限制数值范围
   */
  clamp(value: number, min: number, max: number): number {
    return Math.min(Math.max(value, min), max)
  },
  
  /**
   * 线性插值
   */
  lerp(a: number, b: number, t: number): number {
    return a + (b - a) * t
  },
  
  /**
   * 捕捉到网格
   */
  snapToGrid(value: number, gridSize: number): number {
    return Math.round(value / gridSize) * gridSize
  }
}

/**
 * 颜色工具函数
 */
export const ColorUtils = {
  /**
   * 十六进制转RGB
   */
  hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null
  },
  
  /**
   * RGB转十六进制
   */
  rgbToHex(r: number, g: number, b: number): string {
    return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`
  }
}

/**
 * 本地存储工具函数
 */
export const StorageUtils = {
  /**
   * 设置本地存储
   */
  set(key: string, value: any): void {
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.warn('Failed to save to localStorage:', error)
    }
  },
  
  /**
   * 获取本地存储
   */
  get<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue || null
    } catch (error) {
      console.warn('Failed to read from localStorage:', error)
      return defaultValue || null
    }
  },
  
  /**
   * 删除本地存储
   */
  remove(key: string): void {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.warn('Failed to remove from localStorage:', error)
    }
  }
}
