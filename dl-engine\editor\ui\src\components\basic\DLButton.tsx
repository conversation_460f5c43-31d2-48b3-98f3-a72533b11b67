/**
 * DL-Engine 按钮组件
 * 
 * 基于 Ant Design Button 的增强版本，支持编辑器特定功能
 */

import React from 'react'
import { Button, ButtonProps } from 'antd'
import { useTranslation } from 'react-i18next'
import classNames from 'classnames'
import DLIcon from './DLIcon'
import DLTooltip from './DLTooltip'

/**
 * DL按钮属性
 */
export interface DLButtonProps extends Omit<ButtonProps, 'icon'> {
  /** 图标名称或React节点 */
  icon?: string | React.ReactNode
  /** 工具提示文本 */
  tooltip?: string
  /** 工具提示位置 */
  tooltipPlacement?: 'top' | 'bottom' | 'left' | 'right'
  /** 是否为编辑器工具按钮 */
  tool?: boolean
  /** 是否为活跃状态（用于工具按钮） */
  active?: boolean
  /** 快捷键提示 */
  shortcut?: string
  /** 是否显示加载状态 */
  loading?: boolean
  /** 按钮变体 */
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'ghost'
}

/**
 * DL按钮组件
 */
const DLButton: React.FC<DLButtonProps> = ({
  icon,
  tooltip,
  tooltipPlacement = 'top',
  tool = false,
  active = false,
  shortcut,
  loading = false,
  variant,
  className,
  children,
  ...props
}) => {
  const { t } = useTranslation()
  
  // 处理图标
  const renderIcon = () => {
    if (!icon) return undefined
    if (typeof icon === 'string') {
      return <DLIcon name={icon} />
    }
    return icon
  }
  
  // 处理按钮类型
  const getButtonType = (): ButtonProps['type'] => {
    if (variant === 'primary') return 'primary'
    if (variant === 'danger') return 'primary'
    if (variant === 'ghost') return 'text'
    return 'default'
  }
  
  // 处理按钮样式类
  const buttonClassName = classNames(
    'dl-button',
    {
      'dl-button--tool': tool,
      'dl-button--active': active,
      'dl-button--success': variant === 'success',
      'dl-button--warning': variant === 'warning',
      'dl-button--danger': variant === 'danger',
      'dl-button--secondary': variant === 'secondary'
    },
    className
  )
  
  // 构建工具提示内容
  const tooltipContent = tooltip ? (
    <div>
      <div>{tooltip}</div>
      {shortcut && (
        <div className="dl-tooltip-shortcut">
          {shortcut}
        </div>
      )}
    </div>
  ) : undefined
  
  // 按钮元素
  const buttonElement = (
    <Button
      {...props}
      type={getButtonType()}
      icon={renderIcon()}
      loading={loading}
      className={buttonClassName}
      data-active={active}
      data-tool={tool}
    >
      {children}
    </Button>
  )
  
  // 如果有工具提示，包装在 Tooltip 中
  if (tooltipContent) {
    return (
      <DLTooltip
        title={tooltipContent}
        placement={tooltipPlacement}
      >
        {buttonElement}
      </DLTooltip>
    )
  }
  
  return buttonElement
}

export default DLButton
