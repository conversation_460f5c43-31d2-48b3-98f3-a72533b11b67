/**
 * DL-Engine 教育组件测试
 * 测试所有教育场景专用组件和学习分析功能
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { Engine } from '../../Engine'
import { 
  KnowledgePointComponent, 
  KnowledgePointUtils,
  KnowledgePointType,
  DifficultyLevel,
  KnowledgeStatus 
} from '../KnowledgePointComponent'
import { 
  LearningAnalyticsComponent, 
  LearningAnalyticsUtils,
  LearningBehaviorType 
} from '../LearningAnalyticsComponent'
import { 
  LearningPathComponent, 
  LearningPathUtils,
  PathNodeType,
  PathNodeStatus,
  AdaptationStrategy 
} from '../LearningPathComponent'
import { 
  GamificationComponent, 
  GamificationUtils,
  AchievementType,
  BadgeRarity 
} from '../GamificationComponent'
import { 
  AdaptiveLearningComponent, 
  AdaptiveLearningUtils 
} from '../AdaptiveLearningComponent'
import { EducationSystemManager } from '../EducationSystemManager'

describe('教育组件系统测试', () => {
  let engine: Engine
  let educationManager: EducationSystemManager

  beforeEach(() => {
    engine = new Engine()
    educationManager = EducationSystemManager.getInstance({
      enableAnalytics: true,
      enableAdaptiveLearning: true,
      enableGamification: true,
      enableCollaboration: true,
      enableVirtualLab: true,
      analyticsUpdateFrequency: 5,
      adaptiveCheckFrequency: 10,
      gamificationUpdateFrequency: 1
    })
    educationManager.initialize()
  })

  describe('知识点组件测试', () => {
    it('应该能够创建和管理知识点', () => {
      const entity = engine.store.createEntity()
      
      entity.setComponent(KnowledgePointComponent, {
        id: 'kp_001',
        title: '函数基础',
        description: '学习JavaScript函数的基本概念',
        type: KnowledgePointType.CONCEPT,
        difficulty: DifficultyLevel.BEGINNER,
        subject: '计算机科学',
        chapterId: 'chapter_01',
        courseId: 'course_js',
        status: KnowledgeStatus.NOT_LEARNED,
        masteryLevel: 0,
        studyTime: 0,
        reviewCount: 0,
        relations: [],
        resources: [],
        objectives: [],
        keywords: ['函数', 'JavaScript', '编程'],
        notes: [],
        commonMistakes: [],
        hints: [],
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      })

      const knowledge = entity.getComponent(KnowledgePointComponent)
      expect(knowledge).toBeDefined()
      expect(knowledge!.title).toBe('函数基础')
      expect(knowledge!.type).toBe(KnowledgePointType.CONCEPT)
      expect(knowledge!.difficulty).toBe(DifficultyLevel.BEGINNER)
    })

    it('应该能够更新掌握程度', () => {
      const entity = engine.store.createEntity()
      entity.setComponent(KnowledgePointComponent, {
        id: 'kp_002',
        title: '测试知识点',
        description: '测试描述',
        type: KnowledgePointType.CONCEPT,
        difficulty: DifficultyLevel.BEGINNER,
        subject: '测试',
        chapterId: 'test',
        courseId: 'test',
        status: KnowledgeStatus.NOT_LEARNED,
        masteryLevel: 0,
        studyTime: 0,
        reviewCount: 0,
        relations: [],
        resources: [],
        objectives: [],
        keywords: [],
        notes: [],
        commonMistakes: [],
        hints: [],
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      })

      const knowledge = entity.getComponent(KnowledgePointComponent)!
      
      KnowledgePointUtils.updateMasteryLevel(knowledge, 85)
      
      expect(knowledge.masteryLevel).toBe(85)
      expect(knowledge.status).toBe(KnowledgeStatus.UNDERSTOOD)
    })

    it('应该能够计算复习间隔', () => {
      const entity = engine.store.createEntity()
      entity.setComponent(KnowledgePointComponent, {
        id: 'kp_003',
        title: '复习测试',
        description: '测试复习功能',
        type: KnowledgePointType.CONCEPT,
        difficulty: DifficultyLevel.INTERMEDIATE,
        subject: '测试',
        chapterId: 'test',
        courseId: 'test',
        status: KnowledgeStatus.UNDERSTOOD,
        masteryLevel: 80,
        studyTime: 30,
        reviewCount: 2,
        relations: [],
        resources: [],
        objectives: [],
        keywords: [],
        notes: [],
        commonMistakes: [],
        hints: [],
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      })

      const knowledge = entity.getComponent(KnowledgePointComponent)!
      const interval = KnowledgePointUtils.calculateReviewInterval(knowledge)
      
      expect(interval).toBeGreaterThan(0)
      expect(typeof interval).toBe('number')
    })
  })

  describe('学习分析组件测试', () => {
    it('应该能够记录学习行为', () => {
      const entity = engine.store.createEntity()
      
      entity.setComponent(LearningAnalyticsComponent, {
        learnerId: 'learner_001',
        courseId: 'course_001',
        analysisStartTime: new Date(),
        lastUpdateTime: new Date(),
        behaviors: [],
        patterns: [],
        recommendations: [],
        statistics: {
          totalStudyTime: 0,
          activeDays: 0,
          completedTasks: 0,
          averageScore: 0,
          learningFrequency: 0,
          averageSessionDuration: 0,
          knowledgeMasteryRate: 0,
          errorRate: 0,
          helpSeekingFrequency: 0,
          collaborationLevel: 0
        },
        predictions: [],
        learningStyleAnalysis: { visual: 25, auditory: 25, kinesthetic: 25, reading: 25 },
        cognitiveLoadAnalysis: { intrinsic: 0, extraneous: 0, germane: 0 },
        emotionalStateAnalysis: { engagement: 50, frustration: 0, confidence: 50, anxiety: 0 },
        socialNetworkAnalysis: { connections: [], centrality: 0, clustering: 0 }
      })

      const analytics = entity.getComponent(LearningAnalyticsComponent)!
      
      LearningAnalyticsUtils.recordBehavior(analytics, {
        id: 'behavior_001',
        learnerId: 'learner_001',
        type: LearningBehaviorType.VIEW_CONTENT,
        timestamp: new Date(),
        contentId: 'content_001',
        duration: 300,
        result: 'success',
        score: 85,
        context: {
          deviceType: 'desktop',
          browserType: 'chrome',
          screenResolution: '1920x1080'
        },
        metadata: {}
      })

      expect(analytics.behaviors).toHaveLength(1)
      expect(analytics.behaviors[0].type).toBe(LearningBehaviorType.VIEW_CONTENT)
      expect(analytics.behaviors[0].duration).toBe(300)
    })

    it('应该能够生成学习建议', () => {
      const entity = engine.store.createEntity()
      
      entity.setComponent(LearningAnalyticsComponent, {
        learnerId: 'learner_002',
        courseId: 'course_002',
        analysisStartTime: new Date(),
        lastUpdateTime: new Date(),
        behaviors: [],
        patterns: [],
        recommendations: [],
        statistics: {
          totalStudyTime: 120,
          activeDays: 5,
          completedTasks: 10,
          averageScore: 45, // 低分数，应该触发建议
          learningFrequency: 3,
          averageSessionDuration: 10, // 短时间，应该触发建议
          knowledgeMasteryRate: 0.3,
          errorRate: 0.4, // 高错误率，应该触发建议
          helpSeekingFrequency: 2,
          collaborationLevel: 0.2
        },
        predictions: [],
        learningStyleAnalysis: { visual: 25, auditory: 25, kinesthetic: 25, reading: 25 },
        cognitiveLoadAnalysis: { intrinsic: 0, extraneous: 0, germane: 0 },
        emotionalStateAnalysis: { engagement: 50, frustration: 0, confidence: 50, anxiety: 0 },
        socialNetworkAnalysis: { connections: [], centrality: 0, clustering: 0 }
      })

      const analytics = entity.getComponent(LearningAnalyticsComponent)!
      const recommendations = LearningAnalyticsUtils.generateRecommendations(analytics)

      expect(recommendations).toHaveLength(2) // 应该生成2个建议
      expect(recommendations.some(r => r.type === 'strategy')).toBe(true)
      expect(recommendations.some(r => r.type === 'pace')).toBe(true)
    })
  })

  describe('游戏化组件测试', () => {
    it('应该能够添加经验值和升级', () => {
      const entity = engine.store.createEntity()
      
      entity.setComponent(GamificationComponent, {
        learnerId: 'learner_003',
        courseId: 'course_003',
        levelSystem: {
          currentLevel: 1,
          currentXP: 0,
          currentLevelXP: 0,
          nextLevelXP: 100,
          levelProgress: 0,
          levelTitle: '新手学习者',
          levelIcon: 'novice.png',
          levelPerks: []
        },
        achievements: [],
        quests: [],
        statistics: {
          totalPoints: 0,
          todayPoints: 0,
          weekPoints: 0,
          monthPoints: 0,
          streakDays: 0,
          maxStreakDays: 0,
          achievementsUnlocked: 0,
          questsCompleted: 0,
          totalStudyTime: 0,
          averageDailyTime: 0,
          efficiencyScore: 0
        },
        leaderboard: [],
        inventory: [],
        settings: {
          enableNotifications: true,
          enableSounds: true,
          enableAnimations: true,
          showProgress: true,
          shareAchievements: false,
          competitiveMode: true
        },
        dailyCheckin: {
          lastCheckinDate: undefined,
          consecutiveDays: 0,
          totalCheckins: 0,
          checkinRewards: []
        },
        activityHistory: [],
        createdAt: new Date(),
        updatedAt: new Date()
      })

      const gamification = entity.getComponent(GamificationComponent)!
      
      // 添加足够的经验值来升级
      GamificationUtils.addExperience(gamification, 150, '完成课程')

      expect(gamification.levelSystem.currentLevel).toBe(2)
      expect(gamification.levelSystem.currentXP).toBe(150)
      expect(gamification.statistics.totalPoints).toBe(150)
    })

    it('应该能够解锁成就', () => {
      const entity = engine.store.createEntity()
      
      entity.setComponent(GamificationComponent, {
        learnerId: 'learner_004',
        courseId: 'course_004',
        levelSystem: {
          currentLevel: 1,
          currentXP: 0,
          currentLevelXP: 0,
          nextLevelXP: 100,
          levelProgress: 0,
          levelTitle: '新手学习者',
          levelIcon: 'novice.png',
          levelPerks: []
        },
        achievements: [{
          id: 'test_achievement',
          name: '测试成就',
          description: '测试成就描述',
          type: AchievementType.PROGRESS,
          iconUrl: 'test.png',
          points: 50,
          rarity: BadgeRarity.COMMON,
          unlockConditions: [
            { type: 'count', target: 1, current: 1, description: '完成1个任务' }
          ],
          unlocked: false,
          progress: 100,
          hidden: false,
          prerequisites: []
        }],
        quests: [],
        statistics: {
          totalPoints: 0,
          todayPoints: 0,
          weekPoints: 0,
          monthPoints: 0,
          streakDays: 0,
          maxStreakDays: 0,
          achievementsUnlocked: 0,
          questsCompleted: 0,
          totalStudyTime: 0,
          averageDailyTime: 0,
          efficiencyScore: 0
        },
        leaderboard: [],
        inventory: [],
        settings: {
          enableNotifications: true,
          enableSounds: true,
          enableAnimations: true,
          showProgress: true,
          shareAchievements: false,
          competitiveMode: true
        },
        dailyCheckin: {
          lastCheckinDate: undefined,
          consecutiveDays: 0,
          totalCheckins: 0,
          checkinRewards: []
        },
        activityHistory: [],
        createdAt: new Date(),
        updatedAt: new Date()
      })

      const gamification = entity.getComponent(GamificationComponent)!
      
      const unlocked = GamificationUtils.unlockAchievement(gamification, 'test_achievement')

      expect(unlocked).toBe(true)
      expect(gamification.achievements[0].unlocked).toBe(true)
      expect(gamification.statistics.achievementsUnlocked).toBe(1)
    })
  })

  describe('教育系统管理器测试', () => {
    it('应该能够开始和结束学习会话', () => {
      const sessionId = educationManager.startLearningSession('learner_005', 'course_005')
      
      expect(sessionId).toBeDefined()
      expect(sessionId).toContain('session_')
      expect(sessionId).toContain('learner_005')

      // 记录一些学习活动
      educationManager.recordLearningActivity(sessionId, 'knowledge_point_visited', {
        knowledgePointId: 'kp_001',
        duration: 300
      })

      educationManager.recordLearningActivity(sessionId, 'task_completed', {
        taskId: 'task_001',
        score: 85,
        duration: 600
      })

      // 结束会话
      educationManager.endLearningSession(sessionId)

      // 验证学习者实体已创建并包含相关组件
      const learnerEntity = engine.store.getEntityByName('learner_learner_005')
      expect(learnerEntity).toBeDefined()
    })

    it('应该能够生成学习者报告', () => {
      const sessionId = educationManager.startLearningSession('learner_006', 'course_006')
      
      // 记录一些活动
      educationManager.recordLearningActivity(sessionId, 'task_completed', {
        taskId: 'task_001',
        score: 90
      })

      educationManager.endLearningSession(sessionId)

      const report = educationManager.getLearnerReport('learner_006')
      
      expect(report).toBeDefined()
      expect(report.learnerId).toBe('learner_006')
      expect(report.progress).toBeDefined()
      expect(report.analytics).toBeDefined()
      expect(report.gamification).toBeDefined()
    })
  })
})
