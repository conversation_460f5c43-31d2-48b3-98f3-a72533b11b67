/**
 * DL-Engine 学习路径组件
 * 管理个性化学习路径和自适应学习序列
 */

import { defineComponent } from '../ComponentFunctions'

/**
 * 学习路径节点类型
 */
export enum PathNodeType {
  CONTENT = 'content',           // 内容节点
  ASSESSMENT = 'assessment',     // 评估节点
  PRACTICE = 'practice',         // 练习节点
  REVIEW = 'review',            // 复习节点
  MILESTONE = 'milestone',       // 里程碑节点
  BRANCH = 'branch',            // 分支节点
  MERGE = 'merge'               // 合并节点
}

/**
 * 学习路径节点状态
 */
export enum PathNodeStatus {
  LOCKED = 'locked',             // 锁定
  AVAILABLE = 'available',       // 可用
  IN_PROGRESS = 'in_progress',   // 进行中
  COMPLETED = 'completed',       // 已完成
  SKIPPED = 'skipped',          // 已跳过
  FAILED = 'failed'             // 失败
}

/**
 * 路径适应策略
 */
export enum AdaptationStrategy {
  PERFORMANCE_BASED = 'performance_based',     // 基于表现
  TIME_BASED = 'time_based',                  // 基于时间
  PREFERENCE_BASED = 'preference_based',       // 基于偏好
  DIFFICULTY_BASED = 'difficulty_based',       // 基于难度
  COLLABORATIVE = 'collaborative',             // 协作式
  MASTERY_BASED = 'mastery_based'             // 基于掌握程度
}

/**
 * 学习路径节点
 */
export interface LearningPathNode {
  /** 节点ID */
  id: string
  
  /** 节点标题 */
  title: string
  
  /** 节点描述 */
  description: string
  
  /** 节点类型 */
  type: PathNodeType
  
  /** 节点状态 */
  status: PathNodeStatus
  
  /** 关联的内容ID */
  contentId?: string
  
  /** 关联的知识点ID */
  knowledgePointId?: string
  
  /** 预计学习时间（分钟） */
  estimatedDuration: number
  
  /** 实际学习时间（分钟） */
  actualDuration: number
  
  /** 难度级别 (1-5) */
  difficulty: number
  
  /** 重要性权重 (0-1) */
  importance: number
  
  /** 前置条件节点ID列表 */
  prerequisites: string[]
  
  /** 后续节点ID列表 */
  successors: string[]
  
  /** 可选的替代节点ID列表 */
  alternatives: string[]
  
  /** 节点位置坐标 */
  position: {
    x: number
    y: number
    z?: number
  }
  
  /** 完成条件 */
  completionCriteria: {
    minScore?: number
    minTime?: number
    maxTime?: number
    requiredActions?: string[]
  }
  
  /** 节点元数据 */
  metadata: Record<string, any>
  
  /** 开始时间 */
  startTime?: Date
  
  /** 完成时间 */
  completionTime?: Date
  
  /** 最后访问时间 */
  lastAccessTime?: Date
}

/**
 * 学习路径分支条件
 */
export interface PathBranchCondition {
  /** 条件ID */
  id: string
  
  /** 条件类型 */
  type: 'score' | 'time' | 'attempts' | 'preference' | 'random' | 'custom'
  
  /** 条件表达式 */
  expression: string
  
  /** 目标节点ID */
  targetNodeId: string
  
  /** 条件权重 */
  weight: number
  
  /** 条件描述 */
  description: string
}

/**
 * 学习路径统计
 */
export interface PathStatistics {
  /** 总节点数 */
  totalNodes: number
  
  /** 已完成节点数 */
  completedNodes: number
  
  /** 当前节点ID */
  currentNodeId?: string
  
  /** 完成百分比 */
  completionPercentage: number
  
  /** 总预计时间（分钟） */
  totalEstimatedTime: number
  
  /** 总实际时间（分钟） */
  totalActualTime: number
  
  /** 平均得分 */
  averageScore: number
  
  /** 路径效率 (实际时间/预计时间) */
  efficiency: number
  
  /** 跳过的节点数 */
  skippedNodes: number
  
  /** 失败的节点数 */
  failedNodes: number
}

/**
 * 学习路径数据
 */
export interface LearningPathData {
  /** 路径ID */
  id: string
  
  /** 路径名称 */
  name: string
  
  /** 路径描述 */
  description: string
  
  /** 学习者ID */
  learnerId: string
  
  /** 课程ID */
  courseId: string
  
  /** 路径节点 */
  nodes: LearningPathNode[]
  
  /** 分支条件 */
  branchConditions: PathBranchCondition[]
  
  /** 适应策略 */
  adaptationStrategy: AdaptationStrategy
  
  /** 路径统计 */
  statistics: PathStatistics
  
  /** 是否启用自适应 */
  adaptiveEnabled: boolean
  
  /** 自适应参数 */
  adaptiveParameters: {
    difficultyAdjustmentRate: number
    timeAdjustmentRate: number
    performanceThreshold: number
    adaptationFrequency: number
  }
  
  /** 路径版本 */
  version: string
  
  /** 创建时间 */
  createdAt: Date
  
  /** 更新时间 */
  updatedAt: Date
  
  /** 开始时间 */
  startedAt?: Date
  
  /** 完成时间 */
  completedAt?: Date
  
  /** 是否激活 */
  active: boolean
}

/**
 * 学习路径组件
 */
export const LearningPathComponent = defineComponent({
  name: 'LearningPath',
  schema: {
    id: '',
    name: '',
    description: '',
    learnerId: '',
    courseId: '',
    nodes: [] as LearningPathNode[],
    branchConditions: [] as PathBranchCondition[],
    adaptationStrategy: AdaptationStrategy.PERFORMANCE_BASED,
    statistics: {
      totalNodes: 0,
      completedNodes: 0,
      currentNodeId: undefined,
      completionPercentage: 0,
      totalEstimatedTime: 0,
      totalActualTime: 0,
      averageScore: 0,
      efficiency: 1,
      skippedNodes: 0,
      failedNodes: 0
    } as PathStatistics,
    adaptiveEnabled: true,
    adaptiveParameters: {
      difficultyAdjustmentRate: 0.1,
      timeAdjustmentRate: 0.2,
      performanceThreshold: 0.7,
      adaptationFrequency: 5
    },
    version: '1.0.0',
    createdAt: new Date(),
    updatedAt: new Date(),
    startedAt: undefined as Date | undefined,
    completedAt: undefined as Date | undefined,
    active: false
  } as LearningPathData,
  
  onAdd: (entity, component) => {
    console.log(`Learning path created: ${component.name}`)
    component.createdAt = new Date()
    component.updatedAt = new Date()
  },
  
  onRemove: (entity, component) => {
    console.log(`Learning path removed: ${component.name}`)
  },
  
  onSet: (entity, component) => {
    component.updatedAt = new Date()
    
    // 更新统计信息
    LearningPathUtils.updateStatistics(component)
  }
})

/**
 * 学习路径工具函数
 */
export const LearningPathUtils = {
  /**
   * 开始学习路径
   */
  startPath: (path: LearningPathData): void => {
    path.active = true
    path.startedAt = new Date()
    path.updatedAt = new Date()
    
    // 解锁第一个节点
    const firstNode = path.nodes.find(node => node.prerequisites.length === 0)
    if (firstNode) {
      firstNode.status = PathNodeStatus.AVAILABLE
      path.statistics.currentNodeId = firstNode.id
    }
    
    console.log(`Learning path started: ${path.name}`)
  },
  
  /**
   * 完成节点
   */
  completeNode: (path: LearningPathData, nodeId: string, score?: number): void => {
    const node = path.nodes.find(n => n.id === nodeId)
    if (!node) return
    
    node.status = PathNodeStatus.COMPLETED
    node.completionTime = new Date()
    
    if (score !== undefined) {
      node.metadata.score = score
    }
    
    // 解锁后续节点
    LearningPathUtils.unlockSuccessors(path, nodeId)
    
    // 更新当前节点
    LearningPathUtils.updateCurrentNode(path)
    
    // 检查路径是否完成
    LearningPathUtils.checkPathCompletion(path)
    
    path.updatedAt = new Date()
  },
  
  /**
   * 解锁后续节点
   */
  unlockSuccessors: (path: LearningPathData, completedNodeId: string): void => {
    const completedNode = path.nodes.find(n => n.id === completedNodeId)
    if (!completedNode) return
    
    completedNode.successors.forEach(successorId => {
      const successor = path.nodes.find(n => n.id === successorId)
      if (!successor) return
      
      // 检查所有前置条件是否满足
      const allPrerequisitesMet = successor.prerequisites.every(prereqId => {
        const prereq = path.nodes.find(n => n.id === prereqId)
        return prereq?.status === PathNodeStatus.COMPLETED
      })
      
      if (allPrerequisitesMet && successor.status === PathNodeStatus.LOCKED) {
        successor.status = PathNodeStatus.AVAILABLE
      }
    })
  },
  
  /**
   * 更新当前节点
   */
  updateCurrentNode: (path: LearningPathData): void => {
    // 找到第一个可用的节点作为当前节点
    const availableNode = path.nodes.find(n => n.status === PathNodeStatus.AVAILABLE)
    if (availableNode) {
      path.statistics.currentNodeId = availableNode.id
    } else {
      // 如果没有可用节点，检查是否有进行中的节点
      const inProgressNode = path.nodes.find(n => n.status === PathNodeStatus.IN_PROGRESS)
      if (inProgressNode) {
        path.statistics.currentNodeId = inProgressNode.id
      } else {
        path.statistics.currentNodeId = undefined
      }
    }
  },
  
  /**
   * 检查路径完成
   */
  checkPathCompletion: (path: LearningPathData): void => {
    const completedNodes = path.nodes.filter(n => n.status === PathNodeStatus.COMPLETED)
    const totalNodes = path.nodes.length
    
    if (completedNodes.length === totalNodes) {
      path.completedAt = new Date()
      path.active = false
      console.log(`Learning path completed: ${path.name}`)
    }
  },
  
  /**
   * 更新统计信息
   */
  updateStatistics: (path: LearningPathData): void => {
    const stats = path.statistics
    
    stats.totalNodes = path.nodes.length
    stats.completedNodes = path.nodes.filter(n => n.status === PathNodeStatus.COMPLETED).length
    stats.skippedNodes = path.nodes.filter(n => n.status === PathNodeStatus.SKIPPED).length
    stats.failedNodes = path.nodes.filter(n => n.status === PathNodeStatus.FAILED).length
    
    stats.completionPercentage = stats.totalNodes > 0 
      ? Math.round((stats.completedNodes / stats.totalNodes) * 100) 
      : 0
    
    stats.totalEstimatedTime = path.nodes.reduce((sum, node) => sum + node.estimatedDuration, 0)
    stats.totalActualTime = path.nodes.reduce((sum, node) => sum + node.actualDuration, 0)
    
    stats.efficiency = stats.totalEstimatedTime > 0 
      ? stats.totalActualTime / stats.totalEstimatedTime 
      : 1
    
    // 计算平均得分
    const scoredNodes = path.nodes.filter(n => n.metadata.score !== undefined)
    if (scoredNodes.length > 0) {
      stats.averageScore = scoredNodes.reduce((sum, node) => sum + (node.metadata.score || 0), 0) / scoredNodes.length
    }
  },
  
  /**
   * 自适应调整路径
   */
  adaptPath: (path: LearningPathData): void => {
    if (!path.adaptiveEnabled) return
    
    const { adaptiveParameters } = path
    const { statistics } = path
    
    // 基于表现调整难度
    if (statistics.averageScore < adaptiveParameters.performanceThreshold) {
      LearningPathUtils.adjustDifficulty(path, -adaptiveParameters.difficultyAdjustmentRate)
    } else if (statistics.averageScore > 0.9) {
      LearningPathUtils.adjustDifficulty(path, adaptiveParameters.difficultyAdjustmentRate)
    }
    
    // 基于效率调整时间估计
    if (statistics.efficiency > 1.2) {
      LearningPathUtils.adjustTimeEstimates(path, adaptiveParameters.timeAdjustmentRate)
    } else if (statistics.efficiency < 0.8) {
      LearningPathUtils.adjustTimeEstimates(path, -adaptiveParameters.timeAdjustmentRate)
    }
    
    path.updatedAt = new Date()
  },
  
  /**
   * 调整难度
   */
  adjustDifficulty: (path: LearningPathData, adjustment: number): void => {
    path.nodes.forEach(node => {
      if (node.status === PathNodeStatus.AVAILABLE || node.status === PathNodeStatus.LOCKED) {
        node.difficulty = Math.max(1, Math.min(5, node.difficulty + adjustment))
      }
    })
  },
  
  /**
   * 调整时间估计
   */
  adjustTimeEstimates: (path: LearningPathData, adjustment: number): void => {
    path.nodes.forEach(node => {
      if (node.status === PathNodeStatus.AVAILABLE || node.status === PathNodeStatus.LOCKED) {
        node.estimatedDuration = Math.max(5, node.estimatedDuration * (1 + adjustment))
      }
    })
  },
  
  /**
   * 获取推荐的下一个节点
   */
  getRecommendedNextNode: (path: LearningPathData): LearningPathNode | null => {
    const availableNodes = path.nodes.filter(n => n.status === PathNodeStatus.AVAILABLE)
    
    if (availableNodes.length === 0) return null
    if (availableNodes.length === 1) return availableNodes[0]
    
    // 根据重要性和难度排序
    availableNodes.sort((a, b) => {
      const scoreA = a.importance * 0.7 + (6 - a.difficulty) * 0.3
      const scoreB = b.importance * 0.7 + (6 - b.difficulty) * 0.3
      return scoreB - scoreA
    })
    
    return availableNodes[0]
  },
  
  /**
   * 生成路径可视化数据
   */
  generateVisualizationData: (path: LearningPathData) => {
    return {
      nodes: path.nodes.map(node => ({
        id: node.id,
        title: node.title,
        type: node.type,
        status: node.status,
        position: node.position,
        difficulty: node.difficulty,
        importance: node.importance
      })),
      edges: path.nodes.flatMap(node => 
        node.successors.map(successorId => ({
          source: node.id,
          target: successorId,
          type: 'successor'
        }))
      ),
      statistics: path.statistics
    }
  }
}
