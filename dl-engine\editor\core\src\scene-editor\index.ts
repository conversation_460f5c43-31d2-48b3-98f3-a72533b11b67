/**
 * 场景编辑器模块
 * 
 * 包含3D场景编辑的核心功能：
 * - 3D视口 (Viewport)
 * - 操作工具 (Gizmos)
 * - 选择系统 (Selection)
 * - 相机控制 (Camera Control)
 */

// 视口组件
export { default as Viewport } from './components/Viewport'
export { default as ViewportControls } from './components/ViewportControls'
export { default as ViewportGrid } from './components/ViewportGrid'

// 操作工具
export { default as TransformGizmo } from './gizmos/TransformGizmo'
export { default as MoveGizmo } from './gizmos/MoveGizmo'
export { default as RotateGizmo } from './gizmos/RotateGizmo'
export { default as ScaleGizmo } from './gizmos/ScaleGizmo'

// 选择系统
export { default as SelectionSystem } from './selection/SelectionSystem'
export { default as SelectionBox } from './selection/SelectionBox'
export { default as SelectionOutline } from './selection/SelectionOutline'

// 相机控制
export { default as CameraController } from './camera/CameraController'
export { default as OrbitControls } from './camera/OrbitControls'
export { default as FlyControls } from './camera/FlyControls'

// 场景编辑器主组件
export { default as SceneEditor } from './components/SceneEditor'

// 工具函数
export * from './utils'

// 类型定义
export * from './types'
