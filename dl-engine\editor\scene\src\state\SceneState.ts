/**
 * 场景编辑器状态管理
 * 
 * 使用 Hookstate 管理场景编辑器的全局状态
 */

import { hookstate, State } from '@hookstate/core'
import { Vector3, Quaternion, Color } from 'three'
import { 
  EditorMode, 
  ViewportType, 
  SceneObject, 
  SceneConfig, 
  SelectionInfo, 
  ViewportConfig, 
  EditorSettings, 
  HistoryEntry 
} from '../types'

/**
 * 场景编辑器状态接口
 */
export interface SceneStateType {
  /** 当前编辑器模式 */
  mode: EditorMode
  
  /** 视口类型 */
  viewportType: ViewportType
  
  /** 场景对象列表 */
  objects: SceneObject[]
  
  /** 选择信息 */
  selection: SelectionInfo
  
  /** 场景配置 */
  sceneConfig: SceneConfig
  
  /** 视口配置 */
  viewportConfig: ViewportConfig
  
  /** 编辑器设置 */
  settings: EditorSettings
  
  /** 操作历史 */
  history: {
    undoStack: HistoryEntry[]
    redoStack: HistoryEntry[]
    maxSize: number
  }
  
  /** UI状态 */
  ui: {
    showHierarchy: boolean
    showInspector: boolean
    showLightingPanel: boolean
    showMaterialEditor: boolean
    showTerrainEditor: boolean
    showPhysicsPanel: boolean
    showAnimationTimeline: boolean
    hierarchyWidth: number
    inspectorWidth: number
  }
  
  /** 加载状态 */
  loading: {
    isLoading: boolean
    message: string | null
  }
  
  /** 错误状态 */
  error: {
    hasError: boolean
    message: string | null
    details: any
  }
  
  /** 性能统计 */
  stats: {
    fps: number
    frameTime: number
    triangles: number
    drawCalls: number
    memoryUsage: number
  }
}

/**
 * 默认场景配置
 */
const defaultSceneConfig: SceneConfig = {
  backgroundType: 'color',
  backgroundColor: new Color(0x222222),
  fog: {
    enabled: false,
    type: 'linear',
    color: new Color(0xffffff),
    near: 1,
    far: 1000
  },
  globalIllumination: {
    enabled: true,
    ambientIntensity: 0.2,
    ambientColor: new Color(0x404040),
    lightProbes: false,
    reflectionProbes: false
  },
  postProcessing: {
    enabled: false,
    antialiasing: {
      enabled: true,
      type: 'FXAA'
    },
    toneMapping: {
      enabled: true,
      type: 'aces',
      exposure: 1.0
    },
    bloom: {
      enabled: false,
      threshold: 1.0,
      strength: 1.0,
      radius: 0.5
    },
    depthOfField: {
      enabled: false,
      focusDistance: 10,
      focalLength: 50,
      bokehScale: 1.0
    },
    ssao: {
      enabled: false,
      radius: 0.1,
      intensity: 1.0,
      bias: 0.01
    }
  }
}

/**
 * 默认视口配置
 */
const defaultViewportConfig: ViewportConfig = {
  type: ViewportType.PERSPECTIVE,
  showGrid: true,
  gridSize: 10,
  gridDivisions: 10,
  showGizmos: true,
  showStats: false,
  renderMode: 'solid',
  shadingMode: 'smooth',
  enableLighting: true,
  enableShadows: true,
  enablePostProcessing: false
}

/**
 * 默认编辑器设置
 */
const defaultSettings: EditorSettings = {
  snap: {
    enabled: false,
    type: 'grid',
    increment: 1
  },
  autoSave: {
    enabled: true,
    interval: 300000 // 5分钟
  },
  undoRedo: {
    maxSteps: 50
  },
  performance: {
    maxFPS: 60,
    enableLOD: true,
    enableFrustumCulling: true,
    enableOcclusionCulling: false
  },
  debug: {
    showBoundingBoxes: false,
    showWireframes: false,
    showNormals: false,
    showLightHelpers: false,
    showCameraHelpers: false
  }
}

/**
 * 默认状态
 */
const defaultState: SceneStateType = {
  mode: EditorMode.SELECT,
  viewportType: ViewportType.PERSPECTIVE,
  objects: [],
  selection: {
    selectedObjects: [],
    primaryObject: undefined,
    selectionBox: undefined,
    selectionCenter: undefined
  },
  sceneConfig: defaultSceneConfig,
  viewportConfig: defaultViewportConfig,
  settings: defaultSettings,
  
  history: {
    undoStack: [],
    redoStack: [],
    maxSize: 50
  },
  
  ui: {
    showHierarchy: true,
    showInspector: true,
    showLightingPanel: false,
    showMaterialEditor: false,
    showTerrainEditor: false,
    showPhysicsPanel: false,
    showAnimationTimeline: false,
    hierarchyWidth: 280,
    inspectorWidth: 320
  },
  
  loading: {
    isLoading: false,
    message: null
  },
  
  error: {
    hasError: false,
    message: null,
    details: null
  },
  
  stats: {
    fps: 0,
    frameTime: 0,
    triangles: 0,
    drawCalls: 0,
    memoryUsage: 0
  }
}

/**
 * 场景状态实例
 */
export const SceneState: State<SceneStateType> = hookstate(defaultState)

/**
 * 场景状态操作函数
 */
export const SceneActions = {
  /**
   * 设置编辑器模式
   */
  setMode: (mode: EditorMode) => {
    SceneState.mode.set(mode)
  },
  
  /**
   * 设置视口类型
   */
  setViewportType: (type: ViewportType) => {
    SceneState.viewportType.set(type)
  },
  
  /**
   * 设置场景对象
   */
  setObjects: (objects: SceneObject[]) => {
    SceneState.objects.set(objects)
  },
  
  /**
   * 添加对象
   */
  addObject: (object: SceneObject) => {
    const current = SceneState.objects.get()
    SceneState.objects.set([...current, object])
  },
  
  /**
   * 更新对象
   */
  updateObject: (id: string, updates: Partial<SceneObject>) => {
    const current = SceneState.objects.get()
    const index = current.findIndex(obj => obj.id === id)
    if (index !== -1) {
      const updated = [...current]
      updated[index] = { ...updated[index], ...updates }
      SceneState.objects.set(updated)
    }
  },
  
  /**
   * 删除对象
   */
  removeObject: (id: string) => {
    const current = SceneState.objects.get()
    SceneState.objects.set(current.filter(obj => obj.id !== id))
  },
  
  /**
   * 创建对象
   */
  createObject: (type: string, position?: [number, number, number]) => {
    const newObject: SceneObject = {
      id: generateId(),
      name: `${type}_${Date.now()}`,
      type: type as any,
      children: [],
      transform: {
        position: new Vector3(...(position || [0, 0, 0])),
        rotation: new Quaternion(),
        scale: new Vector3(1, 1, 1)
      },
      visible: true,
      locked: false,
      static: false,
      layer: 0,
      tags: [],
      userData: {}
    }
    
    SceneActions.addObject(newObject)
    SceneActions.setSelectedObjects([newObject.id])
  },
  
  /**
   * 删除对象
   */
  deleteObjects: (objectIds: string[]) => {
    for (const id of objectIds) {
      SceneActions.removeObject(id)
    }
    SceneActions.clearSelection()
  },
  
  /**
   * 设置选中对象
   */
  setSelectedObjects: (objectIds: string[]) => {
    SceneState.selection.selectedObjects.set(objectIds)
    SceneState.selection.primaryObject.set(objectIds[0])
  },
  
  /**
   * 添加选中对象
   */
  addSelectedObject: (objectId: string) => {
    const current = SceneState.selection.selectedObjects.get()
    if (!current.includes(objectId)) {
      SceneState.selection.selectedObjects.set([...current, objectId])
    }
  },
  
  /**
   * 移除选中对象
   */
  removeSelectedObject: (objectId: string) => {
    const current = SceneState.selection.selectedObjects.get()
    SceneState.selection.selectedObjects.set(current.filter(id => id !== objectId))
  },
  
  /**
   * 清空选择
   */
  clearSelection: () => {
    SceneState.selection.set({
      selectedObjects: [],
      primaryObject: undefined,
      selectionBox: undefined,
      selectionCenter: undefined
    })
  },
  
  /**
   * 更新场景配置
   */
  updateSceneConfig: (updates: Partial<SceneConfig>) => {
    const current = SceneState.sceneConfig.get()
    SceneState.sceneConfig.set({ ...current, ...updates })
  },
  
  /**
   * 更新视口配置
   */
  updateViewportConfig: (updates: Partial<ViewportConfig>) => {
    const current = SceneState.viewportConfig.get()
    SceneState.viewportConfig.set({ ...current, ...updates })
  },
  
  /**
   * 更新编辑器设置
   */
  updateSettings: (updates: Partial<EditorSettings>) => {
    const current = SceneState.settings.get()
    SceneState.settings.set({ ...current, ...updates })
  },
  
  /**
   * 添加历史记录
   */
  addHistoryEntry: (entry: HistoryEntry) => {
    const current = SceneState.history.get()
    const newUndoStack = [...current.undoStack, entry]
    
    // 限制栈大小
    if (newUndoStack.length > current.maxSize) {
      newUndoStack.shift()
    }
    
    SceneState.history.undoStack.set(newUndoStack)
    SceneState.history.redoStack.set([]) // 清空重做栈
  },
  
  /**
   * 撤销操作
   */
  undo: () => {
    const current = SceneState.history.get()
    if (current.undoStack.length > 0) {
      const entry = current.undoStack[current.undoStack.length - 1]
      const newUndoStack = current.undoStack.slice(0, -1)
      const newRedoStack = [...current.redoStack, entry]
      
      SceneState.history.undoStack.set(newUndoStack)
      SceneState.history.redoStack.set(newRedoStack)
      
      // 执行撤销操作
      // TODO: 实现具体的撤销逻辑
    }
  },
  
  /**
   * 重做操作
   */
  redo: () => {
    const current = SceneState.history.get()
    if (current.redoStack.length > 0) {
      const entry = current.redoStack[current.redoStack.length - 1]
      const newRedoStack = current.redoStack.slice(0, -1)
      const newUndoStack = [...current.undoStack, entry]
      
      SceneState.history.undoStack.set(newUndoStack)
      SceneState.history.redoStack.set(newRedoStack)
      
      // 执行重做操作
      // TODO: 实现具体的重做逻辑
    }
  },
  
  /**
   * 切换UI面板
   */
  toggleUIPanel: (panel: keyof SceneStateType['ui']) => {
    const current = SceneState.ui[panel].get()
    SceneState.ui[panel].set(!current)
  },
  
  /**
   * 设置面板宽度
   */
  setPanelWidth: (panel: 'hierarchyWidth' | 'inspectorWidth', width: number) => {
    SceneState.ui[panel].set(width)
  },
  
  /**
   * 设置加载状态
   */
  setLoading: (isLoading: boolean, message?: string) => {
    SceneState.loading.set({
      isLoading,
      message: message || null
    })
  },
  
  /**
   * 设置错误状态
   */
  setError: (message: string, details?: any) => {
    SceneState.error.set({
      hasError: true,
      message,
      details
    })
  },
  
  /**
   * 清除错误状态
   */
  clearError: () => {
    SceneState.error.set({
      hasError: false,
      message: null,
      details: null
    })
  },
  
  /**
   * 更新性能统计
   */
  updateStats: (stats: Partial<SceneStateType['stats']>) => {
    const current = SceneState.stats.get()
    SceneState.stats.set({ ...current, ...stats })
  },
  
  /**
   * 加载场景
   */
  loadScene: (sceneData: any) => {
    SceneActions.setLoading(true, '加载场景中...')
    
    try {
      // TODO: 实现场景加载逻辑
      console.log('加载场景:', sceneData)
      
      SceneActions.setLoading(false)
    } catch (error) {
      SceneActions.setError('场景加载失败', error)
      SceneActions.setLoading(false)
    }
  },
  
  /**
   * 导出场景
   */
  exportScene: () => {
    const objects = SceneState.objects.get()
    const sceneConfig = SceneState.sceneConfig.get()
    
    return {
      objects,
      sceneConfig,
      metadata: {
        version: '1.0.0',
        exportedAt: new Date().toISOString()
      }
    }
  }
}

/**
 * 生成唯一ID
 */
function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}
