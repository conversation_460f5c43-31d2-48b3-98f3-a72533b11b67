/**
 * DL-Engine 自适应学习组件
 * 基于学习者表现和偏好动态调整学习内容和策略
 */

import { defineComponent } from '../ComponentFunctions'

/**
 * 学习者模型类型
 */
export enum LearnerModelType {
  COGNITIVE = 'cognitive',           // 认知模型
  BEHAVIORAL = 'behavioral',         // 行为模型
  AFFECTIVE = 'affective',          // 情感模型
  METACOGNITIVE = 'metacognitive'   // 元认知模型
}

/**
 * 适应策略类型
 */
export enum AdaptationStrategy {
  CONTENT_SEQUENCING = 'content_sequencing',     // 内容排序
  DIFFICULTY_ADJUSTMENT = 'difficulty_adjustment', // 难度调整
  PACE_CONTROL = 'pace_control',                 // 节奏控制
  RESOURCE_SELECTION = 'resource_selection',     // 资源选择
  FEEDBACK_TIMING = 'feedback_timing',           // 反馈时机
  HINT_PROVISION = 'hint_provision',             // 提示提供
  ASSESSMENT_ADAPTATION = 'assessment_adaptation' // 评估适应
}

/**
 * 学习者特征
 */
export interface LearnerCharacteristics {
  /** 认知能力 */
  cognitiveAbility: {
    workingMemory: number      // 工作记忆 (0-100)
    processingSpeed: number    // 处理速度 (0-100)
    attention: number          // 注意力 (0-100)
    reasoning: number          // 推理能力 (0-100)
  }
  
  /** 学习风格 */
  learningStyle: {
    visual: number             // 视觉学习偏好 (0-100)
    auditory: number           // 听觉学习偏好 (0-100)
    kinesthetic: number        // 动觉学习偏好 (0-100)
    reading: number            // 阅读学习偏好 (0-100)
  }
  
  /** 动机水平 */
  motivation: {
    intrinsic: number          // 内在动机 (0-100)
    extrinsic: number          // 外在动机 (0-100)
    achievement: number        // 成就动机 (0-100)
    curiosity: number          // 好奇心 (0-100)
  }
  
  /** 情感状态 */
  emotionalState: {
    engagement: number         // 参与度 (0-100)
    frustration: number        // 挫折感 (0-100)
    confidence: number         // 自信心 (0-100)
    anxiety: number            // 焦虑水平 (0-100)
  }
  
  /** 元认知技能 */
  metacognitive: {
    planning: number           // 计划能力 (0-100)
    monitoring: number         // 监控能力 (0-100)
    evaluation: number         // 评估能力 (0-100)
    regulation: number         // 调节能力 (0-100)
  }
}

/**
 * 适应规则
 */
export interface AdaptationRule {
  /** 规则ID */
  id: string
  
  /** 规则名称 */
  name: string
  
  /** 规则描述 */
  description: string
  
  /** 触发条件 */
  condition: {
    type: 'performance' | 'time' | 'behavior' | 'emotion' | 'composite'
    operator: '>' | '<' | '=' | '>=' | '<=' | '!='
    value: number
    metric: string
  }
  
  /** 适应动作 */
  action: {
    strategy: AdaptationStrategy
    parameters: Record<string, any>
    intensity: number // 适应强度 (0-1)
  }
  
  /** 规则优先级 */
  priority: number
  
  /** 规则权重 */
  weight: number
  
  /** 是否启用 */
  enabled: boolean
  
  /** 应用次数 */
  applicationCount: number
  
  /** 成功率 */
  successRate: number
}

/**
 * 适应历史记录
 */
export interface AdaptationHistory {
  /** 记录ID */
  id: string
  
  /** 适应时间 */
  timestamp: Date
  
  /** 触发的规则ID */
  ruleId: string
  
  /** 适应前状态 */
  beforeState: {
    performance: number
    difficulty: number
    engagement: number
  }
  
  /** 适应后状态 */
  afterState: {
    performance: number
    difficulty: number
    engagement: number
  }
  
  /** 适应效果 */
  effectiveness: number
  
  /** 学习者反馈 */
  learnerFeedback?: {
    satisfaction: number
    perceived_difficulty: number
    comments: string
  }
}

/**
 * 预测模型
 */
export interface PredictionModel {
  /** 模型ID */
  id: string
  
  /** 模型类型 */
  type: 'performance' | 'engagement' | 'completion' | 'difficulty'
  
  /** 模型参数 */
  parameters: Record<string, number>
  
  /** 模型准确率 */
  accuracy: number
  
  /** 最后训练时间 */
  lastTrainedAt: Date
  
  /** 预测置信度 */
  confidence: number
}

/**
 * 自适应学习数据
 */
export interface AdaptiveLearningData {
  /** 学习者ID */
  learnerId: string
  
  /** 课程ID */
  courseId: string
  
  /** 学习者特征模型 */
  learnerCharacteristics: LearnerCharacteristics
  
  /** 当前难度级别 */
  currentDifficulty: number
  
  /** 当前学习节奏 */
  currentPace: number
  
  /** 适应规则集 */
  adaptationRules: AdaptationRule[]
  
  /** 适应历史 */
  adaptationHistory: AdaptationHistory[]
  
  /** 预测模型 */
  predictionModels: PredictionModel[]
  
  /** 个性化参数 */
  personalizationParameters: {
    contentDifficultyMultiplier: number
    feedbackDelayMultiplier: number
    hintFrequencyMultiplier: number
    assessmentFrequencyMultiplier: number
    socialInteractionPreference: number
  }
  
  /** 适应配置 */
  adaptationConfig: {
    enabled: boolean
    adaptationFrequency: number // 适应检查频率（分钟）
    minAdaptationInterval: number // 最小适应间隔（分钟）
    maxAdaptationsPerSession: number
    learnerControlLevel: number // 学习者控制程度 (0-1)
  }
  
  /** 性能指标 */
  performanceMetrics: {
    averageScore: number
    learningEfficiency: number
    retentionRate: number
    engagementLevel: number
    frustrationLevel: number
    timeOnTask: number
  }
  
  /** 最后适应时间 */
  lastAdaptationTime: Date
  
  /** 创建时间 */
  createdAt: Date
  
  /** 更新时间 */
  updatedAt: Date
}

/**
 * 自适应学习组件
 */
export const AdaptiveLearningComponent = defineComponent({
  name: 'AdaptiveLearning',
  schema: {
    learnerId: '',
    courseId: '',
    learnerCharacteristics: {
      cognitiveAbility: {
        workingMemory: 50,
        processingSpeed: 50,
        attention: 50,
        reasoning: 50
      },
      learningStyle: {
        visual: 25,
        auditory: 25,
        kinesthetic: 25,
        reading: 25
      },
      motivation: {
        intrinsic: 50,
        extrinsic: 50,
        achievement: 50,
        curiosity: 50
      },
      emotionalState: {
        engagement: 50,
        frustration: 0,
        confidence: 50,
        anxiety: 0
      },
      metacognitive: {
        planning: 50,
        monitoring: 50,
        evaluation: 50,
        regulation: 50
      }
    } as LearnerCharacteristics,
    currentDifficulty: 3,
    currentPace: 1.0,
    adaptationRules: [] as AdaptationRule[],
    adaptationHistory: [] as AdaptationHistory[],
    predictionModels: [] as PredictionModel[],
    personalizationParameters: {
      contentDifficultyMultiplier: 1.0,
      feedbackDelayMultiplier: 1.0,
      hintFrequencyMultiplier: 1.0,
      assessmentFrequencyMultiplier: 1.0,
      socialInteractionPreference: 0.5
    },
    adaptationConfig: {
      enabled: true,
      adaptationFrequency: 5,
      minAdaptationInterval: 2,
      maxAdaptationsPerSession: 3,
      learnerControlLevel: 0.3
    },
    performanceMetrics: {
      averageScore: 0,
      learningEfficiency: 0,
      retentionRate: 0,
      engagementLevel: 50,
      frustrationLevel: 0,
      timeOnTask: 0
    },
    lastAdaptationTime: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  } as AdaptiveLearningData,
  
  onAdd: (entity, component) => {
    console.log(`Adaptive learning initialized for learner ${component.learnerId}`)
    component.createdAt = new Date()
    component.updatedAt = new Date()
    
    // 初始化默认适应规则
    AdaptiveLearningUtils.initializeDefaultRules(component)
  },
  
  onRemove: (entity, component) => {
    console.log(`Adaptive learning removed for learner ${component.learnerId}`)
  },
  
  onSet: (entity, component) => {
    component.updatedAt = new Date()
  }
})

/**
 * 自适应学习工具函数
 */
export const AdaptiveLearningUtils = {
  /**
   * 初始化默认适应规则
   */
  initializeDefaultRules: (adaptive: AdaptiveLearningData): void => {
    const defaultRules: AdaptationRule[] = [
      {
        id: 'rule_performance_low',
        name: '低表现适应',
        description: '当学习表现低于阈值时降低难度',
        condition: {
          type: 'performance',
          operator: '<',
          value: 60,
          metric: 'averageScore'
        },
        action: {
          strategy: AdaptationStrategy.DIFFICULTY_ADJUSTMENT,
          parameters: { adjustment: -0.5 },
          intensity: 0.7
        },
        priority: 1,
        weight: 0.8,
        enabled: true,
        applicationCount: 0,
        successRate: 0
      },
      {
        id: 'rule_frustration_high',
        name: '高挫折感适应',
        description: '当挫折感过高时提供更多提示',
        condition: {
          type: 'emotion',
          operator: '>',
          value: 70,
          metric: 'frustration'
        },
        action: {
          strategy: AdaptationStrategy.HINT_PROVISION,
          parameters: { frequency: 1.5 },
          intensity: 0.6
        },
        priority: 2,
        weight: 0.7,
        enabled: true,
        applicationCount: 0,
        successRate: 0
      },
      {
        id: 'rule_engagement_low',
        name: '低参与度适应',
        description: '当参与度低时调整内容类型',
        condition: {
          type: 'emotion',
          operator: '<',
          value: 40,
          metric: 'engagement'
        },
        action: {
          strategy: AdaptationStrategy.RESOURCE_SELECTION,
          parameters: { preferInteractive: true },
          intensity: 0.5
        },
        priority: 3,
        weight: 0.6,
        enabled: true,
        applicationCount: 0,
        successRate: 0
      }
    ]
    
    adaptive.adaptationRules = defaultRules
  },
  
  /**
   * 检查是否需要适应
   */
  checkAdaptationNeeded: (adaptive: AdaptiveLearningData): boolean => {
    if (!adaptive.adaptationConfig.enabled) return false
    
    const now = new Date()
    const timeSinceLastAdaptation = now.getTime() - adaptive.lastAdaptationTime.getTime()
    const minInterval = adaptive.adaptationConfig.minAdaptationInterval * 60 * 1000 // 转换为毫秒
    
    if (timeSinceLastAdaptation < minInterval) return false
    
    // 检查是否有规则被触发
    return adaptive.adaptationRules.some(rule => 
      rule.enabled && AdaptiveLearningUtils.evaluateRule(adaptive, rule)
    )
  },
  
  /**
   * 评估适应规则
   */
  evaluateRule: (adaptive: AdaptiveLearningData, rule: AdaptationRule): boolean => {
    const { condition } = rule
    let currentValue: number
    
    switch (condition.type) {
      case 'performance':
        currentValue = adaptive.performanceMetrics[condition.metric as keyof typeof adaptive.performanceMetrics] as number
        break
      case 'emotion':
        currentValue = adaptive.learnerCharacteristics.emotionalState[condition.metric as keyof typeof adaptive.learnerCharacteristics.emotionalState]
        break
      case 'behavior':
        currentValue = adaptive.performanceMetrics[condition.metric as keyof typeof adaptive.performanceMetrics] as number
        break
      default:
        return false
    }
    
    switch (condition.operator) {
      case '>': return currentValue > condition.value
      case '<': return currentValue < condition.value
      case '=': return currentValue === condition.value
      case '>=': return currentValue >= condition.value
      case '<=': return currentValue <= condition.value
      case '!=': return currentValue !== condition.value
      default: return false
    }
  },
  
  /**
   * 应用适应策略
   */
  applyAdaptation: (adaptive: AdaptiveLearningData, rule: AdaptationRule): void => {
    const beforeState = {
      performance: adaptive.performanceMetrics.averageScore,
      difficulty: adaptive.currentDifficulty,
      engagement: adaptive.learnerCharacteristics.emotionalState.engagement
    }
    
    switch (rule.action.strategy) {
      case AdaptationStrategy.DIFFICULTY_ADJUSTMENT:
        AdaptiveLearningUtils.adjustDifficulty(adaptive, rule.action.parameters.adjustment)
        break
      case AdaptationStrategy.PACE_CONTROL:
        AdaptiveLearningUtils.adjustPace(adaptive, rule.action.parameters.adjustment)
        break
      case AdaptationStrategy.HINT_PROVISION:
        AdaptiveLearningUtils.adjustHintFrequency(adaptive, rule.action.parameters.frequency)
        break
      case AdaptationStrategy.RESOURCE_SELECTION:
        AdaptiveLearningUtils.adjustResourcePreference(adaptive, rule.action.parameters)
        break
    }
    
    // 记录适应历史
    const adaptationRecord: AdaptationHistory = {
      id: `adaptation_${Date.now()}`,
      timestamp: new Date(),
      ruleId: rule.id,
      beforeState,
      afterState: {
        performance: adaptive.performanceMetrics.averageScore,
        difficulty: adaptive.currentDifficulty,
        engagement: adaptive.learnerCharacteristics.emotionalState.engagement
      },
      effectiveness: 0 // 将在后续评估中更新
    }
    
    adaptive.adaptationHistory.push(adaptationRecord)
    adaptive.lastAdaptationTime = new Date()
    
    // 更新规则应用次数
    rule.applicationCount++
    
    console.log(`Applied adaptation rule: ${rule.name}`)
  },
  
  /**
   * 调整难度
   */
  adjustDifficulty: (adaptive: AdaptiveLearningData, adjustment: number): void => {
    adaptive.currentDifficulty = Math.max(1, Math.min(5, adaptive.currentDifficulty + adjustment))
    adaptive.personalizationParameters.contentDifficultyMultiplier = adaptive.currentDifficulty / 3
  },
  
  /**
   * 调整学习节奏
   */
  adjustPace: (adaptive: AdaptiveLearningData, adjustment: number): void => {
    adaptive.currentPace = Math.max(0.5, Math.min(2.0, adaptive.currentPace + adjustment))
  },
  
  /**
   * 调整提示频率
   */
  adjustHintFrequency: (adaptive: AdaptiveLearningData, frequency: number): void => {
    adaptive.personalizationParameters.hintFrequencyMultiplier = Math.max(0.5, Math.min(2.0, frequency))
  },
  
  /**
   * 调整资源偏好
   */
  adjustResourcePreference: (adaptive: AdaptiveLearningData, parameters: Record<string, any>): void => {
    if (parameters.preferInteractive) {
      // 增加对互动内容的偏好
      adaptive.learnerCharacteristics.learningStyle.kinesthetic += 10
      adaptive.learnerCharacteristics.learningStyle.visual += 5
    }
  },
  
  /**
   * 更新学习者特征
   */
  updateLearnerCharacteristics: (adaptive: AdaptiveLearningData, newData: Partial<LearnerCharacteristics>): void => {
    // 使用加权平均更新特征
    const alpha = 0.3 // 学习率
    
    if (newData.emotionalState) {
      Object.keys(newData.emotionalState).forEach(key => {
        const currentValue = adaptive.learnerCharacteristics.emotionalState[key as keyof typeof adaptive.learnerCharacteristics.emotionalState]
        const newValue = newData.emotionalState![key as keyof typeof newData.emotionalState]!
        adaptive.learnerCharacteristics.emotionalState[key as keyof typeof adaptive.learnerCharacteristics.emotionalState] = 
          currentValue * (1 - alpha) + newValue * alpha
      })
    }
    
    adaptive.updatedAt = new Date()
  },
  
  /**
   * 预测学习表现
   */
  predictPerformance: (adaptive: AdaptiveLearningData, timeHorizon: number): number => {
    // 简化的预测模型，基于当前特征和历史表现
    const currentPerformance = adaptive.performanceMetrics.averageScore
    const engagement = adaptive.learnerCharacteristics.emotionalState.engagement
    const confidence = adaptive.learnerCharacteristics.emotionalState.confidence
    const difficulty = adaptive.currentDifficulty
    
    // 简单的线性预测模型
    const predictedPerformance = currentPerformance * 0.6 + 
                                engagement * 0.2 + 
                                confidence * 0.15 - 
                                (difficulty - 3) * 5
    
    return Math.max(0, Math.min(100, predictedPerformance))
  },
  
  /**
   * 生成个性化建议
   */
  generateRecommendations: (adaptive: AdaptiveLearningData): string[] => {
    const recommendations: string[] = []
    const characteristics = adaptive.learnerCharacteristics
    
    // 基于学习风格的建议
    const dominantStyle = Object.entries(characteristics.learningStyle)
      .reduce((a, b) => a[1] > b[1] ? a : b)[0]
    
    switch (dominantStyle) {
      case 'visual':
        recommendations.push('建议使用更多图表、图像和视频内容')
        break
      case 'auditory':
        recommendations.push('建议增加音频讲解和讨论活动')
        break
      case 'kinesthetic':
        recommendations.push('建议增加互动练习和实践活动')
        break
      case 'reading':
        recommendations.push('建议提供更多文本资料和阅读材料')
        break
    }
    
    // 基于情感状态的建议
    if (characteristics.emotionalState.frustration > 60) {
      recommendations.push('当前挫折感较高，建议降低难度或增加指导')
    }
    
    if (characteristics.emotionalState.engagement < 40) {
      recommendations.push('参与度较低，建议尝试更有趣的学习活动')
    }
    
    return recommendations
  }
}
