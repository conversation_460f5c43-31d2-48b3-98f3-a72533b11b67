/**
 * 属性面板组件
 * 
 * 显示和编辑选中对象的属性
 */

import React, { useState, useCallback } from 'react'
import { useHookstate } from '@hookstate/core'
import { EditorState } from '../../services/EditorState'
import { Vector3, Quaternion, Euler } from 'three'

/**
 * 组件属性数据
 */
interface ComponentProperty {
  name: string
  type: 'string' | 'number' | 'boolean' | 'vector3' | 'color' | 'select'
  value: any
  options?: string[]
  min?: number
  max?: number
  step?: number
}

/**
 * 组件数据
 */
interface ComponentData {
  name: string
  type: string
  properties: ComponentProperty[]
  expanded: boolean
}

/**
 * 属性面板属性
 */
export interface PropertiesPanelProps {
  /** 面板类名 */
  className?: string
  /** 面板样式 */
  style?: React.CSSProperties
}

/**
 * 属性面板组件
 */
const PropertiesPanel: React.FC<PropertiesPanelProps> = ({
  className = '',
  style = {}
}) => {
  const editorState = useHookstate(EditorState)
  const [expandedComponents, setExpandedComponents] = useState<Set<string>>(new Set(['transform']))
  
  // 获取编辑器状态
  const selection = editorState.selection.get()
  const transform = editorState.transform.get()
  
  // 模拟组件数据
  const [componentsData, setComponentsData] = useState<ComponentData[]>([
    {
      name: '变换',
      type: 'transform',
      expanded: true,
      properties: [
        { name: '位置 X', type: 'number', value: transform.position.x, step: 0.1 },
        { name: '位置 Y', type: 'number', value: transform.position.y, step: 0.1 },
        { name: '位置 Z', type: 'number', value: transform.position.z, step: 0.1 },
        { name: '旋转 X', type: 'number', value: 0, step: 1, min: -180, max: 180 },
        { name: '旋转 Y', type: 'number', value: 0, step: 1, min: -180, max: 180 },
        { name: '旋转 Z', type: 'number', value: 0, step: 1, min: -180, max: 180 },
        { name: '缩放 X', type: 'number', value: transform.scale.x, step: 0.1, min: 0.01 },
        { name: '缩放 Y', type: 'number', value: transform.scale.y, step: 0.1, min: 0.01 },
        { name: '缩放 Z', type: 'number', value: transform.scale.z, step: 0.1, min: 0.01 }
      ]
    },
    {
      name: '网格渲染器',
      type: 'mesh-renderer',
      expanded: false,
      properties: [
        { name: '材质', type: 'select', value: 'default', options: ['default', 'metal', 'glass', 'wood'] },
        { name: '投射阴影', type: 'boolean', value: true },
        { name: '接收阴影', type: 'boolean', value: true },
        { name: '可见', type: 'boolean', value: true }
      ]
    },
    {
      name: '碰撞器',
      type: 'collider',
      expanded: false,
      properties: [
        { name: '类型', type: 'select', value: 'box', options: ['box', 'sphere', 'capsule', 'mesh'] },
        { name: '是否触发器', type: 'boolean', value: false },
        { name: '物理材质', type: 'select', value: 'default', options: ['default', 'bouncy', 'ice', 'rubber'] }
      ]
    }
  ])
  
  /**
   * 处理组件展开/折叠
   */
  const handleComponentToggle = useCallback((componentType: string) => {
    setExpandedComponents(prev => {
      const newSet = new Set(prev)
      if (newSet.has(componentType)) {
        newSet.delete(componentType)
      } else {
        newSet.add(componentType)
      }
      return newSet
    })
  }, [])
  
  /**
   * 处理属性值变化
   */
  const handlePropertyChange = useCallback((componentType: string, propertyName: string, value: any) => {
    setComponentsData(prev => prev.map(component => {
      if (component.type === componentType) {
        return {
          ...component,
          properties: component.properties.map(prop => 
            prop.name === propertyName ? { ...prop, value } : prop
          )
        }
      }
      return component
    }))
    
    // 更新编辑器状态
    if (componentType === 'transform') {
      // TODO: 更新变换状态
      console.log('更新变换属性:', propertyName, value)
    }
  }, [])
  
  /**
   * 渲染属性输入控件
   */
  const renderPropertyInput = (component: ComponentData, property: ComponentProperty) => {
    const inputId = `${component.type}-${property.name}`
    
    switch (property.type) {
      case 'string':
        return (
          <input
            id={inputId}
            type="text"
            value={property.value}
            onChange={(e) => handlePropertyChange(component.type, property.name, e.target.value)}
            className="property-input"
          />
        )
      
      case 'number':
        return (
          <input
            id={inputId}
            type="number"
            value={property.value}
            min={property.min}
            max={property.max}
            step={property.step || 1}
            onChange={(e) => handlePropertyChange(component.type, property.name, parseFloat(e.target.value))}
            className="property-input"
          />
        )
      
      case 'boolean':
        return (
          <input
            id={inputId}
            type="checkbox"
            checked={property.value}
            onChange={(e) => handlePropertyChange(component.type, property.name, e.target.checked)}
            className="w-4 h-4"
          />
        )
      
      case 'select':
        return (
          <select
            id={inputId}
            value={property.value}
            onChange={(e) => handlePropertyChange(component.type, property.name, e.target.value)}
            className="property-input"
          >
            {property.options?.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        )
      
      case 'color':
        return (
          <input
            id={inputId}
            type="color"
            value={property.value}
            onChange={(e) => handlePropertyChange(component.type, property.name, e.target.value)}
            className="property-input h-8"
          />
        )
      
      default:
        return (
          <input
            id={inputId}
            type="text"
            value={property.value}
            onChange={(e) => handlePropertyChange(component.type, property.name, e.target.value)}
            className="property-input"
          />
        )
    }
  }
  
  /**
   * 渲染组件
   */
  const renderComponent = (component: ComponentData) => {
    const isExpanded = expandedComponents.has(component.type)
    
    return (
      <div key={component.type} className="property-group">
        {/* 组件标题 */}
        <div 
          className="property-group-title cursor-pointer flex items-center justify-between"
          onClick={() => handleComponentToggle(component.type)}
        >
          <div className="flex items-center gap-2">
            <span className={`hierarchy-icon ${isExpanded ? 'expanded' : ''}`}>▶</span>
            <span>{component.name}</span>
          </div>
          <div className="flex items-center gap-1">
            <button 
              className="text-xs px-1 py-0.5 bg-gray-600 hover:bg-gray-500 rounded"
              title="重置组件"
            >
              ↻
            </button>
            <button 
              className="text-xs px-1 py-0.5 bg-red-600 hover:bg-red-500 rounded"
              title="移除组件"
            >
              ✕
            </button>
          </div>
        </div>
        
        {/* 组件属性 */}
        {isExpanded && (
          <div className="pl-4">
            {component.properties.map(property => (
              <div key={property.name} className="property-item">
                <label htmlFor={`${component.type}-${property.name}`} className="property-label">
                  {property.name}
                </label>
                {renderPropertyInput(component, property)}
              </div>
            ))}
          </div>
        )}
      </div>
    )
  }
  
  /**
   * 处理添加组件
   */
  const handleAddComponent = () => {
    // TODO: 实现添加组件逻辑
    console.log('添加组件')
  }
  
  return (
    <div className={`properties-panel ${className}`} style={style}>
      {/* 面板头部 */}
      <div className="panel-header">
        <span>属性</span>
        <button
          className="text-xs px-2 py-1 bg-blue-600 hover:bg-blue-500 rounded"
          onClick={handleAddComponent}
          title="添加组件"
        >
          + 组件
        </button>
      </div>
      
      {/* 属性内容 */}
      <div className="panel-content">
        {selection.selectedEntities.length > 0 ? (
          <>
            {/* 对象信息 */}
            <div className="property-group">
              <div className="property-group-title">对象信息</div>
              <div className="pl-4">
                <div className="property-item">
                  <label className="property-label">名称</label>
                  <input
                    type="text"
                    value="选中对象"
                    className="property-input"
                  />
                </div>
                <div className="property-item">
                  <label className="property-label">标签</label>
                  <input
                    type="text"
                    value="Untagged"
                    className="property-input"
                  />
                </div>
                <div className="property-item">
                  <label className="property-label">层级</label>
                  <select className="property-input">
                    <option>Default</option>
                    <option>UI</option>
                    <option>Water</option>
                  </select>
                </div>
              </div>
            </div>
            
            {/* 组件列表 */}
            {componentsData.map(renderComponent)}
          </>
        ) : (
          /* 无选择状态 */
          <div className="text-center text-gray-400 py-8">
            <div className="text-4xl mb-2">🎯</div>
            <div>未选择对象</div>
            <div className="text-sm">在场景中选择一个对象来查看其属性</div>
          </div>
        )}
      </div>
    </div>
  )
}

export default PropertiesPanel
