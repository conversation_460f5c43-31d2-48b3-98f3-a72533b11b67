/**
 * DL-Engine 学习资源组件
 * 管理多媒体学习资源、智能推荐和个性化内容
 */

import { defineComponent } from '../ComponentFunctions'

/**
 * 资源类型
 */
export enum ResourceType {
  TEXT = 'text',                 // 文本
  IMAGE = 'image',              // 图片
  VIDEO = 'video',              // 视频
  AUDIO = 'audio',              // 音频
  ANIMATION = 'animation',       // 动画
  SIMULATION = 'simulation',     // 模拟
  INTERACTIVE = 'interactive',   // 交互式
  DOCUMENT = 'document',         // 文档
  PRESENTATION = 'presentation', // 演示文稿
  QUIZ = 'quiz',                // 测验
  GAME = 'game',                // 游戏
  AR_VR = 'ar_vr'               // AR/VR内容
}

/**
 * 资源难度级别
 */
export enum ResourceDifficulty {
  BEGINNER = 1,     // 初级
  ELEMENTARY = 2,   // 基础
  INTERMEDIATE = 3, // 中级
  ADVANCED = 4,     // 高级
  EXPERT = 5        // 专家
}

/**
 * 资源状态
 */
export enum ResourceStatus {
  DRAFT = 'draft',             // 草稿
  REVIEW = 'review',           // 审核中
  PUBLISHED = 'published',     // 已发布
  ARCHIVED = 'archived',       // 已归档
  DEPRECATED = 'deprecated'    // 已废弃
}

/**
 * 学习资源元数据
 */
export interface ResourceMetadata {
  /** 资源ID */
  id: string
  
  /** 资源标题 */
  title: string
  
  /** 资源描述 */
  description: string
  
  /** 资源类型 */
  type: ResourceType
  
  /** 资源URL或路径 */
  url: string
  
  /** 缩略图URL */
  thumbnailUrl?: string
  
  /** 资源大小（字节） */
  size: number
  
  /** 资源时长（秒，适用于音视频） */
  duration?: number
  
  /** 资源难度 */
  difficulty: ResourceDifficulty
  
  /** 资源状态 */
  status: ResourceStatus
  
  /** 目标学习者类型 */
  targetAudience: string[]
  
  /** 学习目标 */
  learningObjectives: string[]
  
  /** 关键词标签 */
  keywords: string[]
  
  /** 学科分类 */
  subjects: string[]
  
  /** 语言 */
  language: string
  
  /** 作者信息 */
  author: {
    id: string
    name: string
    email?: string
    organization?: string
  }
  
  /** 版权信息 */
  copyright: {
    license: string
    attribution: string
    allowCommercialUse: boolean
    allowModification: boolean
  }
  
  /** 技术要求 */
  technicalRequirements: {
    minBandwidth?: number
    supportedDevices: string[]
    requiredPlugins: string[]
    browserCompatibility: string[]
  }
  
  /** 创建时间 */
  createdAt: Date
  
  /** 更新时间 */
  updatedAt: Date
  
  /** 发布时间 */
  publishedAt?: Date
  
  /** 版本号 */
  version: string
}

/**
 * 资源使用统计
 */
export interface ResourceUsageStats {
  /** 总访问次数 */
  totalViews: number
  
  /** 唯一访问者数 */
  uniqueViewers: number
  
  /** 平均观看时长 */
  averageViewDuration: number
  
  /** 完成率 */
  completionRate: number
  
  /** 平均评分 */
  averageRating: number
  
  /** 评分数量 */
  ratingCount: number
  
  /** 收藏次数 */
  bookmarkCount: number
  
  /** 分享次数 */
  shareCount: number
  
  /** 下载次数 */
  downloadCount: number
  
  /** 最后访问时间 */
  lastAccessTime: Date
}

/**
 * 资源评价
 */
export interface ResourceReview {
  /** 评价ID */
  id: string
  
  /** 评价者ID */
  reviewerId: string
  
  /** 评价者姓名 */
  reviewerName: string
  
  /** 评分 (1-5) */
  rating: number
  
  /** 评价内容 */
  comment: string
  
  /** 评价标签 */
  tags: string[]
  
  /** 是否推荐 */
  recommended: boolean
  
  /** 评价时间 */
  createdAt: Date
  
  /** 有用性投票 */
  helpfulVotes: number
  
  /** 总投票数 */
  totalVotes: number
}

/**
 * 资源推荐
 */
export interface ResourceRecommendation {
  /** 推荐ID */
  id: string
  
  /** 推荐的资源ID */
  resourceId: string
  
  /** 推荐理由 */
  reason: string
  
  /** 推荐算法 */
  algorithm: 'collaborative' | 'content_based' | 'hybrid' | 'popularity' | 'manual'
  
  /** 推荐置信度 (0-1) */
  confidence: number
  
  /** 推荐权重 */
  weight: number
  
  /** 推荐时间 */
  recommendedAt: Date
  
  /** 是否已查看 */
  viewed: boolean
  
  /** 是否已采纳 */
  adopted: boolean
}

/**
 * 学习资源数据
 */
export interface LearningResourceData {
  /** 学习者ID */
  learnerId: string
  
  /** 课程ID */
  courseId: string
  
  /** 当前知识点ID */
  currentKnowledgePointId?: string
  
  /** 可用资源列表 */
  availableResources: ResourceMetadata[]
  
  /** 已访问的资源 */
  accessedResources: Array<{
    resourceId: string
    accessTime: Date
    duration: number
    completed: boolean
    progress: number
    rating?: number
    notes?: string
  }>
  
  /** 收藏的资源 */
  bookmarkedResources: string[]
  
  /** 资源使用统计 */
  usageStats: Map<string, ResourceUsageStats>
  
  /** 资源评价 */
  reviews: ResourceReview[]
  
  /** 个性化推荐 */
  recommendations: ResourceRecommendation[]
  
  /** 学习偏好 */
  learningPreferences: {
    preferredTypes: ResourceType[]
    preferredDifficulty: ResourceDifficulty
    preferredDuration: number // 分钟
    preferredLanguage: string
    accessibilityNeeds: string[]
  }
  
  /** 资源过滤器 */
  filters: {
    types: ResourceType[]
    difficulties: ResourceDifficulty[]
    subjects: string[]
    languages: string[]
    minRating: number
    maxDuration: number
    freeOnly: boolean
  }
  
  /** 搜索历史 */
  searchHistory: Array<{
    query: string
    timestamp: Date
    resultsCount: number
    selectedResourceId?: string
  }>
  
  /** 学习路径资源映射 */
  pathResourceMapping: Map<string, string[]> // 知识点ID -> 资源ID列表
  
  /** 离线资源 */
  offlineResources: Array<{
    resourceId: string
    downloadedAt: Date
    localPath: string
    size: number
    expiresAt?: Date
  }>
  
  /** 创建时间 */
  createdAt: Date
  
  /** 更新时间 */
  updatedAt: Date
}

/**
 * 学习资源组件
 */
export const LearningResourceComponent = defineComponent({
  name: 'LearningResource',
  schema: {
    learnerId: '',
    courseId: '',
    currentKnowledgePointId: undefined as string | undefined,
    availableResources: [] as ResourceMetadata[],
    accessedResources: [],
    bookmarkedResources: [] as string[],
    usageStats: new Map(),
    reviews: [] as ResourceReview[],
    recommendations: [] as ResourceRecommendation[],
    learningPreferences: {
      preferredTypes: [ResourceType.VIDEO, ResourceType.INTERACTIVE],
      preferredDifficulty: ResourceDifficulty.INTERMEDIATE,
      preferredDuration: 15,
      preferredLanguage: 'zh-CN',
      accessibilityNeeds: []
    },
    filters: {
      types: [],
      difficulties: [],
      subjects: [],
      languages: [],
      minRating: 0,
      maxDuration: 60,
      freeOnly: false
    },
    searchHistory: [],
    pathResourceMapping: new Map(),
    offlineResources: [],
    createdAt: new Date(),
    updatedAt: new Date()
  } as LearningResourceData,
  
  onAdd: (entity, component) => {
    console.log(`Learning resource system initialized for learner ${component.learnerId}`)
    component.createdAt = new Date()
    component.updatedAt = new Date()
  },
  
  onRemove: (entity, component) => {
    console.log(`Learning resource system removed for learner ${component.learnerId}`)
  },
  
  onSet: (entity, component) => {
    component.updatedAt = new Date()
  }
})

/**
 * 学习资源工具函数
 */
export const LearningResourceUtils = {
  /**
   * 访问资源
   */
  accessResource: (resources: LearningResourceData, resourceId: string): void => {
    const existingAccess = resources.accessedResources.find(a => a.resourceId === resourceId)
    
    if (existingAccess) {
      existingAccess.accessTime = new Date()
    } else {
      resources.accessedResources.push({
        resourceId,
        accessTime: new Date(),
        duration: 0,
        completed: false,
        progress: 0
      })
    }
    
    // 更新使用统计
    LearningResourceUtils.updateUsageStats(resources, resourceId)
    
    resources.updatedAt = new Date()
  },
  
  /**
   * 更新资源进度
   */
  updateProgress: (resources: LearningResourceData, resourceId: string, progress: number, duration: number): void => {
    const access = resources.accessedResources.find(a => a.resourceId === resourceId)
    if (!access) return
    
    access.progress = Math.max(access.progress, progress)
    access.duration += duration
    access.completed = progress >= 100
    
    // 更新使用统计
    const stats = resources.usageStats.get(resourceId)
    if (stats) {
      stats.averageViewDuration = (stats.averageViewDuration + duration) / 2
      if (access.completed) {
        stats.completionRate = ((stats.completionRate * stats.totalViews) + 1) / (stats.totalViews + 1)
      }
    }
    
    resources.updatedAt = new Date()
  },
  
  /**
   * 评价资源
   */
  rateResource: (resources: LearningResourceData, resourceId: string, rating: number, comment?: string): void => {
    const access = resources.accessedResources.find(a => a.resourceId === resourceId)
    if (access) {
      access.rating = rating
      if (comment) {
        access.notes = comment
      }
    }
    
    // 添加评价记录
    const review: ResourceReview = {
      id: `review_${Date.now()}`,
      reviewerId: resources.learnerId,
      reviewerName: `学习者${resources.learnerId.slice(-4)}`,
      rating,
      comment: comment || '',
      tags: [],
      recommended: rating >= 4,
      createdAt: new Date(),
      helpfulVotes: 0,
      totalVotes: 0
    }
    
    resources.reviews.push(review)
    
    // 更新使用统计
    const stats = resources.usageStats.get(resourceId)
    if (stats) {
      stats.averageRating = ((stats.averageRating * stats.ratingCount) + rating) / (stats.ratingCount + 1)
      stats.ratingCount++
    }
    
    resources.updatedAt = new Date()
  },
  
  /**
   * 收藏资源
   */
  bookmarkResource: (resources: LearningResourceData, resourceId: string): boolean => {
    if (resources.bookmarkedResources.includes(resourceId)) {
      return false
    }
    
    resources.bookmarkedResources.push(resourceId)
    
    // 更新使用统计
    const stats = resources.usageStats.get(resourceId)
    if (stats) {
      stats.bookmarkCount++
    }
    
    resources.updatedAt = new Date()
    return true
  },
  
  /**
   * 取消收藏
   */
  unbookmarkResource: (resources: LearningResourceData, resourceId: string): boolean => {
    const index = resources.bookmarkedResources.indexOf(resourceId)
    if (index === -1) return false
    
    resources.bookmarkedResources.splice(index, 1)
    
    // 更新使用统计
    const stats = resources.usageStats.get(resourceId)
    if (stats) {
      stats.bookmarkCount = Math.max(0, stats.bookmarkCount - 1)
    }
    
    resources.updatedAt = new Date()
    return true
  },
  
  /**
   * 搜索资源
   */
  searchResources: (resources: LearningResourceData, query: string): ResourceMetadata[] => {
    const searchTerms = query.toLowerCase().split(' ')
    
    const results = resources.availableResources.filter(resource => {
      const searchableText = [
        resource.title,
        resource.description,
        ...resource.keywords,
        ...resource.subjects,
        resource.author.name
      ].join(' ').toLowerCase()
      
      return searchTerms.every(term => searchableText.includes(term))
    })
    
    // 记录搜索历史
    resources.searchHistory.push({
      query,
      timestamp: new Date(),
      resultsCount: results.length
    })
    
    // 限制搜索历史数量
    if (resources.searchHistory.length > 50) {
      resources.searchHistory = resources.searchHistory.slice(-50)
    }
    
    resources.updatedAt = new Date()
    return results
  },
  
  /**
   * 过滤资源
   */
  filterResources: (resources: LearningResourceData): ResourceMetadata[] => {
    const { filters } = resources
    
    return resources.availableResources.filter(resource => {
      // 类型过滤
      if (filters.types.length > 0 && !filters.types.includes(resource.type)) {
        return false
      }
      
      // 难度过滤
      if (filters.difficulties.length > 0 && !filters.difficulties.includes(resource.difficulty)) {
        return false
      }
      
      // 学科过滤
      if (filters.subjects.length > 0 && !filters.subjects.some(s => resource.subjects.includes(s))) {
        return false
      }
      
      // 语言过滤
      if (filters.languages.length > 0 && !filters.languages.includes(resource.language)) {
        return false
      }
      
      // 评分过滤
      const stats = resources.usageStats.get(resource.id)
      if (stats && stats.averageRating < filters.minRating) {
        return false
      }
      
      // 时长过滤
      if (resource.duration && resource.duration > filters.maxDuration * 60) {
        return false
      }
      
      return true
    })
  },
  
  /**
   * 生成个性化推荐
   */
  generateRecommendations: (resources: LearningResourceData): ResourceRecommendation[] => {
    const recommendations: ResourceRecommendation[] = []
    const { learningPreferences } = resources
    
    // 基于偏好的推荐
    const preferredResources = resources.availableResources.filter(resource => {
      return learningPreferences.preferredTypes.includes(resource.type) &&
             resource.difficulty === learningPreferences.preferredDifficulty &&
             resource.language === learningPreferences.preferredLanguage &&
             (!resource.duration || resource.duration <= learningPreferences.preferredDuration * 60)
    })
    
    preferredResources.slice(0, 5).forEach(resource => {
      recommendations.push({
        id: `rec_${Date.now()}_${resource.id}`,
        resourceId: resource.id,
        reason: '基于您的学习偏好推荐',
        algorithm: 'content_based',
        confidence: 0.8,
        weight: 1.0,
        recommendedAt: new Date(),
        viewed: false,
        adopted: false
      })
    })
    
    // 基于热门度的推荐
    const popularResources = resources.availableResources
      .filter(resource => {
        const stats = resources.usageStats.get(resource.id)
        return stats && stats.averageRating >= 4.0 && stats.totalViews > 10
      })
      .sort((a, b) => {
        const statsA = resources.usageStats.get(a.id)!
        const statsB = resources.usageStats.get(b.id)!
        return statsB.totalViews - statsA.totalViews
      })
      .slice(0, 3)
    
    popularResources.forEach(resource => {
      if (!recommendations.find(r => r.resourceId === resource.id)) {
        recommendations.push({
          id: `rec_${Date.now()}_${resource.id}`,
          resourceId: resource.id,
          reason: '热门推荐',
          algorithm: 'popularity',
          confidence: 0.6,
          weight: 0.8,
          recommendedAt: new Date(),
          viewed: false,
          adopted: false
        })
      }
    })
    
    resources.recommendations = recommendations
    resources.updatedAt = new Date()
    
    return recommendations
  },
  
  /**
   * 更新使用统计
   */
  updateUsageStats: (resources: LearningResourceData, resourceId: string): void => {
    let stats = resources.usageStats.get(resourceId)
    
    if (!stats) {
      stats = {
        totalViews: 0,
        uniqueViewers: 0,
        averageViewDuration: 0,
        completionRate: 0,
        averageRating: 0,
        ratingCount: 0,
        bookmarkCount: 0,
        shareCount: 0,
        downloadCount: 0,
        lastAccessTime: new Date()
      }
      resources.usageStats.set(resourceId, stats)
    }
    
    stats.totalViews++
    stats.lastAccessTime = new Date()
    
    // 检查是否是新的访问者
    const hasAccessedBefore = resources.accessedResources.some(a => 
      a.resourceId === resourceId && a.accessTime < new Date(Date.now() - 24 * 60 * 60 * 1000)
    )
    
    if (!hasAccessedBefore) {
      stats.uniqueViewers++
    }
  },
  
  /**
   * 获取推荐的学习路径资源
   */
  getPathResources: (resources: LearningResourceData, knowledgePointId: string): ResourceMetadata[] => {
    const resourceIds = resources.pathResourceMapping.get(knowledgePointId) || []
    return resources.availableResources.filter(r => resourceIds.includes(r.id))
  },
  
  /**
   * 下载离线资源
   */
  downloadForOffline: (resources: LearningResourceData, resourceId: string, localPath: string): void => {
    const resource = resources.availableResources.find(r => r.id === resourceId)
    if (!resource) return
    
    const existing = resources.offlineResources.find(o => o.resourceId === resourceId)
    if (existing) return
    
    resources.offlineResources.push({
      resourceId,
      downloadedAt: new Date(),
      localPath,
      size: resource.size,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30天后过期
    })
    
    // 更新使用统计
    const stats = resources.usageStats.get(resourceId)
    if (stats) {
      stats.downloadCount++
    }
    
    resources.updatedAt = new Date()
  }
}
