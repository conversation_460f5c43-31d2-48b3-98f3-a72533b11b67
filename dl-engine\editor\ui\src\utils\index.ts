/**
 * DL-Engine 编辑器UI工具函数
 */

import { ThemeToken, ResponsiveConfig, Breakpoint } from '../types'

/**
 * 类名工具函数
 */
export const classNames = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ')
}

/**
 * 样式工具函数
 */
export const styleUtils = {
  /**
   * 合并样式对象
   */
  mergeStyles: (...styles: (React.CSSProperties | undefined)[]): React.CSSProperties => {
    return Object.assign({}, ...styles.filter(Boolean))
  },
  
  /**
   * 转换尺寸值
   */
  toPx: (value: string | number): string => {
    if (typeof value === 'number') return `${value}px`
    return value
  },
  
  /**
   * 转换颜色值
   */
  toRgba: (color: string, alpha: number = 1): string => {
    // 简单的颜色转换，实际项目中可能需要更复杂的颜色处理库
    if (color.startsWith('#')) {
      const r = parseInt(color.slice(1, 3), 16)
      const g = parseInt(color.slice(3, 5), 16)
      const b = parseInt(color.slice(5, 7), 16)
      return `rgba(${r}, ${g}, ${b}, ${alpha})`
    }
    return color
  }
}

/**
 * 响应式工具函数
 */
export const responsiveUtils = {
  /**
   * 断点映射
   */
  breakpoints: {
    xs: 480,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200,
    xxl: 1600
  } as Record<Breakpoint, number>,
  
  /**
   * 获取当前断点
   */
  getCurrentBreakpoint: (): Breakpoint => {
    const width = window.innerWidth
    const { breakpoints } = responsiveUtils
    
    if (width >= breakpoints.xxl) return 'xxl'
    if (width >= breakpoints.xl) return 'xl'
    if (width >= breakpoints.lg) return 'lg'
    if (width >= breakpoints.md) return 'md'
    if (width >= breakpoints.sm) return 'sm'
    return 'xs'
  },
  
  /**
   * 解析响应式配置
   */
  parseResponsiveConfig: <T>(config: ResponsiveConfig<T>): T => {
    if (typeof config === 'object' && config !== null && !Array.isArray(config)) {
      const currentBreakpoint = responsiveUtils.getCurrentBreakpoint()
      const breakpointConfig = config as Partial<Record<Breakpoint, T>>
      
      // 按优先级查找配置
      const breakpointOrder: Breakpoint[] = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs']
      const currentIndex = breakpointOrder.indexOf(currentBreakpoint)
      
      for (let i = currentIndex; i < breakpointOrder.length; i++) {
        const bp = breakpointOrder[i]
        if (breakpointConfig[bp] !== undefined) {
          return breakpointConfig[bp]!
        }
      }
      
      // 如果没有找到，返回第一个可用的值
      for (const bp of breakpointOrder) {
        if (breakpointConfig[bp] !== undefined) {
          return breakpointConfig[bp]!
        }
      }
    }
    
    return config as T
  }
}

/**
 * 事件工具函数
 */
export const eventUtils = {
  /**
   * 防抖函数
   */
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout | null = null
    
    return (...args: Parameters<T>) => {
      if (timeout) clearTimeout(timeout)
      timeout = setTimeout(() => func(...args), wait)
    }
  },
  
  /**
   * 节流函数
   */
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle = false
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  },
  
  /**
   * 阻止事件冒泡
   */
  stopPropagation: (event: React.SyntheticEvent) => {
    event.stopPropagation()
  },
  
  /**
   * 阻止默认行为
   */
  preventDefault: (event: React.SyntheticEvent) => {
    event.preventDefault()
  }
}

/**
 * 数据工具函数
 */
export const dataUtils = {
  /**
   * 深拷贝
   */
  deepClone: <T>(obj: T): T => {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime()) as any
    if (obj instanceof Array) return obj.map(item => dataUtils.deepClone(item)) as any
    if (typeof obj === 'object') {
      const clonedObj = {} as any
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = dataUtils.deepClone(obj[key])
        }
      }
      return clonedObj
    }
    return obj
  },
  
  /**
   * 深度合并对象
   */
  deepMerge: <T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T => {
    if (!sources.length) return target
    const source = sources.shift()
    
    if (dataUtils.isObject(target) && dataUtils.isObject(source)) {
      for (const key in source) {
        if (dataUtils.isObject(source[key])) {
          if (!target[key]) Object.assign(target, { [key]: {} })
          dataUtils.deepMerge(target[key], source[key])
        } else {
          Object.assign(target, { [key]: source[key] })
        }
      }
    }
    
    return dataUtils.deepMerge(target, ...sources)
  },
  
  /**
   * 检查是否为对象
   */
  isObject: (item: any): item is Record<string, any> => {
    return item && typeof item === 'object' && !Array.isArray(item)
  },
  
  /**
   * 获取嵌套属性值
   */
  get: (obj: any, path: string, defaultValue?: any): any => {
    const keys = path.split('.')
    let result = obj
    
    for (const key of keys) {
      if (result == null || typeof result !== 'object') {
        return defaultValue
      }
      result = result[key]
    }
    
    return result !== undefined ? result : defaultValue
  },
  
  /**
   * 设置嵌套属性值
   */
  set: (obj: any, path: string, value: any): void => {
    const keys = path.split('.')
    const lastKey = keys.pop()!
    let current = obj
    
    for (const key of keys) {
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {}
      }
      current = current[key]
    }
    
    current[lastKey] = value
  }
}

/**
 * 格式化工具函数
 */
export const formatUtils = {
  /**
   * 格式化文件大小
   */
  fileSize: (bytes: number): string => {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`
  },
  
  /**
   * 格式化数字
   */
  number: (num: number, precision: number = 2): string => {
    return num.toFixed(precision)
  },
  
  /**
   * 格式化百分比
   */
  percentage: (num: number, precision: number = 1): string => {
    return `${(num * 100).toFixed(precision)}%`
  },
  
  /**
   * 格式化时间
   */
  time: (date: Date | string | number): string => {
    const d = new Date(date)
    return d.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  },
  
  /**
   * 格式化相对时间
   */
  relativeTime: (date: Date | string | number): string => {
    const now = new Date()
    const target = new Date(date)
    const diff = now.getTime() - target.getTime()
    
    const minute = 60 * 1000
    const hour = 60 * minute
    const day = 24 * hour
    const week = 7 * day
    const month = 30 * day
    const year = 365 * day
    
    if (diff < minute) return '刚刚'
    if (diff < hour) return `${Math.floor(diff / minute)}分钟前`
    if (diff < day) return `${Math.floor(diff / hour)}小时前`
    if (diff < week) return `${Math.floor(diff / day)}天前`
    if (diff < month) return `${Math.floor(diff / week)}周前`
    if (diff < year) return `${Math.floor(diff / month)}个月前`
    return `${Math.floor(diff / year)}年前`
  }
}

/**
 * 验证工具函数
 */
export const validationUtils = {
  /**
   * 验证邮箱
   */
  email: (email: string): boolean => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return regex.test(email)
  },
  
  /**
   * 验证URL
   */
  url: (url: string): boolean => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  },
  
  /**
   * 验证颜色值
   */
  color: (color: string): boolean => {
    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
    const rgbRegex = /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/
    const rgbaRegex = /^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)$/
    
    return hexRegex.test(color) || rgbRegex.test(color) || rgbaRegex.test(color)
  },
  
  /**
   * 验证数字范围
   */
  numberRange: (value: number, min?: number, max?: number): boolean => {
    if (min !== undefined && value < min) return false
    if (max !== undefined && value > max) return false
    return true
  }
}

/**
 * 存储工具函数
 */
export const storageUtils = {
  /**
   * 本地存储
   */
  local: {
    get: <T>(key: string, defaultValue?: T): T | null => {
      try {
        const item = localStorage.getItem(key)
        return item ? JSON.parse(item) : defaultValue || null
      } catch {
        return defaultValue || null
      }
    },
    
    set: (key: string, value: any): void => {
      try {
        localStorage.setItem(key, JSON.stringify(value))
      } catch (error) {
        console.warn('Failed to save to localStorage:', error)
      }
    },
    
    remove: (key: string): void => {
      try {
        localStorage.removeItem(key)
      } catch (error) {
        console.warn('Failed to remove from localStorage:', error)
      }
    }
  },
  
  /**
   * 会话存储
   */
  session: {
    get: <T>(key: string, defaultValue?: T): T | null => {
      try {
        const item = sessionStorage.getItem(key)
        return item ? JSON.parse(item) : defaultValue || null
      } catch {
        return defaultValue || null
      }
    },
    
    set: (key: string, value: any): void => {
      try {
        sessionStorage.setItem(key, JSON.stringify(value))
      } catch (error) {
        console.warn('Failed to save to sessionStorage:', error)
      }
    },
    
    remove: (key: string): void => {
      try {
        sessionStorage.removeItem(key)
      } catch (error) {
        console.warn('Failed to remove from sessionStorage:', error)
      }
    }
  }
}
