/**
 * Digital Learning Engine - 编辑器核心模块
 * 
 * 这个模块包含了DL-Engine编辑器的核心功能：
 * - 场景编辑器 (Scene Editor)
 * - 属性面板 (Properties Panel) 
 * - 资产浏览器 (Assets Browser)
 * - 层次结构 (Hierarchy)
 * - 工具栏系统 (Toolbar System)
 */

// 场景编辑器
export * from './scene-editor'

// 属性面板
export * from './properties'

// 资产浏览器
export * from './assets'

// 层次结构
export * from './hierarchy'

// 工具栏
export * from './toolbar'

// 编辑器容器和主要组件
export * from './components'

// 编辑器服务和状态管理
export * from './services'

// 编辑器系统
export * from './systems'

// 编辑器工具函数
export * from './utils'

// 编辑器类型定义
export * from './types'

// 编辑器常量
export * from './constants'

// 编辑器钩子
export * from './hooks'

// 编辑器上下文
export * from './contexts'

// 主编辑器组件
export { default as EditorContainer } from './components/EditorContainer'
export { default as EditorProvider } from './components/EditorProvider'

// 编辑器状态
export { EditorState } from './services/EditorState'

// 编辑器配置
export { EditorConfig } from './config/EditorConfig'
