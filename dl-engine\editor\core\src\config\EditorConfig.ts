/**
 * 编辑器配置
 * 
 * 定义编辑器的默认配置和设置
 */

import { EditorMode, EditorTool, EditorConfiguration } from '../types'

/**
 * 默认编辑器配置
 */
export const EditorConfig: EditorConfiguration = {
  // 网格设置
  grid: {
    visible: true,
    size: 10,
    divisions: 10,
    color: '#888888',
    centerLineColor: '#444444'
  },
  
  // 捕捉设置
  snap: {
    position: false,
    rotation: false,
    scale: false,
    positionIncrement: 1,
    rotationIncrement: 15,
    scaleIncrement: 0.1
  },
  
  // 自动保存间隔（毫秒）
  autoSaveInterval: 30000, // 30秒
  
  // 历史记录最大数量
  maxHistoryItems: 50,
  
  // 默认编辑器模式
  defaultMode: EditorMode.SELECT,
  
  // 默认工具
  defaultTool: EditorTool.SELECT
}

/**
 * 编辑器主题配置
 */
export const EditorTheme = {
  colors: {
    primary: '#007acc',
    secondary: '#1e1e1e',
    background: '#2a2a2a',
    surface: '#2d2d30',
    border: '#3c3c3c',
    text: '#ffffff',
    textSecondary: '#cccccc',
    textMuted: '#888888',
    success: '#4caf50',
    warning: '#ff9800',
    error: '#f44336',
    info: '#2196f3'
  },
  
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '12px',
    lg: '16px',
    xl: '24px',
    xxl: '32px'
  },
  
  borderRadius: {
    sm: '3px',
    md: '4px',
    lg: '6px'
  },
  
  shadows: {
    sm: '0 1px 3px rgba(0, 0, 0, 0.2)',
    md: '0 2px 6px rgba(0, 0, 0, 0.3)',
    lg: '0 4px 12px rgba(0, 0, 0, 0.4)'
  }
}

/**
 * 编辑器快捷键配置
 */
export const EditorHotkeys = {
  // 文件操作
  newScene: 'Ctrl+N',
  openScene: 'Ctrl+O',
  saveScene: 'Ctrl+S',
  saveAsScene: 'Ctrl+Shift+S',
  
  // 编辑操作
  undo: 'Ctrl+Z',
  redo: 'Ctrl+Y',
  copy: 'Ctrl+C',
  paste: 'Ctrl+V',
  delete: 'Delete',
  selectAll: 'Ctrl+A',
  
  // 工具切换
  selectTool: 'Q',
  moveTool: 'W',
  rotateTool: 'E',
  scaleTool: 'R',
  
  // 视图操作
  focusSelected: 'F',
  frameAll: 'A',
  toggleGrid: 'G',
  toggleWireframe: 'Alt+W',
  
  // 面板切换
  toggleHierarchy: 'H',
  toggleProperties: 'P',
  toggleAssets: 'Ctrl+1',
  toggleConsole: 'Ctrl+2'
}

/**
 * 编辑器性能配置
 */
export const EditorPerformance = {
  // 渲染设置
  maxFPS: 60,
  enableVSync: true,
  antialias: true,
  shadowMapSize: 2048,
  
  // LOD设置
  enableLOD: true,
  lodDistances: [10, 50, 100],
  
  // 剔除设置
  enableFrustumCulling: true,
  enableOcclusionCulling: false,
  
  // 内存管理
  maxTextureSize: 2048,
  textureCompression: true,
  geometryCompression: true
}

/**
 * 编辑器调试配置
 */
export const EditorDebug = {
  // 调试显示
  showFPS: false,
  showMemoryUsage: false,
  showRenderStats: false,
  showBoundingBoxes: false,
  showWireframe: false,
  
  // 日志级别
  logLevel: 'info', // 'debug', 'info', 'warn', 'error'
  
  // 性能监控
  enableProfiler: false,
  profileSampleRate: 60
}
