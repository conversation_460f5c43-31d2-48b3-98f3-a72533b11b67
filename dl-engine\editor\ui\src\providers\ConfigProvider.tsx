/**
 * DL-Engine 配置提供者
 * 
 * 为整个应用提供 Ant Design 配置
 */

import React from 'react'
import { ConfigProvider as AntdConfigProvider, App } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import enUS from 'antd/locale/en_US'
import { useTranslation } from 'react-i18next'
import { SupportedLanguage } from '../i18n'

/**
 * 配置提供者属性
 */
export interface ConfigProviderProps {
  children: React.ReactNode
}

/**
 * 语言映射
 */
const localeMap = {
  'zh-CN': zhCN,
  'en-US': enUS
}

/**
 * DL-Engine 配置提供者
 */
const ConfigProvider: React.FC<ConfigProviderProps> = ({ children }) => {
  const { i18n } = useTranslation()
  
  // 获取当前语言的 Ant Design 本地化配置
  const locale = localeMap[i18n.language as SupportedLanguage] || zhCN
  
  return (
    <AntdConfigProvider
      locale={locale}
      componentSize="middle"
      direction="ltr"
      theme={{
        cssVar: true,
        hashed: false
      }}
    >
      <App>
        {children}
      </App>
    </AntdConfigProvider>
  )
}

export default ConfigProvider
