/**
 * 图形工具函数测试
 */

import { describe, it, expect } from 'vitest'
import {
  createDefaultGraph,
  createNodeInstance,
  createConnection,
  validateGraph,
  isDataTypeCompatible,
  hasCircularDependency,
  removeNode,
  addNode,
  addConnection
} from '../utils/graphUtils'
import { NodeType, DataType } from '../types'

describe('graphUtils', () => {
  describe('createDefaultGraph', () => {
    it('should create a default graph with empty nodes and connections', () => {
      const graph = createDefaultGraph()
      
      expect(graph.id).toBeDefined()
      expect(graph.name).toBe('新脚本')
      expect(graph.nodes).toEqual([])
      expect(graph.connections).toEqual([])
      expect(graph.variables).toEqual([])
      expect(graph.parameters).toEqual([])
      expect(graph.createdAt).toBeInstanceOf(Date)
      expect(graph.updatedAt).toBeInstanceOf(Date)
    })
  })

  describe('createNodeInstance', () => {
    it('should create a node instance with correct properties', () => {
      const position = { x: 100, y: 200 }
      const data = { value: 42 }
      
      const node = createNodeInstance(NodeType.ADD, position, data)
      
      expect(node.id).toBeDefined()
      expect(node.type).toBe(NodeType.ADD)
      expect(node.position).toEqual(position)
      expect(node.data).toEqual(data)
      expect(node.selected).toBe(false)
      expect(node.disabled).toBe(false)
    })
  })

  describe('createConnection', () => {
    it('should create a connection with correct properties', () => {
      const connection = createConnection('node1', 'output1', 'node2', 'input1', true)
      
      expect(connection.id).toBeDefined()
      expect(connection.sourceNodeId).toBe('node1')
      expect(connection.sourceSocketId).toBe('output1')
      expect(connection.targetNodeId).toBe('node2')
      expect(connection.targetSocketId).toBe('input1')
      expect(connection.isExecution).toBe(true)
    })
  })

  describe('isDataTypeCompatible', () => {
    it('should return true for same types', () => {
      expect(isDataTypeCompatible(DataType.NUMBER, DataType.NUMBER)).toBe(true)
      expect(isDataTypeCompatible(DataType.STRING, DataType.STRING)).toBe(true)
    })

    it('should return true for ANY type', () => {
      expect(isDataTypeCompatible(DataType.ANY, DataType.NUMBER)).toBe(true)
      expect(isDataTypeCompatible(DataType.STRING, DataType.ANY)).toBe(true)
    })

    it('should return true for compatible conversions', () => {
      expect(isDataTypeCompatible(DataType.NUMBER, DataType.STRING)).toBe(true)
      expect(isDataTypeCompatible(DataType.BOOLEAN, DataType.STRING)).toBe(true)
    })

    it('should return false for incompatible types', () => {
      expect(isDataTypeCompatible(DataType.STRING, DataType.NUMBER)).toBe(false)
      expect(isDataTypeCompatible(DataType.OBJECT, DataType.BOOLEAN)).toBe(false)
    })
  })

  describe('validateGraph', () => {
    it('should validate an empty graph as valid', () => {
      const graph = createDefaultGraph()
      const result = validateGraph(graph)
      
      expect(result.valid).toBe(true)
      expect(result.errors).toEqual([])
    })

    it('should detect missing node IDs', () => {
      const graph = createDefaultGraph()
      graph.nodes = [
        {
          id: '',
          type: NodeType.ADD,
          definition: {} as any,
          position: { x: 0, y: 0 },
          data: {}
        }
      ]
      
      const result = validateGraph(graph)
      
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('节点缺少ID: add')
    })
  })

  describe('hasCircularDependency', () => {
    it('should return false for acyclic graph', () => {
      const graph = createDefaultGraph()
      
      // 创建简单的线性图: A -> B -> C
      const nodeA = createNodeInstance(NodeType.START, { x: 0, y: 0 })
      const nodeB = createNodeInstance(NodeType.ADD, { x: 100, y: 0 })
      const nodeC = createNodeInstance(NodeType.END, { x: 200, y: 0 })
      
      graph.nodes = [nodeA, nodeB, nodeC]
      graph.connections = [
        createConnection(nodeA.id, 'out', nodeB.id, 'in'),
        createConnection(nodeB.id, 'out', nodeC.id, 'in')
      ]
      
      expect(hasCircularDependency(graph)).toBe(false)
    })

    it('should return true for cyclic graph', () => {
      const graph = createDefaultGraph()
      
      // 创建循环图: A -> B -> A
      const nodeA = createNodeInstance(NodeType.ADD, { x: 0, y: 0 })
      const nodeB = createNodeInstance(NodeType.MULTIPLY, { x: 100, y: 0 })
      
      graph.nodes = [nodeA, nodeB]
      graph.connections = [
        createConnection(nodeA.id, 'out', nodeB.id, 'in'),
        createConnection(nodeB.id, 'out', nodeA.id, 'in')
      ]
      
      expect(hasCircularDependency(graph)).toBe(true)
    })
  })

  describe('removeNode', () => {
    it('should remove node and its connections', () => {
      const graph = createDefaultGraph()
      
      const nodeA = createNodeInstance(NodeType.START, { x: 0, y: 0 })
      const nodeB = createNodeInstance(NodeType.ADD, { x: 100, y: 0 })
      const nodeC = createNodeInstance(NodeType.END, { x: 200, y: 0 })
      
      graph.nodes = [nodeA, nodeB, nodeC]
      graph.connections = [
        createConnection(nodeA.id, 'out', nodeB.id, 'in'),
        createConnection(nodeB.id, 'out', nodeC.id, 'in')
      ]
      
      const updatedGraph = removeNode(graph, nodeB.id)
      
      expect(updatedGraph.nodes).toHaveLength(2)
      expect(updatedGraph.connections).toHaveLength(0)
      expect(updatedGraph.nodes.find(n => n.id === nodeB.id)).toBeUndefined()
    })
  })

  describe('addNode', () => {
    it('should add node to graph', () => {
      const graph = createDefaultGraph()
      const node = createNodeInstance(NodeType.ADD, { x: 0, y: 0 })
      
      const updatedGraph = addNode(graph, node)
      
      expect(updatedGraph.nodes).toHaveLength(1)
      expect(updatedGraph.nodes[0]).toEqual(node)
      expect(updatedGraph.updatedAt).toBeInstanceOf(Date)
    })
  })

  describe('addConnection', () => {
    it('should add connection to graph', () => {
      const graph = createDefaultGraph()
      const connection = createConnection('node1', 'out', 'node2', 'in')
      
      const updatedGraph = addConnection(graph, connection)
      
      expect(updatedGraph.connections).toHaveLength(1)
      expect(updatedGraph.connections[0]).toEqual(connection)
      expect(updatedGraph.updatedAt).toBeInstanceOf(Date)
    })
  })
})
