/**
 * 视口控制组件
 * 
 * 提供视口的控制按钮和设置
 */

import React from 'react'
import { useHookstate } from '@hookstate/core'
import { EditorState } from '../../services/EditorState'
import { ViewportType } from '../../types'

/**
 * 视口控制属性
 */
export interface ViewportControlsProps {
  className?: string
  style?: React.CSSProperties
}

/**
 * 视口控制组件
 */
const ViewportControls: React.FC<ViewportControlsProps> = ({
  className = '',
  style = {}
}) => {
  const editorState = useHookstate(EditorState)
  const viewportType = editorState.viewportType.get()
  const config = editorState.config.get()

  /**
   * 切换视口类型
   */
  const handleViewportTypeChange = (type: ViewportType) => {
    editorState.viewportType.set(type)
  }

  return (
    <div className={`absolute top-4 right-4 bg-black bg-opacity-50 rounded p-2 space-y-2 ${className}`} style={style}>
      {/* 视口类型切换 */}
      <div className="flex flex-col gap-1">
        <button
          className={`px-2 py-1 text-xs rounded ${viewportType === ViewportType.PERSPECTIVE ? 'bg-blue-600' : 'bg-gray-600 hover:bg-gray-500'}`}
          onClick={() => handleViewportTypeChange(ViewportType.PERSPECTIVE)}
        >
          透视
        </button>
        <button
          className={`px-2 py-1 text-xs rounded ${viewportType === ViewportType.ORTHOGRAPHIC ? 'bg-blue-600' : 'bg-gray-600 hover:bg-gray-500'}`}
          onClick={() => handleViewportTypeChange(ViewportType.ORTHOGRAPHIC)}
        >
          正交
        </button>
        <button
          className={`px-2 py-1 text-xs rounded ${viewportType === ViewportType.TOP ? 'bg-blue-600' : 'bg-gray-600 hover:bg-gray-500'}`}
          onClick={() => handleViewportTypeChange(ViewportType.TOP)}
        >
          顶视图
        </button>
        <button
          className={`px-2 py-1 text-xs rounded ${viewportType === ViewportType.FRONT ? 'bg-blue-600' : 'bg-gray-600 hover:bg-gray-500'}`}
          onClick={() => handleViewportTypeChange(ViewportType.FRONT)}
        >
          前视图
        </button>
        <button
          className={`px-2 py-1 text-xs rounded ${viewportType === ViewportType.RIGHT ? 'bg-blue-600' : 'bg-gray-600 hover:bg-gray-500'}`}
          onClick={() => handleViewportTypeChange(ViewportType.RIGHT)}
        >
          右视图
        </button>
      </div>

      {/* 显示设置 */}
      <div className="border-t border-gray-600 pt-2">
        <button
          className={`w-full px-2 py-1 text-xs rounded ${config.grid.visible ? 'bg-green-600' : 'bg-gray-600 hover:bg-gray-500'}`}
          onClick={() => {
            const current = editorState.config.grid.visible.get()
            editorState.config.grid.visible.set(!current)
          }}
        >
          网格
        </button>
      </div>
    </div>
  )
}

export default ViewportControls
