/**
 * 资产管理器核心类
 * 
 * 提供资产的增删改查、导入导出、搜索等核心功能
 */

import { EventEmitter } from 'events'
import { 
  Asset, 
  AssetFolder, 
  AssetType, 
  AssetStatus, 
  AssetConfig, 
  AssetSearchCriteria, 
  AssetSearchResult,
  AssetImportConfig,
  AssetProcessingTask,
  AssetStatistics,
  AssetEvent
} from '../types'
import AssetService from '../services/AssetService'
import ThumbnailService from '../services/ThumbnailService'
import MetadataService from '../services/MetadataService'
import { AssetState, AssetActions } from '../state/AssetState'

/**
 * 资产管理器配置
 */
export interface AssetManagerConfig extends AssetConfig {
  /** 是否启用自动索引 */
  enableAutoIndexing?: boolean
  
  /** 是否启用实时监控 */
  enableFileWatching?: boolean
  
  /** 是否启用缓存 */
  enableCaching?: boolean
}

/**
 * 资产管理器类
 */
export class AssetManager extends EventEmitter {
  private config: AssetManagerConfig
  private assetService: AssetService
  private thumbnailService: ThumbnailService
  private metadataService: MetadataService
  private isInitialized = false
  
  constructor(config: AssetManagerConfig) {
    super()
    this.config = config
    this.assetService = new AssetService(config)
    this.thumbnailService = new ThumbnailService(config)
    this.metadataService = new MetadataService(config)
  }
  
  /**
   * 初始化资产管理器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return
    }
    
    try {
      // 初始化服务
      await this.assetService.initialize()
      await this.thumbnailService.initialize()
      await this.metadataService.initialize()
      
      // 加载现有资产
      await this.loadAssets()
      
      // 启用文件监控
      if (this.config.enableFileWatching) {
        await this.startFileWatching()
      }
      
      this.isInitialized = true
      this.emit('initialized')
      
    } catch (error) {
      this.emit('error', error)
      throw error
    }
  }
  
  /**
   * 销毁资产管理器
   */
  async destroy(): Promise<void> {
    if (!this.isInitialized) {
      return
    }
    
    try {
      await this.stopFileWatching()
      await this.assetService.destroy()
      await this.thumbnailService.destroy()
      await this.metadataService.destroy()
      
      this.isInitialized = false
      this.emit('destroyed')
      
    } catch (error) {
      this.emit('error', error)
      throw error
    }
  }
  
  /**
   * 获取所有资产
   */
  async getAssets(): Promise<Asset[]> {
    return AssetState.assets.get()
  }
  
  /**
   * 根据ID获取资产
   */
  async getAsset(id: string): Promise<Asset | null> {
    const assets = AssetState.assets.get()
    return assets.find(asset => asset.id === id) || null
  }
  
  /**
   * 根据路径获取资产
   */
  async getAssetByPath(path: string): Promise<Asset | null> {
    const assets = AssetState.assets.get()
    return assets.find(asset => asset.path === path) || null
  }
  
  /**
   * 获取文件夹内容
   */
  async getFolderContents(folderId?: string): Promise<Asset[]> {
    const assets = AssetState.assets.get()
    return assets.filter(asset => asset.parentId === folderId)
  }
  
  /**
   * 创建文件夹
   */
  async createFolder(name: string, parentId?: string): Promise<AssetFolder> {
    const folder: AssetFolder = {
      id: this.generateId(),
      name,
      type: AssetType.FOLDER,
      status: AssetStatus.READY,
      path: this.buildPath(name, parentId),
      relativePath: this.buildRelativePath(name, parentId),
      parentId,
      children: [],
      expanded: false,
      isFavorite: false,
      isReadOnly: false,
      isHidden: false
    }
    
    AssetActions.addAsset(folder as Asset)
    
    const event: AssetEvent = {
      id: this.generateId(),
      type: 'created',
      assetId: folder.id,
      timestamp: new Date(),
      data: { type: 'folder' },
      description: `创建文件夹: ${name}`
    }
    
    this.emit('assetCreated', folder, event)
    return folder
  }
  
  /**
   * 导入资产
   */
  async importAssets(
    files: File[], 
    config?: Partial<AssetImportConfig>
  ): Promise<AssetProcessingTask[]> {
    const importConfig = { ...this.config.importConfig, ...config }
    const tasks: AssetProcessingTask[] = []
    
    for (const file of files) {
      const task: AssetProcessingTask = {
        id: this.generateId(),
        assetId: '',
        type: 'import',
        status: 'pending',
        progress: 0,
        description: `导入文件: ${file.name}`,
        config: importConfig
      }
      
      tasks.push(task)
      AssetActions.addProcessingTask(task)
      
      // 异步处理导入
      this.processImportTask(task, file, importConfig)
    }
    
    return tasks
  }
  
  /**
   * 删除资产
   */
  async deleteAsset(id: string): Promise<void> {
    const asset = await this.getAsset(id)
    if (!asset) {
      throw new Error(`资产不存在: ${id}`)
    }
    
    if (asset.isReadOnly) {
      throw new Error(`无法删除只读资产: ${asset.name}`)
    }
    
    // 如果是文件夹，递归删除子资产
    if (asset.type === AssetType.FOLDER) {
      const children = await this.getFolderContents(id)
      for (const child of children) {
        await this.deleteAsset(child.id)
      }
    }
    
    // 删除物理文件
    await this.assetService.deleteFile(asset.path)
    
    // 删除缩略图
    if (asset.thumbnail) {
      await this.thumbnailService.deleteThumbnail(asset.thumbnail.id)
    }
    
    // 从状态中移除
    AssetActions.removeAsset(id)
    
    const event: AssetEvent = {
      id: this.generateId(),
      type: 'deleted',
      assetId: id,
      timestamp: new Date(),
      data: { name: asset.name, type: asset.type },
      description: `删除资产: ${asset.name}`
    }
    
    this.emit('assetDeleted', asset, event)
  }
  
  /**
   * 重命名资产
   */
  async renameAsset(id: string, newName: string): Promise<void> {
    const asset = await this.getAsset(id)
    if (!asset) {
      throw new Error(`资产不存在: ${id}`)
    }
    
    if (asset.isReadOnly) {
      throw new Error(`无法重命名只读资产: ${asset.name}`)
    }
    
    const oldName = asset.name
    const newPath = this.buildPath(newName, asset.parentId)
    
    // 重命名物理文件
    await this.assetService.renameFile(asset.path, newPath)
    
    // 更新资产信息
    AssetActions.updateAsset(id, {
      name: newName,
      path: newPath,
      relativePath: this.buildRelativePath(newName, asset.parentId)
    })
    
    const event: AssetEvent = {
      id: this.generateId(),
      type: 'renamed',
      assetId: id,
      timestamp: new Date(),
      data: { oldName, newName },
      description: `重命名资产: ${oldName} -> ${newName}`
    }
    
    this.emit('assetRenamed', asset, event)
  }
  
  /**
   * 移动资产
   */
  async moveAsset(id: string, targetFolderId?: string): Promise<void> {
    const asset = await this.getAsset(id)
    if (!asset) {
      throw new Error(`资产不存在: ${id}`)
    }
    
    if (asset.isReadOnly) {
      throw new Error(`无法移动只读资产: ${asset.name}`)
    }
    
    const oldParentId = asset.parentId
    const newPath = this.buildPath(asset.name, targetFolderId)
    
    // 移动物理文件
    await this.assetService.moveFile(asset.path, newPath)
    
    // 更新资产信息
    AssetActions.updateAsset(id, {
      parentId: targetFolderId,
      path: newPath,
      relativePath: this.buildRelativePath(asset.name, targetFolderId)
    })
    
    const event: AssetEvent = {
      id: this.generateId(),
      type: 'moved',
      assetId: id,
      timestamp: new Date(),
      data: { oldParentId, newParentId: targetFolderId },
      description: `移动资产: ${asset.name}`
    }
    
    this.emit('assetMoved', asset, event)
  }
  
  /**
   * 搜索资产
   */
  async searchAssets(criteria: AssetSearchCriteria): Promise<AssetSearchResult> {
    const startTime = Date.now()
    const assets = AssetState.assets.get()
    
    let filteredAssets = assets
    
    // 应用过滤条件
    if (criteria.query) {
      const query = criteria.query.toLowerCase()
      filteredAssets = filteredAssets.filter(asset =>
        asset.name.toLowerCase().includes(query) ||
        asset.description?.toLowerCase().includes(query) ||
        asset.metadata.tags.some(tag => tag.toLowerCase().includes(query))
      )
    }
    
    if (criteria.types && criteria.types.length > 0) {
      filteredAssets = filteredAssets.filter(asset =>
        criteria.types!.includes(asset.type)
      )
    }
    
    if (criteria.tags && criteria.tags.length > 0) {
      filteredAssets = filteredAssets.filter(asset =>
        criteria.tags!.some(tag => asset.metadata.tags.includes(tag))
      )
    }
    
    if (criteria.folderId) {
      if (criteria.includeSubfolders) {
        // TODO: 实现递归文件夹搜索
        filteredAssets = filteredAssets.filter(asset =>
          asset.parentId === criteria.folderId
        )
      } else {
        filteredAssets = filteredAssets.filter(asset =>
          asset.parentId === criteria.folderId
        )
      }
    }
    
    if (criteria.favoritesOnly) {
      filteredAssets = filteredAssets.filter(asset => asset.isFavorite)
    }
    
    // 排序
    if (criteria.sortBy) {
      filteredAssets.sort((a, b) => {
        let comparison = 0
        
        switch (criteria.sortBy) {
          case 'name':
            comparison = a.name.localeCompare(b.name)
            break
          case 'type':
            comparison = a.type.localeCompare(b.type)
            break
          case 'size':
            comparison = a.metadata.fileSize - b.metadata.fileSize
            break
          case 'date':
            comparison = a.metadata.modifiedAt.getTime() - b.metadata.modifiedAt.getTime()
            break
        }
        
        return criteria.sortOrder === 'desc' ? -comparison : comparison
      })
    }
    
    // 分页
    const total = filteredAssets.length
    const page = criteria.pagination?.page || 1
    const pageSize = criteria.pagination?.pageSize || 50
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    
    const paginatedAssets = filteredAssets.slice(startIndex, endIndex)
    const searchTime = Date.now() - startTime
    
    return {
      assets: paginatedAssets,
      total,
      page,
      pageSize,
      searchTime
    }
  }
  
  /**
   * 获取资产统计信息
   */
  async getStatistics(): Promise<AssetStatistics> {
    const assets = AssetState.assets.get()
    
    const statistics: AssetStatistics = {
      totalAssets: assets.length,
      assetsByType: {} as Record<AssetType, number>,
      totalSize: 0,
      sizeByType: {} as Record<AssetType, number>,
      recentImports: 0,
      favoriteAssets: 0,
      errorAssets: 0,
      missingAssets: 0,
      popularTags: [],
      storageUsage: {
        used: 0,
        total: 0,
        percentage: 0
      }
    }
    
    // 统计各种数据
    const tagCounts = new Map<string, number>()
    const now = new Date()
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    
    for (const asset of assets) {
      // 按类型统计
      statistics.assetsByType[asset.type] = (statistics.assetsByType[asset.type] || 0) + 1
      statistics.sizeByType[asset.type] = (statistics.sizeByType[asset.type] || 0) + asset.metadata.fileSize
      
      // 总大小
      statistics.totalSize += asset.metadata.fileSize
      
      // 最近导入
      if (asset.metadata.importedAt > oneDayAgo) {
        statistics.recentImports++
      }
      
      // 收藏资产
      if (asset.isFavorite) {
        statistics.favoriteAssets++
      }
      
      // 错误资产
      if (asset.status === AssetStatus.ERROR) {
        statistics.errorAssets++
      }
      
      // 缺失资产
      if (asset.status === AssetStatus.MISSING) {
        statistics.missingAssets++
      }
      
      // 标签统计
      for (const tag of asset.metadata.tags) {
        tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1)
      }
    }
    
    // 热门标签
    statistics.popularTags = Array.from(tagCounts.entries())
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
    
    // 存储使用情况（简化实现）
    statistics.storageUsage = {
      used: statistics.totalSize,
      total: this.config.maxCacheSize || 1024 * 1024 * 1024, // 1GB 默认
      percentage: (statistics.totalSize / (this.config.maxCacheSize || 1024 * 1024 * 1024)) * 100
    }
    
    return statistics
  }
  
  /**
   * 私有方法：加载现有资产
   */
  private async loadAssets(): Promise<void> {
    try {
      const assets = await this.assetService.loadAssets()
      AssetActions.setAssets(assets)
    } catch (error) {
      console.error('加载资产失败:', error)
    }
  }
  
  /**
   * 私有方法：处理导入任务
   */
  private async processImportTask(
    task: AssetProcessingTask, 
    file: File, 
    config: AssetImportConfig
  ): Promise<void> {
    try {
      AssetActions.updateProcessingTask(task.id, { status: 'running', startedAt: new Date() })
      
      // TODO: 实现具体的导入逻辑
      // 1. 验证文件
      // 2. 生成资产信息
      // 3. 保存文件
      // 4. 生成缩略图
      // 5. 提取元数据
      
      AssetActions.updateProcessingTask(task.id, { 
        status: 'completed', 
        progress: 100,
        completedAt: new Date()
      })
      
    } catch (error) {
      AssetActions.updateProcessingTask(task.id, { 
        status: 'failed', 
        error: error instanceof Error ? error.message : String(error),
        completedAt: new Date()
      })
    }
  }
  
  /**
   * 私有方法：启动文件监控
   */
  private async startFileWatching(): Promise<void> {
    // TODO: 实现文件系统监控
  }
  
  /**
   * 私有方法：停止文件监控
   */
  private async stopFileWatching(): Promise<void> {
    // TODO: 停止文件系统监控
  }
  
  /**
   * 私有方法：生成ID
   */
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9)
  }
  
  /**
   * 私有方法：构建路径
   */
  private buildPath(name: string, parentId?: string): string {
    // TODO: 实现路径构建逻辑
    return parentId ? `${parentId}/${name}` : name
  }
  
  /**
   * 私有方法：构建相对路径
   */
  private buildRelativePath(name: string, parentId?: string): string {
    // TODO: 实现相对路径构建逻辑
    return this.buildPath(name, parentId)
  }
}

export default AssetManager
