/**
 * 可视化脚本编辑器主组件
 * 
 * 集成节点编辑器、节点面板、属性检查器等功能
 */

import React, { useState, useCallback, useRef } from 'react'
import { useHookstate } from '@hookstate/core'
import { useTranslation } from 'react-i18next'
import { Layout, Splitter } from '@dl-engine/editor-ui'
import NodeEditor from './NodeEditor'
import NodePalette from './NodePalette'
import NodeInspector from './NodeInspector'
import ScriptRunner from './ScriptRunner'
import DebugPanel from './DebugPanel'
import TutorialOverlay from '../education/components/TutorialOverlay'
import ProgressTracker from '../education/components/ProgressTracker'
import HintSystem from '../education/components/HintSystem'
import { 
  ScriptGraph, 
  NodeInstance, 
  Connection, 
  NodeEditorConfig, 
  EducationConfig,
  ExecutionState 
} from '../types'
import { createDefaultGraph } from '../utils/graphUtils'
import { VisualScriptState } from '../state/VisualScriptState'

/**
 * 可视化脚本编辑器属性
 */
export interface VisualScriptEditorProps {
  /** 初始脚本图形 */
  initialGraph?: ScriptGraph
  /** 编辑器配置 */
  config?: NodeEditorConfig
  /** 教育配置 */
  educationConfig?: EducationConfig
  /** 是否只读模式 */
  readonly?: boolean
  /** 是否启用调试 */
  enableDebug?: boolean
  /** 脚本保存回调 */
  onSave?: (graph: ScriptGraph) => void
  /** 脚本执行回调 */
  onExecute?: (graph: ScriptGraph) => void
  /** 错误回调 */
  onError?: (error: Error) => void
  /** 自定义类名 */
  className?: string
  /** 自定义样式 */
  style?: React.CSSProperties
}

/**
 * 可视化脚本编辑器组件
 */
const VisualScriptEditor: React.FC<VisualScriptEditorProps> = ({
  initialGraph,
  config = {},
  educationConfig,
  readonly = false,
  enableDebug = true,
  onSave,
  onExecute,
  onError,
  className = '',
  style = {}
}) => {
  const { t } = useTranslation()
  const editorRef = useRef<HTMLDivElement>(null)
  
  // 状态管理
  const visualScriptState = useHookstate(VisualScriptState)
  const [currentGraph, setCurrentGraph] = useState<ScriptGraph>(
    initialGraph || createDefaultGraph()
  )
  const [selectedNodes, setSelectedNodes] = useState<string[]>([])
  const [selectedConnections, setSelectedConnections] = useState<string[]>([])
  const [executionState, setExecutionState] = useState<ExecutionState>(ExecutionState.IDLE)
  const [showTutorial, setShowTutorial] = useState(false)
  const [showProgress, setShowProgress] = useState(false)
  
  // 获取编辑器状态
  const currentMode = visualScriptState.mode.get()
  const isEducationMode = visualScriptState.educationMode.get()
  const debugInfo = visualScriptState.debugInfo.get()
  
  /**
   * 处理节点添加
   */
  const handleNodeAdd = useCallback((nodeType: string, position: { x: number; y: number }) => {
    if (readonly) return
    
    // TODO: 实现节点添加逻辑
    console.log('添加节点:', nodeType, position)
  }, [readonly])
  
  /**
   * 处理节点删除
   */
  const handleNodeDelete = useCallback((nodeIds: string[]) => {
    if (readonly) return
    
    // TODO: 实现节点删除逻辑
    console.log('删除节点:', nodeIds)
  }, [readonly])
  
  /**
   * 处理节点选择
   */
  const handleNodeSelect = useCallback((nodeIds: string[]) => {
    setSelectedNodes(nodeIds)
  }, [])
  
  /**
   * 处理连接创建
   */
  const handleConnectionCreate = useCallback((connection: Partial<Connection>) => {
    if (readonly) return
    
    // TODO: 实现连接创建逻辑
    console.log('创建连接:', connection)
  }, [readonly])
  
  /**
   * 处理连接删除
   */
  const handleConnectionDelete = useCallback((connectionIds: string[]) => {
    if (readonly) return
    
    // TODO: 实现连接删除逻辑
    console.log('删除连接:', connectionIds)
  }, [readonly])
  
  /**
   * 处理脚本执行
   */
  const handleExecute = useCallback(() => {
    if (executionState === ExecutionState.RUNNING) {
      // 停止执行
      setExecutionState(ExecutionState.STOPPED)
    } else {
      // 开始执行
      setExecutionState(ExecutionState.RUNNING)
      onExecute?.(currentGraph)
    }
  }, [currentGraph, executionState, onExecute])
  
  /**
   * 处理脚本保存
   */
  const handleSave = useCallback(() => {
    onSave?.(currentGraph)
  }, [currentGraph, onSave])
  
  /**
   * 处理教程开始
   */
  const handleTutorialStart = useCallback(() => {
    setShowTutorial(true)
  }, [])
  
  /**
   * 处理教程结束
   */
  const handleTutorialEnd = useCallback(() => {
    setShowTutorial(false)
  }, [])
  
  /**
   * 处理进度显示
   */
  const handleProgressToggle = useCallback(() => {
    setShowProgress(!showProgress)
  }, [showProgress])
  
  return (
    <div 
      ref={editorRef}
      className={`visual-script-editor ${className}`}
      style={style}
    >
      <Layout direction="horizontal" className="h-full">
        {/* 左侧面板 - 节点面板 */}
        <Layout.Sider width={280} className="border-r border-gray-300">
          <NodePalette
            educationConfig={educationConfig}
            onNodeSelect={handleNodeAdd}
            readonly={readonly}
          />
        </Layout.Sider>
        
        {/* 中央编辑区域 */}
        <Layout.Content className="flex-1">
          <Layout direction="vertical" className="h-full">
            {/* 工具栏 */}
            <div className="toolbar border-b border-gray-300 p-2 flex items-center gap-2">
              <button
                className={`px-3 py-1 rounded ${
                  executionState === ExecutionState.RUNNING 
                    ? 'bg-red-500 text-white' 
                    : 'bg-green-500 text-white'
                }`}
                onClick={handleExecute}
                disabled={readonly}
              >
                {executionState === ExecutionState.RUNNING ? '停止' : '运行'}
              </button>
              
              <button
                className="px-3 py-1 bg-blue-500 text-white rounded"
                onClick={handleSave}
                disabled={readonly}
              >
                保存
              </button>
              
              {educationConfig && (
                <>
                  <button
                    className="px-3 py-1 bg-purple-500 text-white rounded"
                    onClick={handleTutorialStart}
                  >
                    教程
                  </button>
                  
                  <button
                    className="px-3 py-1 bg-orange-500 text-white rounded"
                    onClick={handleProgressToggle}
                  >
                    进度
                  </button>
                </>
              )}
            </div>
            
            {/* 节点编辑器 */}
            <div className="flex-1">
              <NodeEditor
                graph={currentGraph}
                config={config}
                selectedNodes={selectedNodes}
                selectedConnections={selectedConnections}
                executionState={executionState}
                readonly={readonly}
                onNodeAdd={handleNodeAdd}
                onNodeDelete={handleNodeDelete}
                onNodeSelect={handleNodeSelect}
                onConnectionCreate={handleConnectionCreate}
                onConnectionDelete={handleConnectionDelete}
              />
            </div>
          </Layout>
        </Layout.Content>
        
        {/* 右侧面板 */}
        <Layout.Sider width={320} className="border-l border-gray-300">
          <Layout direction="vertical" className="h-full">
            {/* 节点检查器 */}
            <div className="flex-1 border-b border-gray-300">
              <NodeInspector
                selectedNodes={selectedNodes.map(id => 
                  currentGraph.nodes.find(n => n.id === id)
                ).filter(Boolean) as NodeInstance[]}
                readonly={readonly}
                onNodeUpdate={(nodeId, data) => {
                  // TODO: 实现节点更新逻辑
                  console.log('更新节点:', nodeId, data)
                }}
              />
            </div>
            
            {/* 调试面板 */}
            {enableDebug && (
              <div className="h-64">
                <DebugPanel
                  executionState={executionState}
                  debugInfo={debugInfo}
                  onBreakpointToggle={(nodeId) => {
                    // TODO: 实现断点切换逻辑
                    console.log('切换断点:', nodeId)
                  }}
                  onStepExecute={() => {
                    // TODO: 实现单步执行逻辑
                    console.log('单步执行')
                  }}
                />
              </div>
            )}
          </Layout>
        </Layout.Sider>
      </Layout>
      
      {/* 脚本运行器 */}
      <ScriptRunner
        graph={currentGraph}
        executionState={executionState}
        onStateChange={setExecutionState}
        onError={onError}
      />
      
      {/* 教育功能覆盖层 */}
      {educationConfig && (
        <>
          {/* 教程覆盖层 */}
          {showTutorial && (
            <TutorialOverlay
              config={educationConfig}
              onComplete={handleTutorialEnd}
              onSkip={handleTutorialEnd}
            />
          )}
          
          {/* 进度跟踪器 */}
          {showProgress && (
            <ProgressTracker
              config={educationConfig}
              onClose={() => setShowProgress(false)}
            />
          )}
          
          {/* 提示系统 */}
          <HintSystem
            config={educationConfig}
            currentGraph={currentGraph}
            selectedNodes={selectedNodes}
          />
        </>
      )}
    </div>
  )
}

export default VisualScriptEditor
