/**
 * 可视化脚本编辑器状态管理
 * 
 * 使用 Hookstate 管理编辑器的全局状态
 */

import { hookstate, State } from '@hookstate/core'
import { 
  ScriptGraph, 
  NodeInstance, 
  Connection, 
  ExecutionState, 
  DebugInfo,
  EducationConfig,
  ProgressData 
} from '../types'

/**
 * 编辑器模式
 */
export enum EditorMode {
  EDIT = 'edit',
  DEBUG = 'debug',
  EXECUTE = 'execute',
  TUTORIAL = 'tutorial'
}

/**
 * 可视化脚本编辑器状态接口
 */
export interface VisualScriptStateType {
  /** 当前编辑器模式 */
  mode: EditorMode
  
  /** 是否为教育模式 */
  educationMode: boolean
  
  /** 当前脚本图形 */
  currentGraph: ScriptGraph | null
  
  /** 选中的节点ID列表 */
  selectedNodes: string[]
  
  /** 选中的连接ID列表 */
  selectedConnections: string[]
  
  /** 执行状态 */
  executionState: ExecutionState
  
  /** 调试信息 */
  debugInfo: DebugInfo
  
  /** 教育配置 */
  educationConfig: EducationConfig | null
  
  /** 进度数据 */
  progressData: ProgressData | null
  
  /** 撤销重做历史 */
  history: {
    undoStack: ScriptGraph[]
    redoStack: ScriptGraph[]
    maxSize: number
  }
  
  /** 编辑器设置 */
  settings: {
    showGrid: boolean
    gridSize: number
    snapToGrid: boolean
    autoSave: boolean
    autoSaveInterval: number
    enableHints: boolean
    enableTutorial: boolean
  }
  
  /** 错误状态 */
  error: {
    hasError: boolean
    message: string | null
    details: any
  }
  
  /** 加载状态 */
  loading: {
    isLoading: boolean
    message: string | null
  }
  
  /** UI状态 */
  ui: {
    showNodePalette: boolean
    showNodeInspector: boolean
    showDebugPanel: boolean
    showTutorial: boolean
    showProgress: boolean
    sidebarWidth: number
    inspectorWidth: number
  }
}

/**
 * 默认调试信息
 */
const defaultDebugInfo: DebugInfo = {
  breakpoints: new Set(),
  executionHistory: [],
  watchedVariables: new Set()
}

/**
 * 默认状态
 */
const defaultState: VisualScriptStateType = {
  mode: EditorMode.EDIT,
  educationMode: false,
  currentGraph: null,
  selectedNodes: [],
  selectedConnections: [],
  executionState: ExecutionState.IDLE,
  debugInfo: defaultDebugInfo,
  educationConfig: null,
  progressData: null,
  
  history: {
    undoStack: [],
    redoStack: [],
    maxSize: 50
  },
  
  settings: {
    showGrid: true,
    gridSize: 20,
    snapToGrid: true,
    autoSave: true,
    autoSaveInterval: 30000, // 30秒
    enableHints: true,
    enableTutorial: true
  },
  
  error: {
    hasError: false,
    message: null,
    details: null
  },
  
  loading: {
    isLoading: false,
    message: null
  },
  
  ui: {
    showNodePalette: true,
    showNodeInspector: true,
    showDebugPanel: false,
    showTutorial: false,
    showProgress: false,
    sidebarWidth: 280,
    inspectorWidth: 320
  }
}

/**
 * 可视化脚本状态实例
 */
export const VisualScriptState: State<VisualScriptStateType> = hookstate(defaultState)

/**
 * 状态操作函数
 */
export const VisualScriptActions = {
  /**
   * 设置编辑器模式
   */
  setMode: (mode: EditorMode) => {
    VisualScriptState.mode.set(mode)
  },
  
  /**
   * 设置教育模式
   */
  setEducationMode: (enabled: boolean) => {
    VisualScriptState.educationMode.set(enabled)
  },
  
  /**
   * 设置当前图形
   */
  setCurrentGraph: (graph: ScriptGraph | null) => {
    VisualScriptState.currentGraph.set(graph)
  },
  
  /**
   * 设置选中节点
   */
  setSelectedNodes: (nodeIds: string[]) => {
    VisualScriptState.selectedNodes.set(nodeIds)
  },
  
  /**
   * 添加选中节点
   */
  addSelectedNode: (nodeId: string) => {
    const current = VisualScriptState.selectedNodes.get()
    if (!current.includes(nodeId)) {
      VisualScriptState.selectedNodes.set([...current, nodeId])
    }
  },
  
  /**
   * 移除选中节点
   */
  removeSelectedNode: (nodeId: string) => {
    const current = VisualScriptState.selectedNodes.get()
    VisualScriptState.selectedNodes.set(current.filter(id => id !== nodeId))
  },
  
  /**
   * 清空选中节点
   */
  clearSelectedNodes: () => {
    VisualScriptState.selectedNodes.set([])
  },
  
  /**
   * 设置选中连接
   */
  setSelectedConnections: (connectionIds: string[]) => {
    VisualScriptState.selectedConnections.set(connectionIds)
  },
  
  /**
   * 设置执行状态
   */
  setExecutionState: (state: ExecutionState) => {
    VisualScriptState.executionState.set(state)
  },
  
  /**
   * 设置调试信息
   */
  setDebugInfo: (debugInfo: Partial<DebugInfo>) => {
    const current = VisualScriptState.debugInfo.get()
    VisualScriptState.debugInfo.set({ ...current, ...debugInfo })
  },
  
  /**
   * 添加断点
   */
  addBreakpoint: (nodeId: string) => {
    const current = VisualScriptState.debugInfo.get()
    const newBreakpoints = new Set(current.breakpoints)
    newBreakpoints.add(nodeId)
    VisualScriptState.debugInfo.breakpoints.set(newBreakpoints)
  },
  
  /**
   * 移除断点
   */
  removeBreakpoint: (nodeId: string) => {
    const current = VisualScriptState.debugInfo.get()
    const newBreakpoints = new Set(current.breakpoints)
    newBreakpoints.delete(nodeId)
    VisualScriptState.debugInfo.breakpoints.set(newBreakpoints)
  },
  
  /**
   * 切换断点
   */
  toggleBreakpoint: (nodeId: string) => {
    const current = VisualScriptState.debugInfo.get()
    if (current.breakpoints.has(nodeId)) {
      VisualScriptActions.removeBreakpoint(nodeId)
    } else {
      VisualScriptActions.addBreakpoint(nodeId)
    }
  },
  
  /**
   * 设置教育配置
   */
  setEducationConfig: (config: EducationConfig | null) => {
    VisualScriptState.educationConfig.set(config)
  },
  
  /**
   * 设置进度数据
   */
  setProgressData: (data: ProgressData | null) => {
    VisualScriptState.progressData.set(data)
  },
  
  /**
   * 添加到撤销栈
   */
  pushToUndoStack: (graph: ScriptGraph) => {
    const current = VisualScriptState.history.get()
    const newUndoStack = [...current.undoStack, graph]
    
    // 限制栈大小
    if (newUndoStack.length > current.maxSize) {
      newUndoStack.shift()
    }
    
    VisualScriptState.history.undoStack.set(newUndoStack)
    VisualScriptState.history.redoStack.set([]) // 清空重做栈
  },
  
  /**
   * 撤销操作
   */
  undo: () => {
    const current = VisualScriptState.history.get()
    const currentGraph = VisualScriptState.currentGraph.get()
    
    if (current.undoStack.length > 0 && currentGraph) {
      const previousGraph = current.undoStack[current.undoStack.length - 1]
      const newUndoStack = current.undoStack.slice(0, -1)
      const newRedoStack = [...current.redoStack, currentGraph]
      
      VisualScriptState.history.undoStack.set(newUndoStack)
      VisualScriptState.history.redoStack.set(newRedoStack)
      VisualScriptState.currentGraph.set(previousGraph)
    }
  },
  
  /**
   * 重做操作
   */
  redo: () => {
    const current = VisualScriptState.history.get()
    const currentGraph = VisualScriptState.currentGraph.get()
    
    if (current.redoStack.length > 0 && currentGraph) {
      const nextGraph = current.redoStack[current.redoStack.length - 1]
      const newRedoStack = current.redoStack.slice(0, -1)
      const newUndoStack = [...current.undoStack, currentGraph]
      
      VisualScriptState.history.undoStack.set(newUndoStack)
      VisualScriptState.history.redoStack.set(newRedoStack)
      VisualScriptState.currentGraph.set(nextGraph)
    }
  },
  
  /**
   * 设置错误状态
   */
  setError: (message: string, details?: any) => {
    VisualScriptState.error.set({
      hasError: true,
      message,
      details
    })
  },
  
  /**
   * 清除错误状态
   */
  clearError: () => {
    VisualScriptState.error.set({
      hasError: false,
      message: null,
      details: null
    })
  },
  
  /**
   * 设置加载状态
   */
  setLoading: (isLoading: boolean, message?: string) => {
    VisualScriptState.loading.set({
      isLoading,
      message: message || null
    })
  },
  
  /**
   * 切换UI面板显示
   */
  togglePanel: (panel: keyof VisualScriptStateType['ui']) => {
    const current = VisualScriptState.ui[panel].get()
    VisualScriptState.ui[panel].set(!current)
  },
  
  /**
   * 设置面板宽度
   */
  setPanelWidth: (panel: 'sidebarWidth' | 'inspectorWidth', width: number) => {
    VisualScriptState.ui[panel].set(width)
  },
  
  /**
   * 更新设置
   */
  updateSettings: (settings: Partial<VisualScriptStateType['settings']>) => {
    const current = VisualScriptState.settings.get()
    VisualScriptState.settings.set({ ...current, ...settings })
  }
}
