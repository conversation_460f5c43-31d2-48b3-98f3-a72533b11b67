/**
 * 场景编辑器主组件
 * 
 * 这是3D场景编辑的核心组件，集成了：
 * - Three.js 渲染器
 * - 相机控制
 * - 操作工具
 * - 选择系统
 */

import React, { useRef, useEffect, useCallback } from 'react'
import { useHookstate } from '@hookstate/core'
import { Canvas } from '@react-three/fiber'
import { EditorState } from '../../services/EditorState'
import { EditorMode, EditorTool } from '../../types'
import Viewport from './Viewport'
import ViewportControls from './ViewportControls'
import ViewportGrid from './ViewportGrid'
import TransformGizmo from '../gizmos/TransformGizmo'
import SelectionSystem from '../selection/SelectionSystem'
import CameraController from '../camera/CameraController'

/**
 * 场景编辑器属性
 */
export interface SceneEditorProps {
  /** 编辑器容器类名 */
  className?: string
  /** 编辑器样式 */
  style?: React.CSSProperties
  /** 是否启用网格 */
  showGrid?: boolean
  /** 是否启用辅助线 */
  showHelpers?: boolean
  /** 渲染器设置 */
  rendererSettings?: {
    antialias?: boolean
    alpha?: boolean
    shadowMap?: boolean
  }
}

/**
 * 场景编辑器组件
 */
const SceneEditor: React.FC<SceneEditorProps> = ({
  className = '',
  style = {},
  showGrid = true,
  showHelpers = true,
  rendererSettings = {
    antialias: true,
    alpha: true,
    shadowMap: true
  }
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const editorState = useHookstate(EditorState)
  
  // 获取编辑器状态
  const mode = editorState.mode.get()
  const tool = editorState.tool.get()
  const selection = editorState.selection.get()
  const config = editorState.config.get()
  
  /**
   * 初始化编辑器
   */
  useEffect(() => {
    if (canvasRef.current) {
      editorState.canvasRef.set(canvasRef.current)
    }
  }, [])
  
  /**
   * 处理键盘快捷键
   */
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // 防止在输入框中触发快捷键
    if (event.target instanceof HTMLInputElement || 
        event.target instanceof HTMLTextAreaElement) {
      return
    }
    
    switch (event.key) {
      case 'q':
      case 'Q':
        editorState.tool.set(EditorTool.SELECT)
        break
      case 'w':
      case 'W':
        editorState.tool.set(EditorTool.MOVE)
        break
      case 'e':
      case 'E':
        editorState.tool.set(EditorTool.ROTATE)
        break
      case 'r':
      case 'R':
        editorState.tool.set(EditorTool.SCALE)
        break
      case 'Delete':
      case 'Backspace':
        // 删除选中的对象
        if (selection.selectedEntities.length > 0) {
          // TODO: 实现删除逻辑
          console.log('删除选中对象:', selection.selectedEntities)
        }
        break
      case 'Escape':
        // 清空选择
        editorState.selection.selectedEntities.set([])
        editorState.selection.primaryEntity.set(undefined)
        break
      case 'f':
      case 'F':
        // 聚焦到选中对象
        if (selection.primaryEntity) {
          // TODO: 实现聚焦逻辑
          console.log('聚焦到对象:', selection.primaryEntity)
        }
        break
    }
  }, [selection, editorState])
  
  /**
   * 绑定键盘事件
   */
  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleKeyDown])
  
  /**
   * 渲染场景编辑器
   */
  return (
    <div 
      className={`scene-editor relative w-full h-full ${className}`}
      style={style}
    >
      {/* Three.js Canvas */}
      <Canvas
        ref={canvasRef}
        className="w-full h-full"
        gl={{
          antialias: rendererSettings.antialias,
          alpha: rendererSettings.alpha,
          shadowMap: rendererSettings.shadowMap
        }}
        camera={{
          position: [
            editorState.camera.position.x.get(),
            editorState.camera.position.y.get(),
            editorState.camera.position.z.get()
          ],
          fov: editorState.camera.fov.get(),
          near: editorState.camera.near.get(),
          far: editorState.camera.far.get()
        }}
        onCreated={({ gl, camera, scene }) => {
          // 设置渲染器
          gl.setClearColor('#2a2a2a')
          gl.shadowMap.enabled = rendererSettings.shadowMap || false
          
          // 设置相机
          camera.position.set(
            editorState.camera.position.x.get(),
            editorState.camera.position.y.get(),
            editorState.camera.position.z.get()
          )
          
          // 添加基础光照
          const ambientLight = new THREE.AmbientLight(0x404040, 0.4)
          scene.add(ambientLight)
          
          const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
          directionalLight.position.set(10, 10, 5)
          directionalLight.castShadow = true
          scene.add(directionalLight)
        }}
      >
        {/* 相机控制器 */}
        <CameraController />
        
        {/* 网格 */}
        {showGrid && config.grid.visible && (
          <ViewportGrid 
            size={config.grid.size}
            divisions={config.grid.divisions}
            color={config.grid.color}
            centerLineColor={config.grid.centerLineColor}
          />
        )}
        
        {/* 视口组件 */}
        <Viewport />
        
        {/* 选择系统 */}
        <SelectionSystem />
        
        {/* 变换工具 */}
        {selection.selectedEntities.length > 0 && (
          <TransformGizmo 
            mode={tool}
            visible={tool !== EditorTool.SELECT}
          />
        )}
        
        {/* 辅助对象 */}
        {showHelpers && (
          <>
            {/* 坐标轴辅助线 */}
            <axesHelper args={[5]} />
            
            {/* 网格辅助线 */}
            <gridHelper 
              args={[config.grid.size, config.grid.divisions]} 
              visible={config.grid.visible}
            />
          </>
        )}
      </Canvas>
      
      {/* 视口控制UI */}
      <ViewportControls />
      
      {/* 编辑器状态显示 */}
      <div className="absolute top-4 left-4 bg-black bg-opacity-50 text-white p-2 rounded text-sm">
        <div>模式: {mode}</div>
        <div>工具: {tool}</div>
        <div>选中: {selection.selectedEntities.length} 个对象</div>
      </div>
    </div>
  )
}

export default SceneEditor
