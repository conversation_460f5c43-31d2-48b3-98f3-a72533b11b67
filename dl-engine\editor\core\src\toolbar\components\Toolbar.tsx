/**
 * 编辑器工具栏组件
 * 
 * 提供编辑器的主要工具和操作按钮
 */

import React from 'react'
import { useHookstate } from '@hookstate/core'
import { EditorState, EditorActions } from '../../services/EditorState'
import { EditorMode, EditorTool } from '../../types'

/**
 * 工具栏属性
 */
export interface ToolbarProps {
  /** 工具栏类名 */
  className?: string
  /** 工具栏样式 */
  style?: React.CSSProperties
}

/**
 * 工具栏组件
 */
const Toolbar: React.FC<ToolbarProps> = ({
  className = '',
  style = {}
}) => {
  const editorState = useHookstate(EditorState)
  
  // 获取当前状态
  const mode = editorState.mode.get()
  const tool = editorState.tool.get()
  const scenePath = editorState.scenePath.get()
  const loading = editorState.loading.get()
  const selection = editorState.selection.get()
  
  /**
   * 处理工具切换
   */
  const handleToolChange = (newTool: EditorTool) => {
    EditorActions.setTool(newTool)
  }
  
  /**
   * 处理模式切换
   */
  const handleModeChange = (newMode: EditorMode) => {
    EditorActions.setMode(newMode)
  }
  
  /**
   * 处理保存场景
   */
  const handleSaveScene = () => {
    if (scenePath) {
      EditorActions.setLoading(true)
      // TODO: 实现保存逻辑
      console.log('保存场景:', scenePath)
      setTimeout(() => {
        EditorActions.setLoading(false)
      }, 1000)
    }
  }
  
  /**
   * 处理新建场景
   */
  const handleNewScene = () => {
    // TODO: 实现新建场景逻辑
    console.log('新建场景')
  }
  
  /**
   * 处理打开场景
   */
  const handleOpenScene = () => {
    // TODO: 实现打开场景逻辑
    console.log('打开场景')
  }
  
  /**
   * 处理撤销
   */
  const handleUndo = () => {
    // TODO: 实现撤销逻辑
    console.log('撤销')
  }
  
  /**
   * 处理重做
   */
  const handleRedo = () => {
    // TODO: 实现重做逻辑
    console.log('重做')
  }
  
  /**
   * 处理复制
   */
  const handleCopy = () => {
    if (selection.selectedEntities.length > 0) {
      // TODO: 实现复制逻辑
      console.log('复制选中对象')
    }
  }
  
  /**
   * 处理粘贴
   */
  const handlePaste = () => {
    // TODO: 实现粘贴逻辑
    console.log('粘贴')
  }
  
  /**
   * 处理删除
   */
  const handleDelete = () => {
    if (selection.selectedEntities.length > 0) {
      // TODO: 实现删除逻辑
      console.log('删除选中对象')
    }
  }
  
  return (
    <div className={`editor-toolbar ${className}`} style={style}>
      {/* 文件操作组 */}
      <div className="toolbar-group">
        <button 
          className="toolbar-button"
          onClick={handleNewScene}
          title="新建场景 (Ctrl+N)"
        >
          📄 新建
        </button>
        <button 
          className="toolbar-button"
          onClick={handleOpenScene}
          title="打开场景 (Ctrl+O)"
        >
          📁 打开
        </button>
        <button 
          className="toolbar-button"
          onClick={handleSaveScene}
          disabled={!scenePath || loading}
          title="保存场景 (Ctrl+S)"
        >
          💾 保存
        </button>
      </div>
      
      {/* 编辑操作组 */}
      <div className="toolbar-group">
        <button 
          className="toolbar-button"
          onClick={handleUndo}
          title="撤销 (Ctrl+Z)"
        >
          ↶ 撤销
        </button>
        <button 
          className="toolbar-button"
          onClick={handleRedo}
          title="重做 (Ctrl+Y)"
        >
          ↷ 重做
        </button>
      </div>
      
      {/* 剪贴板操作组 */}
      <div className="toolbar-group">
        <button 
          className="toolbar-button"
          onClick={handleCopy}
          disabled={selection.selectedEntities.length === 0}
          title="复制 (Ctrl+C)"
        >
          📋 复制
        </button>
        <button 
          className="toolbar-button"
          onClick={handlePaste}
          title="粘贴 (Ctrl+V)"
        >
          📄 粘贴
        </button>
        <button 
          className="toolbar-button"
          onClick={handleDelete}
          disabled={selection.selectedEntities.length === 0}
          title="删除 (Delete)"
        >
          🗑️ 删除
        </button>
      </div>
      
      {/* 工具选择组 */}
      <div className="toolbar-group">
        <button 
          className={`toolbar-button ${tool === EditorTool.SELECT ? 'active' : ''}`}
          onClick={() => handleToolChange(EditorTool.SELECT)}
          title="选择工具 (Q)"
        >
          🔍 选择
        </button>
        <button 
          className={`toolbar-button ${tool === EditorTool.MOVE ? 'active' : ''}`}
          onClick={() => handleToolChange(EditorTool.MOVE)}
          title="移动工具 (W)"
        >
          ↔️ 移动
        </button>
        <button 
          className={`toolbar-button ${tool === EditorTool.ROTATE ? 'active' : ''}`}
          onClick={() => handleToolChange(EditorTool.ROTATE)}
          title="旋转工具 (E)"
        >
          🔄 旋转
        </button>
        <button 
          className={`toolbar-button ${tool === EditorTool.SCALE ? 'active' : ''}`}
          onClick={() => handleToolChange(EditorTool.SCALE)}
          title="缩放工具 (R)"
        >
          📏 缩放
        </button>
      </div>
      
      {/* 视图模式组 */}
      <div className="toolbar-group">
        <button 
          className={`toolbar-button ${mode === EditorMode.SELECT ? 'active' : ''}`}
          onClick={() => handleModeChange(EditorMode.SELECT)}
          title="选择模式"
        >
          🎯 选择
        </button>
        <button 
          className={`toolbar-button ${mode === EditorMode.FLY ? 'active' : ''}`}
          onClick={() => handleModeChange(EditorMode.FLY)}
          title="飞行模式"
        >
          ✈️ 飞行
        </button>
      </div>
      
      {/* 网格和捕捉设置组 */}
      <div className="toolbar-group">
        <button 
          className={`toolbar-button ${editorState.config.grid.visible.get() ? 'active' : ''}`}
          onClick={() => {
            const current = editorState.config.grid.visible.get()
            editorState.config.grid.visible.set(!current)
          }}
          title="显示/隐藏网格"
        >
          ⊞ 网格
        </button>
        <button 
          className={`toolbar-button ${editorState.config.snap.position.get() ? 'active' : ''}`}
          onClick={() => {
            const current = editorState.config.snap.position.get()
            editorState.config.snap.position.set(!current)
          }}
          title="位置捕捉"
        >
          🧲 捕捉
        </button>
      </div>
      
      {/* 状态显示 */}
      <div className="toolbar-group ml-auto">
        <span className="text-sm text-gray-400">
          {loading ? '加载中...' : `选中: ${selection.selectedEntities.length}`}
        </span>
      </div>
    </div>
  )
}

export default Toolbar
