/**
 * DL-Engine 虚拟实验室组件
 * 支持科学实验模拟、安全实验环境、实验数据收集和分析
 */

import { defineComponent } from '../ComponentFunctions'

/**
 * 实验类型
 */
export enum ExperimentType {
  PHYSICS = 'physics',           // 物理实验
  CHEMISTRY = 'chemistry',       // 化学实验
  BIOLOGY = 'biology',          // 生物实验
  ENGINEERING = 'engineering',   // 工程实验
  COMPUTER_SCIENCE = 'computer_science', // 计算机科学
  MATHEMATICS = 'mathematics',   // 数学实验
  ENVIRONMENTAL = 'environmental' // 环境科学
}

/**
 * 实验状态
 */
export enum ExperimentStatus {
  NOT_STARTED = 'not_started',   // 未开始
  SETUP = 'setup',              // 设置中
  RUNNING = 'running',          // 运行中
  PAUSED = 'paused',            // 暂停
  COMPLETED = 'completed',       // 已完成
  FAILED = 'failed',            // 失败
  RESET = 'reset'               // 重置
}

/**
 * 安全级别
 */
export enum SafetyLevel {
  LOW = 1,      // 低风险
  MEDIUM = 2,   // 中等风险
  HIGH = 3,     // 高风险
  CRITICAL = 4  // 极高风险
}

/**
 * 实验设备
 */
export interface LabEquipment {
  /** 设备ID */
  id: string
  
  /** 设备名称 */
  name: string
  
  /** 设备类型 */
  type: string
  
  /** 设备描述 */
  description: string
  
  /** 3D模型路径 */
  modelPath: string
  
  /** 设备状态 */
  status: 'available' | 'in_use' | 'maintenance' | 'broken'
  
  /** 设备参数 */
  parameters: Record<string, any>
  
  /** 操作历史 */
  operationHistory: Array<{
    timestamp: Date
    operation: string
    parameters: Record<string, any>
    result: any
  }>
  
  /** 安全要求 */
  safetyRequirements: string[]
  
  /** 使用说明 */
  instructions: string[]
  
  /** 维护信息 */
  maintenance: {
    lastMaintenance: Date
    nextMaintenance: Date
    maintenanceLog: string[]
  }
}

/**
 * 实验材料
 */
export interface LabMaterial {
  /** 材料ID */
  id: string
  
  /** 材料名称 */
  name: string
  
  /** 材料类型 */
  type: string
  
  /** 化学式（如适用） */
  chemicalFormula?: string
  
  /** 物理属性 */
  physicalProperties: {
    density?: number
    meltingPoint?: number
    boilingPoint?: number
    color?: string
    state?: 'solid' | 'liquid' | 'gas' | 'plasma'
  }
  
  /** 安全信息 */
  safetyInfo: {
    hazardLevel: SafetyLevel
    hazardSymbols: string[]
    safetyPrecautions: string[]
    firstAid: string[]
  }
  
  /** 可用数量 */
  availableQuantity: number
  
  /** 单位 */
  unit: string
  
  /** 存储条件 */
  storageConditions: string[]
}

/**
 * 实验步骤
 */
export interface ExperimentStep {
  /** 步骤ID */
  id: string
  
  /** 步骤序号 */
  order: number
  
  /** 步骤标题 */
  title: string
  
  /** 步骤描述 */
  description: string
  
  /** 所需设备 */
  requiredEquipment: string[]
  
  /** 所需材料 */
  requiredMaterials: Array<{
    materialId: string
    quantity: number
    unit: string
  }>
  
  /** 操作指令 */
  instructions: string[]
  
  /** 预期结果 */
  expectedResults: string[]
  
  /** 安全注意事项 */
  safetyNotes: string[]
  
  /** 估计时间（分钟） */
  estimatedTime: number
  
  /** 是否完成 */
  completed: boolean
  
  /** 实际结果 */
  actualResults?: any[]
  
  /** 完成时间 */
  completionTime?: Date
  
  /** 步骤评分 */
  score?: number
}

/**
 * 实验数据
 */
export interface ExperimentData {
  /** 数据ID */
  id: string
  
  /** 数据类型 */
  type: 'measurement' | 'observation' | 'calculation' | 'image' | 'video'
  
  /** 数据值 */
  value: any
  
  /** 数据单位 */
  unit?: string
  
  /** 测量时间 */
  timestamp: Date
  
  /** 测量设备 */
  equipment?: string
  
  /** 数据精度 */
  precision?: number
  
  /** 数据来源 */
  source: string
  
  /** 数据标签 */
  tags: string[]
  
  /** 数据注释 */
  notes?: string
}

/**
 * 虚拟实验室数据
 */
export interface VirtualLabData {
  /** 实验室ID */
  labId: string
  
  /** 实验室名称 */
  name: string
  
  /** 实验室描述 */
  description: string
  
  /** 实验类型 */
  experimentType: ExperimentType
  
  /** 当前实验状态 */
  status: ExperimentStatus
  
  /** 学习者ID */
  learnerId: string
  
  /** 课程ID */
  courseId: string
  
  /** 实验目标 */
  objectives: string[]
  
  /** 可用设备 */
  equipment: LabEquipment[]
  
  /** 可用材料 */
  materials: LabMaterial[]
  
  /** 实验步骤 */
  steps: ExperimentStep[]
  
  /** 当前步骤 */
  currentStep: number
  
  /** 实验数据 */
  experimentData: ExperimentData[]
  
  /** 环境条件 */
  environmentConditions: {
    temperature: number
    humidity: number
    pressure: number
    lighting: number
    ventilation: boolean
  }
  
  /** 安全状态 */
  safetyStatus: {
    overallLevel: SafetyLevel
    activeHazards: string[]
    safetyEquipment: string[]
    emergencyProcedures: string[]
  }
  
  /** 实验配置 */
  configuration: {
    allowReset: boolean
    allowSkipSteps: boolean
    enableHints: boolean
    enableAutoSave: boolean
    maxAttempts: number
    timeLimit?: number // 分钟
  }
  
  /** 实验统计 */
  statistics: {
    startTime?: Date
    endTime?: Date
    totalTime: number // 分钟
    stepsCompleted: number
    totalSteps: number
    errorsCount: number
    hintsUsed: number
    resetCount: number
    score: number
  }
  
  /** 实验报告 */
  report?: {
    summary: string
    results: string[]
    conclusions: string[]
    improvements: string[]
    attachments: string[]
  }
  
  /** 创建时间 */
  createdAt: Date
  
  /** 更新时间 */
  updatedAt: Date
}

/**
 * 虚拟实验室组件
 */
export const VirtualLabComponent = defineComponent({
  name: 'VirtualLab',
  schema: {
    labId: '',
    name: '',
    description: '',
    experimentType: ExperimentType.PHYSICS,
    status: ExperimentStatus.NOT_STARTED,
    learnerId: '',
    courseId: '',
    objectives: [] as string[],
    equipment: [] as LabEquipment[],
    materials: [] as LabMaterial[],
    steps: [] as ExperimentStep[],
    currentStep: 0,
    experimentData: [] as ExperimentData[],
    environmentConditions: {
      temperature: 20,
      humidity: 50,
      pressure: 101.3,
      lighting: 80,
      ventilation: true
    },
    safetyStatus: {
      overallLevel: SafetyLevel.LOW,
      activeHazards: [],
      safetyEquipment: [],
      emergencyProcedures: []
    },
    configuration: {
      allowReset: true,
      allowSkipSteps: false,
      enableHints: true,
      enableAutoSave: true,
      maxAttempts: 3,
      timeLimit: undefined
    },
    statistics: {
      startTime: undefined,
      endTime: undefined,
      totalTime: 0,
      stepsCompleted: 0,
      totalSteps: 0,
      errorsCount: 0,
      hintsUsed: 0,
      resetCount: 0,
      score: 0
    },
    report: undefined,
    createdAt: new Date(),
    updatedAt: new Date()
  } as VirtualLabData,
  
  onAdd: (entity, component) => {
    console.log(`Virtual lab created: ${component.name}`)
    component.createdAt = new Date()
    component.updatedAt = new Date()
  },
  
  onRemove: (entity, component) => {
    console.log(`Virtual lab removed: ${component.name}`)
  },
  
  onSet: (entity, component) => {
    component.updatedAt = new Date()
    
    // 更新统计信息
    VirtualLabUtils.updateStatistics(component)
  }
})

/**
 * 虚拟实验室工具函数
 */
export const VirtualLabUtils = {
  /**
   * 开始实验
   */
  startExperiment: (lab: VirtualLabData): void => {
    lab.status = ExperimentStatus.SETUP
    lab.statistics.startTime = new Date()
    lab.currentStep = 0
    lab.updatedAt = new Date()
    
    console.log(`Experiment started: ${lab.name}`)
  },
  
  /**
   * 完成当前步骤
   */
  completeStep: (lab: VirtualLabData, stepResults?: any[]): boolean => {
    if (lab.currentStep >= lab.steps.length) return false
    
    const step = lab.steps[lab.currentStep]
    step.completed = true
    step.completionTime = new Date()
    step.actualResults = stepResults
    
    lab.statistics.stepsCompleted++
    lab.currentStep++
    
    // 检查是否完成所有步骤
    if (lab.currentStep >= lab.steps.length) {
      VirtualLabUtils.completeExperiment(lab)
    }
    
    lab.updatedAt = new Date()
    return true
  },
  
  /**
   * 完成实验
   */
  completeExperiment: (lab: VirtualLabData): void => {
    lab.status = ExperimentStatus.COMPLETED
    lab.statistics.endTime = new Date()
    
    if (lab.statistics.startTime) {
      lab.statistics.totalTime = (lab.statistics.endTime.getTime() - lab.statistics.startTime.getTime()) / (1000 * 60)
    }
    
    // 计算总分
    VirtualLabUtils.calculateScore(lab)
    
    console.log(`Experiment completed: ${lab.name}`)
  },
  
  /**
   * 重置实验
   */
  resetExperiment: (lab: VirtualLabData): void => {
    lab.status = ExperimentStatus.RESET
    lab.currentStep = 0
    lab.statistics.resetCount++
    
    // 重置所有步骤
    lab.steps.forEach(step => {
      step.completed = false
      step.actualResults = undefined
      step.completionTime = undefined
      step.score = undefined
    })
    
    // 清空实验数据
    lab.experimentData = []
    
    // 重置设备状态
    lab.equipment.forEach(equipment => {
      equipment.status = 'available'
      equipment.operationHistory = []
    })
    
    lab.updatedAt = new Date()
    console.log(`Experiment reset: ${lab.name}`)
  },
  
  /**
   * 记录实验数据
   */
  recordData: (lab: VirtualLabData, data: Omit<ExperimentData, 'id' | 'timestamp'>): void => {
    const experimentData: ExperimentData = {
      id: `data_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      ...data
    }
    
    lab.experimentData.push(experimentData)
    lab.updatedAt = new Date()
  },
  
  /**
   * 使用设备
   */
  useEquipment: (lab: VirtualLabData, equipmentId: string, operation: string, parameters: Record<string, any>): any => {
    const equipment = lab.equipment.find(e => e.id === equipmentId)
    if (!equipment) {
      console.error(`Equipment not found: ${equipmentId}`)
      return null
    }
    
    if (equipment.status !== 'available') {
      console.error(`Equipment not available: ${equipment.name}`)
      return null
    }
    
    // 模拟设备操作
    equipment.status = 'in_use'
    
    const operationResult = VirtualLabUtils.simulateEquipmentOperation(equipment, operation, parameters)
    
    // 记录操作历史
    equipment.operationHistory.push({
      timestamp: new Date(),
      operation,
      parameters,
      result: operationResult
    })
    
    equipment.status = 'available'
    lab.updatedAt = new Date()
    
    return operationResult
  },
  
  /**
   * 模拟设备操作
   */
  simulateEquipmentOperation: (equipment: LabEquipment, operation: string, parameters: Record<string, any>): any => {
    // 这里应该根据设备类型和操作类型进行具体的模拟
    // 简化示例：
    switch (equipment.type) {
      case 'thermometer':
        return { temperature: 20 + Math.random() * 10, unit: '°C' }
      case 'scale':
        return { weight: parameters.expectedWeight || Math.random() * 100, unit: 'g' }
      case 'microscope':
        return { magnification: parameters.magnification || 100, image: 'microscope_view.jpg' }
      default:
        return { status: 'operation_completed', data: parameters }
    }
  },
  
  /**
   * 检查安全状态
   */
  checkSafety: (lab: VirtualLabData): void => {
    const hazards: string[] = []
    let maxSafetyLevel = SafetyLevel.LOW
    
    // 检查材料安全
    lab.materials.forEach(material => {
      if (material.safetyInfo.hazardLevel > SafetyLevel.LOW) {
        hazards.push(...material.safetyInfo.hazardSymbols)
        maxSafetyLevel = Math.max(maxSafetyLevel, material.safetyInfo.hazardLevel)
      }
    })
    
    // 检查环境条件
    if (lab.environmentConditions.temperature > 40 || lab.environmentConditions.temperature < 0) {
      hazards.push('extreme_temperature')
      maxSafetyLevel = Math.max(maxSafetyLevel, SafetyLevel.MEDIUM)
    }
    
    if (!lab.environmentConditions.ventilation) {
      hazards.push('poor_ventilation')
      maxSafetyLevel = Math.max(maxSafetyLevel, SafetyLevel.MEDIUM)
    }
    
    lab.safetyStatus.activeHazards = [...new Set(hazards)]
    lab.safetyStatus.overallLevel = maxSafetyLevel
    lab.updatedAt = new Date()
  },
  
  /**
   * 计算实验得分
   */
  calculateScore: (lab: VirtualLabData): void => {
    let totalScore = 0
    let maxScore = 0
    
    // 基于步骤完成情况计算分数
    lab.steps.forEach(step => {
      maxScore += 100
      if (step.completed) {
        totalScore += step.score || 80 // 默认80分
      }
    })
    
    // 扣除错误和提示使用的分数
    totalScore -= lab.statistics.errorsCount * 5
    totalScore -= lab.statistics.hintsUsed * 2
    
    // 时间奖励/惩罚
    if (lab.configuration.timeLimit && lab.statistics.totalTime > 0) {
      const timeRatio = lab.statistics.totalTime / lab.configuration.timeLimit
      if (timeRatio < 0.8) {
        totalScore += 10 // 提前完成奖励
      } else if (timeRatio > 1.2) {
        totalScore -= 10 // 超时惩罚
      }
    }
    
    lab.statistics.score = Math.max(0, Math.min(100, (totalScore / maxScore) * 100))
  },
  
  /**
   * 更新统计信息
   */
  updateStatistics: (lab: VirtualLabData): void => {
    lab.statistics.totalSteps = lab.steps.length
    lab.statistics.stepsCompleted = lab.steps.filter(s => s.completed).length
    
    if (lab.statistics.startTime && lab.status === ExperimentStatus.RUNNING) {
      lab.statistics.totalTime = (Date.now() - lab.statistics.startTime.getTime()) / (1000 * 60)
    }
  },
  
  /**
   * 生成实验报告
   */
  generateReport: (lab: VirtualLabData): void => {
    const completedSteps = lab.steps.filter(s => s.completed)
    const results = lab.experimentData.map(d => `${d.type}: ${d.value} ${d.unit || ''}`).slice(0, 10)
    
    lab.report = {
      summary: `实验 "${lab.name}" 已完成，共完成 ${completedSteps.length}/${lab.steps.length} 个步骤，得分 ${lab.statistics.score.toFixed(1)} 分。`,
      results,
      conclusions: [
        '实验目标基本达成',
        '数据收集完整',
        '操作流程规范'
      ],
      improvements: [
        '可以提高操作精度',
        '注意安全规范',
        '加强数据分析'
      ],
      attachments: []
    }
    
    lab.updatedAt = new Date()
  }
}
