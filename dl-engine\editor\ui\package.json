{"name": "@dl-engine/editor-ui", "version": "1.0.0", "description": "Digital Learning Engine - 编辑器UI组件库", "main": "dist/index.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "scripts": {"build": "vite build", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "dev": "vite --mode development", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@dl-engine/shared-common": "workspace:*", "antd": "5.0.0", "@ant-design/icons": "^5.0.0", "@ant-design/colors": "^7.0.0", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "11.16.6", "i18next": "21.6.16", "classnames": "^2.3.2", "lodash": "4.17.21", "@hookstate/core": "^4.0.1"}, "devDependencies": {"@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@types/lodash": "^4.17.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@vitest/coverage-v8": "^2.0.0", "@storybook/react": "^8.0.0", "@storybook/react-vite": "^8.0.0", "@storybook/addon-essentials": "^8.0.0", "@storybook/addon-docs": "^8.0.0", "eslint": "^9.0.0", "rimraf": "^6.0.0", "typescript": "5.6.3", "vite": "5.4.8", "vite-plugin-dts": "^4.0.0", "vitest": "^2.0.0", "storybook": "^8.0.0"}, "peerDependencies": {"react": "18.2.0", "react-dom": "18.2.0"}, "engines": {"node": ">=22.0.0"}}