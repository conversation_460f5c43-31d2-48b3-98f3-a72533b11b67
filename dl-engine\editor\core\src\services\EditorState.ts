/**
 * 编辑器状态管理
 * 
 * 使用 Hookstate 进行响应式状态管理，替代传统的 Redux
 */

import { hookstate, State } from '@hookstate/core'
import { Entity } from '@dl-engine/engine-ecs'
import { 
  EditorMode, 
  EditorTool, 
  ViewportType, 
  SelectionState, 
  TransformState, 
  CameraState, 
  EditorConfiguration,
  HistoryItem
} from '../types'

/**
 * 编辑器主状态接口
 */
export interface EditorStateType {
  /** 当前编辑器模式 */
  mode: EditorMode
  
  /** 当前工具 */
  tool: EditorTool
  
  /** 当前视口类型 */
  viewportType: ViewportType
  
  /** 是否启用UI */
  uiEnabled: boolean
  
  /** 是否正在加载 */
  loading: boolean
  
  /** 当前场景路径 */
  scenePath: string | null
  
  /** 当前场景名称 */
  sceneName: string | null
  
  /** 当前项目名称 */
  projectName: string | null
  
  /** 场景资产ID */
  sceneAssetID: string | null
  
  /** 根实体 */
  rootEntity: Entity | null
  
  /** Canvas引用 */
  canvasRef: HTMLCanvasElement | null
  
  /** 选择状态 */
  selection: SelectionState
  
  /** 变换状态 */
  transform: TransformState
  
  /** 相机状态 */
  camera: CameraState
  
  /** 编辑器配置 */
  config: EditorConfiguration
  
  /** 历史记录 */
  history: {
    items: HistoryItem[]
    currentIndex: number
    maxItems: number
  }
  
  /** 错误状态 */
  error: {
    hasError: boolean
    message: string | null
    details: any
  }
  
  /** 警告状态 */
  warning: {
    hasWarning: boolean
    message: string | null
  }
  
  /** 活动的下方面板 */
  activeLowerPanel: string | null
  
  /** 面板可见性 */
  panels: {
    hierarchy: boolean
    properties: boolean
    assets: boolean
    materials: boolean
    console: boolean
    inspector: boolean
  }
}

/**
 * 默认编辑器状态
 */
const defaultEditorState: EditorStateType = {
  mode: EditorMode.SELECT,
  tool: EditorTool.SELECT,
  viewportType: ViewportType.PERSPECTIVE,
  uiEnabled: true,
  loading: false,
  scenePath: null,
  sceneName: null,
  projectName: null,
  sceneAssetID: null,
  rootEntity: null,
  canvasRef: null,
  
  selection: {
    selectedEntities: [],
    primaryEntity: undefined
  },
  
  transform: {
    position: { x: 0, y: 0, z: 0 } as any,
    rotation: { x: 0, y: 0, z: 0, w: 1 } as any,
    euler: { x: 0, y: 0, z: 0 } as any,
    scale: { x: 1, y: 1, z: 1 } as any
  },
  
  camera: {
    position: { x: 0, y: 5, z: 10 } as any,
    target: { x: 0, y: 0, z: 0 } as any,
    rotation: { x: 0, y: 0, z: 0, w: 1 } as any,
    fov: 75,
    near: 0.1,
    far: 1000
  },
  
  config: {
    grid: {
      visible: true,
      size: 10,
      divisions: 10,
      color: '#888888',
      centerLineColor: '#444444'
    },
    snap: {
      position: false,
      rotation: false,
      scale: false,
      positionIncrement: 1,
      rotationIncrement: 15,
      scaleIncrement: 0.1
    },
    autoSaveInterval: 30000, // 30秒
    maxHistoryItems: 50,
    defaultMode: EditorMode.SELECT,
    defaultTool: EditorTool.SELECT
  },
  
  history: {
    items: [],
    currentIndex: -1,
    maxItems: 50
  },
  
  error: {
    hasError: false,
    message: null,
    details: null
  },
  
  warning: {
    hasWarning: false,
    message: null
  },
  
  activeLowerPanel: null,
  
  panels: {
    hierarchy: true,
    properties: true,
    assets: true,
    materials: false,
    console: false,
    inspector: true
  }
}

/**
 * 编辑器状态实例
 */
export const EditorState: State<EditorStateType> = hookstate(defaultEditorState)

/**
 * 编辑器状态操作函数
 */
export const EditorActions = {
  /**
   * 设置编辑器模式
   */
  setMode: (mode: EditorMode) => {
    EditorState.mode.set(mode)
  },
  
  /**
   * 设置编辑器工具
   */
  setTool: (tool: EditorTool) => {
    EditorState.tool.set(tool)
  },
  
  /**
   * 设置场景信息
   */
  setScene: (scenePath: string, sceneName: string, projectName: string, sceneAssetID: string) => {
    EditorState.scenePath.set(scenePath)
    EditorState.sceneName.set(sceneName)
    EditorState.projectName.set(projectName)
    EditorState.sceneAssetID.set(sceneAssetID)
  },
  
  /**
   * 设置选择状态
   */
  setSelection: (entities: Entity[], primary?: Entity) => {
    EditorState.selection.set({
      selectedEntities: entities,
      primaryEntity: primary || entities[0]
    })
  },
  
  /**
   * 添加到选择
   */
  addToSelection: (entity: Entity) => {
    const current = EditorState.selection.get()
    if (!current.selectedEntities.includes(entity)) {
      EditorState.selection.selectedEntities.set([...current.selectedEntities, entity])
    }
  },
  
  /**
   * 从选择中移除
   */
  removeFromSelection: (entity: Entity) => {
    const current = EditorState.selection.get()
    EditorState.selection.selectedEntities.set(
      current.selectedEntities.filter(e => e !== entity)
    )
  },
  
  /**
   * 清空选择
   */
  clearSelection: () => {
    EditorState.selection.set({
      selectedEntities: [],
      primaryEntity: undefined
    })
  },
  
  /**
   * 设置错误状态
   */
  setError: (message: string, details?: any) => {
    EditorState.error.set({
      hasError: true,
      message,
      details
    })
  },
  
  /**
   * 清除错误状态
   */
  clearError: () => {
    EditorState.error.set({
      hasError: false,
      message: null,
      details: null
    })
  },
  
  /**
   * 设置警告状态
   */
  setWarning: (message: string) => {
    EditorState.warning.set({
      hasWarning: true,
      message
    })
  },
  
  /**
   * 清除警告状态
   */
  clearWarning: () => {
    EditorState.warning.set({
      hasWarning: false,
      message: null
    })
  },
  
  /**
   * 切换面板可见性
   */
  togglePanel: (panel: keyof EditorStateType['panels']) => {
    const current = EditorState.panels[panel].get()
    EditorState.panels[panel].set(!current)
  },
  
  /**
   * 设置加载状态
   */
  setLoading: (loading: boolean) => {
    EditorState.loading.set(loading)
  }
}
