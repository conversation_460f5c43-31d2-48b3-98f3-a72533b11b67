/**
 * 相机控制器组件
 * 
 * 提供编辑器相机的控制功能
 */

import React from 'react'
import { OrbitControls } from '@react-three/drei'
import { useHookstate } from '@hookstate/core'
import { EditorState } from '../../services/EditorState'
import { EditorMode } from '../../types'

/**
 * 相机控制器属性
 */
export interface CameraControllerProps {
  className?: string
}

/**
 * 相机控制器组件
 */
const CameraController: React.FC<CameraControllerProps> = ({
  className = ''
}) => {
  const editorState = useHookstate(EditorState)
  const mode = editorState.mode.get()
  
  // 根据编辑器模式决定是否启用相机控制
  const enableControls = mode === EditorMode.FLY || mode === EditorMode.SELECT

  return (
    <>
      {enableControls && (
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          dampingFactor={0.05}
          enableDamping={true}
          maxPolarAngle={Math.PI}
          minDistance={0.1}
          maxDistance={1000}
        />
      )}
    </>
  )
}

export default CameraController
