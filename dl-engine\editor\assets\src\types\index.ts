/**
 * 资产管理系统类型定义
 */

/**
 * 资产类型枚举
 */
export enum AssetType {
  // 3D模型
  MODEL = 'model',
  
  // 纹理和图像
  TEXTURE = 'texture',
  IMAGE = 'image',
  
  // 音频
  AUDIO = 'audio',
  MUSIC = 'music',
  SOUND_EFFECT = 'sound_effect',
  
  // 视频
  VIDEO = 'video',
  ANIMATION = 'animation',
  
  // 材质
  MATERIAL = 'material',
  SHADER = 'shader',
  
  // 脚本
  SCRIPT = 'script',
  VISUAL_SCRIPT = 'visual_script',
  
  // 场景
  SCENE = 'scene',
  PREFAB = 'prefab',
  
  // 字体
  FONT = 'font',
  
  // 数据
  JSON = 'json',
  XML = 'xml',
  CSV = 'csv',
  
  // 文档
  DOCUMENT = 'document',
  PDF = 'pdf',
  
  // 文件夹
  FOLDER = 'folder',
  
  // 其他
  UNKNOWN = 'unknown'
}

/**
 * 资产状态枚举
 */
export enum AssetStatus {
  PENDING = 'pending',
  IMPORTING = 'importing',
  PROCESSING = 'processing',
  READY = 'ready',
  ERROR = 'error',
  MISSING = 'missing'
}

/**
 * 资产元数据接口
 */
export interface AssetMetadata {
  /** 文件大小（字节） */
  fileSize: number
  
  /** MIME类型 */
  mimeType: string
  
  /** 文件扩展名 */
  extension: string
  
  /** 创建时间 */
  createdAt: Date
  
  /** 修改时间 */
  modifiedAt: Date
  
  /** 导入时间 */
  importedAt: Date
  
  /** 最后访问时间 */
  lastAccessedAt?: Date
  
  /** 校验和 */
  checksum: string
  
  /** 版本号 */
  version: number
  
  /** 标签 */
  tags: string[]
  
  /** 自定义属性 */
  customProperties: Record<string, any>
  
  /** 依赖关系 */
  dependencies: string[]
  
  /** 引用计数 */
  referenceCount: number
}

/**
 * 资产缩略图接口
 */
export interface AssetThumbnail {
  /** 缩略图ID */
  id: string
  
  /** 资产ID */
  assetId: string
  
  /** 缩略图URL或Base64数据 */
  url: string
  
  /** 缩略图尺寸 */
  size: { width: number; height: number }
  
  /** 生成时间 */
  generatedAt: Date
  
  /** 缩略图格式 */
  format: 'png' | 'jpg' | 'webp'
}

/**
 * 资产接口
 */
export interface Asset {
  /** 资产唯一ID */
  id: string
  
  /** 资产名称 */
  name: string
  
  /** 资产类型 */
  type: AssetType
  
  /** 资产状态 */
  status: AssetStatus
  
  /** 文件路径 */
  path: string
  
  /** 相对路径 */
  relativePath: string
  
  /** 父文件夹ID */
  parentId?: string
  
  /** 资产描述 */
  description?: string
  
  /** 资产元数据 */
  metadata: AssetMetadata
  
  /** 缩略图 */
  thumbnail?: AssetThumbnail
  
  /** 预览数据 */
  previewData?: any
  
  /** 导入配置 */
  importSettings?: Record<string, any>
  
  /** 是否为收藏 */
  isFavorite: boolean
  
  /** 是否只读 */
  isReadOnly: boolean
  
  /** 是否隐藏 */
  isHidden: boolean
}

/**
 * 资产文件夹接口
 */
export interface AssetFolder extends Omit<Asset, 'type' | 'metadata'> {
  type: AssetType.FOLDER
  
  /** 子资产ID列表 */
  children: string[]
  
  /** 是否展开 */
  expanded: boolean
  
  /** 文件夹颜色 */
  color?: string
  
  /** 文件夹图标 */
  icon?: string
}

/**
 * 资产导入配置接口
 */
export interface AssetImportConfig {
  /** 目标文件夹ID */
  targetFolderId?: string
  
  /** 是否覆盖现有文件 */
  overwrite: boolean
  
  /** 是否生成缩略图 */
  generateThumbnails: boolean
  
  /** 缩略图尺寸 */
  thumbnailSize: { width: number; height: number }
  
  /** 是否自动处理 */
  autoProcess: boolean
  
  /** 处理选项 */
  processingOptions: Record<string, any>
  
  /** 是否保留原始文件结构 */
  preserveStructure: boolean
  
  /** 文件名冲突处理策略 */
  conflictResolution: 'skip' | 'overwrite' | 'rename' | 'ask'
  
  /** 支持的文件类型 */
  supportedTypes: string[]
  
  /** 最大文件大小 */
  maxFileSize: number
}

/**
 * 资产搜索条件接口
 */
export interface AssetSearchCriteria {
  /** 搜索关键词 */
  query?: string
  
  /** 资产类型过滤 */
  types?: AssetType[]
  
  /** 标签过滤 */
  tags?: string[]
  
  /** 文件大小范围 */
  sizeRange?: { min: number; max: number }
  
  /** 日期范围 */
  dateRange?: { start: Date; end: Date }
  
  /** 文件夹过滤 */
  folderId?: string
  
  /** 是否包含子文件夹 */
  includeSubfolders?: boolean
  
  /** 是否只显示收藏 */
  favoritesOnly?: boolean
  
  /** 排序方式 */
  sortBy?: 'name' | 'type' | 'size' | 'date' | 'relevance'
  
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc'
  
  /** 分页信息 */
  pagination?: {
    page: number
    pageSize: number
  }
}

/**
 * 资产搜索结果接口
 */
export interface AssetSearchResult {
  /** 匹配的资产列表 */
  assets: Asset[]
  
  /** 总数量 */
  total: number
  
  /** 当前页 */
  page: number
  
  /** 每页大小 */
  pageSize: number
  
  /** 搜索耗时 */
  searchTime: number
  
  /** 搜索建议 */
  suggestions?: string[]
}

/**
 * 资产版本信息接口
 */
export interface AssetVersion {
  /** 版本ID */
  id: string
  
  /** 资产ID */
  assetId: string
  
  /** 版本号 */
  version: number
  
  /** 版本描述 */
  description?: string
  
  /** 创建时间 */
  createdAt: Date
  
  /** 创建者 */
  createdBy: string
  
  /** 文件路径 */
  filePath: string
  
  /** 文件大小 */
  fileSize: number
  
  /** 校验和 */
  checksum: string
  
  /** 是否为当前版本 */
  isCurrent: boolean
  
  /** 变更日志 */
  changelog?: string
}

/**
 * 资产处理任务接口
 */
export interface AssetProcessingTask {
  /** 任务ID */
  id: string
  
  /** 资产ID */
  assetId: string
  
  /** 任务类型 */
  type: 'import' | 'thumbnail' | 'optimize' | 'convert' | 'validate'
  
  /** 任务状态 */
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  
  /** 进度百分比 */
  progress: number
  
  /** 任务描述 */
  description: string
  
  /** 开始时间 */
  startedAt?: Date
  
  /** 完成时间 */
  completedAt?: Date
  
  /** 错误信息 */
  error?: string
  
  /** 任务结果 */
  result?: any
  
  /** 任务配置 */
  config: Record<string, any>
}

/**
 * 资产统计信息接口
 */
export interface AssetStatistics {
  /** 总资产数量 */
  totalAssets: number
  
  /** 按类型分组的数量 */
  assetsByType: Record<AssetType, number>
  
  /** 总文件大小 */
  totalSize: number
  
  /** 按类型分组的大小 */
  sizeByType: Record<AssetType, number>
  
  /** 最近导入的资产数量 */
  recentImports: number
  
  /** 收藏资产数量 */
  favoriteAssets: number
  
  /** 错误资产数量 */
  errorAssets: number
  
  /** 缺失资产数量 */
  missingAssets: number
  
  /** 最常用的标签 */
  popularTags: Array<{ tag: string; count: number }>
  
  /** 存储使用情况 */
  storageUsage: {
    used: number
    total: number
    percentage: number
  }
}

/**
 * 资产事件接口
 */
export interface AssetEvent {
  /** 事件ID */
  id: string
  
  /** 事件类型 */
  type: 'created' | 'updated' | 'deleted' | 'moved' | 'renamed' | 'imported' | 'exported'
  
  /** 资产ID */
  assetId: string
  
  /** 事件时间 */
  timestamp: Date
  
  /** 用户ID */
  userId?: string
  
  /** 事件数据 */
  data: Record<string, any>
  
  /** 事件描述 */
  description: string
}

/**
 * 资产配置接口
 */
export interface AssetConfig {
  /** 存储根目录 */
  storageRoot: string
  
  /** 缓存目录 */
  cacheDirectory: string
  
  /** 缩略图目录 */
  thumbnailDirectory: string
  
  /** 临时目录 */
  tempDirectory: string
  
  /** 最大缓存大小 */
  maxCacheSize: number
  
  /** 缩略图配置 */
  thumbnailConfig: {
    defaultSize: { width: number; height: number }
    formats: string[]
    quality: number
  }
  
  /** 导入配置 */
  importConfig: AssetImportConfig
  
  /** 搜索配置 */
  searchConfig: {
    indexingEnabled: boolean
    maxResults: number
    fuzzySearch: boolean
  }
  
  /** 版本控制配置 */
  versioningConfig: {
    enabled: boolean
    maxVersions: number
    autoCleanup: boolean
  }
  
  /** 备份配置 */
  backupConfig: {
    enabled: boolean
    interval: number
    retention: number
  }
}
