/**
 * 资产管理工具函数测试
 */

import { describe, it, expect } from 'vitest'
import { 
  fileTypeUtils, 
  fileSizeUtils, 
  pathUtils, 
  searchUtils, 
  validationUtils 
} from '../utils'
import { AssetType } from '../types'

describe('fileTypeUtils', () => {
  describe('getAssetTypeFromExtension', () => {
    it('should return correct asset type for model files', () => {
      expect(fileTypeUtils.getAssetTypeFromExtension('.gltf')).toBe(AssetType.MODEL)
      expect(fileTypeUtils.getAssetTypeFromExtension('.fbx')).toBe(AssetType.MODEL)
      expect(fileTypeUtils.getAssetTypeFromExtension('.obj')).toBe(AssetType.MODEL)
    })

    it('should return correct asset type for image files', () => {
      expect(fileTypeUtils.getAssetTypeFromExtension('.jpg')).toBe(AssetType.TEXTURE)
      expect(fileTypeUtils.getAssetTypeFromExtension('.png')).toBe(AssetType.TEXTURE)
      expect(fileTypeUtils.getAssetTypeFromExtension('.gif')).toBe(AssetType.TEXTURE)
    })

    it('should return correct asset type for audio files', () => {
      expect(fileTypeUtils.getAssetTypeFromExtension('.mp3')).toBe(AssetType.AUDIO)
      expect(fileTypeUtils.getAssetTypeFromExtension('.wav')).toBe(AssetType.AUDIO)
      expect(fileTypeUtils.getAssetTypeFromExtension('.ogg')).toBe(AssetType.AUDIO)
    })

    it('should return UNKNOWN for unsupported extensions', () => {
      expect(fileTypeUtils.getAssetTypeFromExtension('.xyz')).toBe(AssetType.UNKNOWN)
      expect(fileTypeUtils.getAssetTypeFromExtension('.unknown')).toBe(AssetType.UNKNOWN)
    })

    it('should handle case insensitive extensions', () => {
      expect(fileTypeUtils.getAssetTypeFromExtension('.GLTF')).toBe(AssetType.MODEL)
      expect(fileTypeUtils.getAssetTypeFromExtension('.JPG')).toBe(AssetType.TEXTURE)
    })
  })

  describe('getAssetTypeIcon', () => {
    it('should return correct icons for different asset types', () => {
      expect(fileTypeUtils.getAssetTypeIcon(AssetType.FOLDER)).toBe('📁')
      expect(fileTypeUtils.getAssetTypeIcon(AssetType.MODEL)).toBe('🎲')
      expect(fileTypeUtils.getAssetTypeIcon(AssetType.TEXTURE)).toBe('🖼️')
      expect(fileTypeUtils.getAssetTypeIcon(AssetType.AUDIO)).toBe('🎵')
    })
  })

  describe('isPreviewable', () => {
    it('should return true for previewable asset types', () => {
      expect(fileTypeUtils.isPreviewable(AssetType.IMAGE)).toBe(true)
      expect(fileTypeUtils.isPreviewable(AssetType.MODEL)).toBe(true)
      expect(fileTypeUtils.isPreviewable(AssetType.VIDEO)).toBe(true)
    })

    it('should return false for non-previewable asset types', () => {
      expect(fileTypeUtils.isPreviewable(AssetType.FOLDER)).toBe(false)
      expect(fileTypeUtils.isPreviewable(AssetType.UNKNOWN)).toBe(false)
    })
  })
})

describe('fileSizeUtils', () => {
  describe('formatFileSize', () => {
    it('should format bytes correctly', () => {
      expect(fileSizeUtils.formatFileSize(0)).toBe('0 B')
      expect(fileSizeUtils.formatFileSize(1024)).toBe('1.0 KB')
      expect(fileSizeUtils.formatFileSize(1048576)).toBe('1.0 MB')
      expect(fileSizeUtils.formatFileSize(1073741824)).toBe('1.0 GB')
    })

    it('should handle decimal values', () => {
      expect(fileSizeUtils.formatFileSize(1536)).toBe('1.5 KB')
      expect(fileSizeUtils.formatFileSize(2621440)).toBe('2.5 MB')
    })
  })

  describe('parseFileSize', () => {
    it('should parse size strings correctly', () => {
      expect(fileSizeUtils.parseFileSize('1024 B')).toBe(1024)
      expect(fileSizeUtils.parseFileSize('1 KB')).toBe(1024)
      expect(fileSizeUtils.parseFileSize('1 MB')).toBe(1048576)
      expect(fileSizeUtils.parseFileSize('1.5 KB')).toBe(1536)
    })

    it('should handle case insensitive units', () => {
      expect(fileSizeUtils.parseFileSize('1 kb')).toBe(1024)
      expect(fileSizeUtils.parseFileSize('1 Mb')).toBe(1048576)
    })

    it('should return 0 for invalid strings', () => {
      expect(fileSizeUtils.parseFileSize('invalid')).toBe(0)
      expect(fileSizeUtils.parseFileSize('1 XB')).toBe(0)
    })
  })
})

describe('pathUtils', () => {
  describe('getBaseName', () => {
    it('should extract base name without extension', () => {
      expect(pathUtils.getBaseName('file.txt')).toBe('file')
      expect(pathUtils.getBaseName('path/to/file.txt')).toBe('file')
      expect(pathUtils.getBaseName('file.name.txt')).toBe('file.name')
    })

    it('should handle files without extension', () => {
      expect(pathUtils.getBaseName('file')).toBe('file')
      expect(pathUtils.getBaseName('path/to/file')).toBe('file')
    })
  })

  describe('getExtension', () => {
    it('should extract file extension', () => {
      expect(pathUtils.getExtension('file.txt')).toBe('.txt')
      expect(pathUtils.getExtension('path/to/file.jpg')).toBe('.jpg')
      expect(pathUtils.getExtension('file.name.txt')).toBe('.txt')
    })

    it('should return empty string for files without extension', () => {
      expect(pathUtils.getExtension('file')).toBe('')
      expect(pathUtils.getExtension('path/to/file')).toBe('')
    })
  })

  describe('join', () => {
    it('should join paths correctly', () => {
      expect(pathUtils.join('a', 'b', 'c')).toBe('a/b/c')
      expect(pathUtils.join('a/', '/b/', '/c')).toBe('a/b/c')
      expect(pathUtils.join('', 'a', '', 'b')).toBe('a/b')
    })
  })

  describe('normalize', () => {
    it('should normalize paths', () => {
      expect(pathUtils.normalize('a//b///c')).toBe('a/b/c')
      expect(pathUtils.normalize('/a/b/c/')).toBe('a/b/c')
      expect(pathUtils.normalize('//a/b/c//')).toBe('a/b/c')
    })
  })
})

describe('searchUtils', () => {
  describe('fuzzyMatch', () => {
    it('should match fuzzy queries', () => {
      expect(searchUtils.fuzzyMatch('abc', 'aabbcc')).toBe(true)
      expect(searchUtils.fuzzyMatch('file', 'my_file_name')).toBe(true)
      expect(searchUtils.fuzzyMatch('xyz', 'abc')).toBe(false)
    })

    it('should be case insensitive', () => {
      expect(searchUtils.fuzzyMatch('ABC', 'aabbcc')).toBe(true)
      expect(searchUtils.fuzzyMatch('file', 'MY_FILE_NAME')).toBe(true)
    })
  })

  describe('highlightMatch', () => {
    it('should highlight matching text', () => {
      expect(searchUtils.highlightMatch('hello world', 'world')).toBe('hello <mark>world</mark>')
      expect(searchUtils.highlightMatch('test file', 'file')).toBe('test <mark>file</mark>')
    })

    it('should be case insensitive', () => {
      expect(searchUtils.highlightMatch('Hello World', 'world')).toBe('Hello <mark>World</mark>')
    })

    it('should return original text if no query', () => {
      expect(searchUtils.highlightMatch('hello world', '')).toBe('hello world')
    })
  })
})

describe('validationUtils', () => {
  describe('validateAssetName', () => {
    it('should validate correct names', () => {
      expect(validationUtils.validateAssetName('valid_name')).toEqual({ valid: true })
      expect(validationUtils.validateAssetName('file.txt')).toEqual({ valid: true })
      expect(validationUtils.validateAssetName('123')).toEqual({ valid: true })
    })

    it('should reject empty names', () => {
      expect(validationUtils.validateAssetName('')).toEqual({ 
        valid: false, 
        error: '名称不能为空' 
      })
      expect(validationUtils.validateAssetName('   ')).toEqual({ 
        valid: false, 
        error: '名称不能为空' 
      })
    })

    it('should reject names with invalid characters', () => {
      expect(validationUtils.validateAssetName('file<name')).toEqual({ 
        valid: false, 
        error: '名称包含无效字符' 
      })
      expect(validationUtils.validateAssetName('file|name')).toEqual({ 
        valid: false, 
        error: '名称包含无效字符' 
      })
    })

    it('should reject names that are too long', () => {
      const longName = 'a'.repeat(256)
      expect(validationUtils.validateAssetName(longName)).toEqual({ 
        valid: false, 
        error: '名称过长（最多255个字符）' 
      })
    })
  })

  describe('validateFileSize', () => {
    it('should validate file sizes within limit', () => {
      expect(validationUtils.validateFileSize(1024, 2048)).toEqual({ valid: true })
      expect(validationUtils.validateFileSize(2048, 2048)).toEqual({ valid: true })
    })

    it('should reject files that are too large', () => {
      expect(validationUtils.validateFileSize(3072, 2048)).toEqual({ 
        valid: false, 
        error: '文件过大（最大 2.0 KB）' 
      })
    })
  })

  describe('validateFileType', () => {
    it('should validate allowed file types', () => {
      expect(validationUtils.validateFileType('.jpg', ['.jpg', '.png'])).toEqual({ valid: true })
      expect(validationUtils.validateFileType('.png', ['.jpg', '.png'])).toEqual({ valid: true })
    })

    it('should reject disallowed file types', () => {
      expect(validationUtils.validateFileType('.gif', ['.jpg', '.png'])).toEqual({ 
        valid: false, 
        error: '不支持的文件类型（支持：.jpg, .png）' 
      })
    })

    it('should allow all types when no restrictions', () => {
      expect(validationUtils.validateFileType('.anything', [])).toEqual({ valid: true })
    })
  })
})
