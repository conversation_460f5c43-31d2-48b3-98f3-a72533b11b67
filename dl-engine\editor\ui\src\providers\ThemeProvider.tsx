/**
 * DL-Engine 主题提供者
 * 
 * 管理应用主题状态和切换
 */

import React, { createContext, useContext, useState, useEffect } from 'react'
import { ConfigProvider } from 'antd'
import { useHookstate } from '@hookstate/core'
import { ThemeType, DLThemeConfig, getTheme, defaultTheme } from '../theme'

/**
 * 主题上下文类型
 */
interface ThemeContextType {
  /** 当前主题类型 */
  currentTheme: ThemeType
  /** 当前主题配置 */
  themeConfig: DLThemeConfig
  /** 切换主题 */
  setTheme: (theme: ThemeType) => void
  /** 获取主题配置 */
  getThemeConfig: (theme: ThemeType) => DLThemeConfig
}

/**
 * 主题上下文
 */
const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

/**
 * 主题提供者属性
 */
export interface ThemeProviderProps {
  /** 初始主题 */
  initialTheme?: ThemeType
  /** 子组件 */
  children: React.ReactNode
}

/**
 * 主题状态
 */
const themeState = hookstate<ThemeType>(defaultTheme.name)

/**
 * DL-Engine 主题提供者
 */
const ThemeProvider: React.FC<ThemeProviderProps> = ({
  initialTheme = defaultTheme.name,
  children
}) => {
  const currentThemeState = useHookstate(themeState)
  const [themeConfig, setThemeConfig] = useState<DLThemeConfig>(defaultTheme)
  
  // 初始化主题
  useEffect(() => {
    // 从本地存储加载主题
    const savedTheme = localStorage.getItem('dl-editor-theme') as ThemeType
    const theme = savedTheme || initialTheme
    
    currentThemeState.set(theme)
    setThemeConfig(getTheme(theme))
  }, [initialTheme])
  
  // 监听主题变化
  useEffect(() => {
    const theme = currentThemeState.get()
    const config = getTheme(theme)
    setThemeConfig(config)
    
    // 保存到本地存储
    localStorage.setItem('dl-editor-theme', theme)
    
    // 更新 CSS 变量
    updateCSSVariables(config)
  }, [currentThemeState.get()])
  
  /**
   * 更新 CSS 变量
   */
  const updateCSSVariables = (config: DLThemeConfig) => {
    const root = document.documentElement
    const token = config.token || {}
    
    // 设置主要颜色变量
    if (token.colorPrimary) {
      root.style.setProperty('--dl-color-primary', token.colorPrimary)
    }
    if (token.colorBgBase) {
      root.style.setProperty('--dl-color-bg-base', token.colorBgBase)
    }
    if (token.colorBgContainer) {
      root.style.setProperty('--dl-color-bg-container', token.colorBgContainer)
    }
    if (token.colorText) {
      root.style.setProperty('--dl-color-text', token.colorText)
    }
    if (token.colorBorder) {
      root.style.setProperty('--dl-color-border', token.colorBorder)
    }
    
    // 设置字体变量
    if (token.fontFamily) {
      root.style.setProperty('--dl-font-family', token.fontFamily)
    }
    if (token.fontSize) {
      root.style.setProperty('--dl-font-size', `${token.fontSize}px`)
    }
    
    // 设置圆角变量
    if (token.borderRadius) {
      root.style.setProperty('--dl-border-radius', `${token.borderRadius}px`)
    }
    
    // 设置间距变量
    if (token.padding) {
      root.style.setProperty('--dl-padding', `${token.padding}px`)
    }
    if (token.margin) {
      root.style.setProperty('--dl-margin', `${token.margin}px`)
    }
  }
  
  /**
   * 切换主题
   */
  const setTheme = (theme: ThemeType) => {
    currentThemeState.set(theme)
  }
  
  /**
   * 获取主题配置
   */
  const getThemeConfig = (theme: ThemeType) => {
    return getTheme(theme)
  }
  
  const contextValue: ThemeContextType = {
    currentTheme: currentThemeState.get(),
    themeConfig,
    setTheme,
    getThemeConfig
  }
  
  return (
    <ThemeContext.Provider value={contextValue}>
      <ConfigProvider theme={themeConfig}>
        {children}
      </ConfigProvider>
    </ThemeContext.Provider>
  )
}

/**
 * 使用主题钩子
 */
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

export default ThemeProvider
