/**
 * 资产管理工具函数
 */

import { Asset, AssetType, AssetMetadata } from '../types'

/**
 * 文件类型检测工具
 */
export const fileTypeUtils = {
  /**
   * 根据文件扩展名获取资产类型
   */
  getAssetTypeFromExtension: (extension: string): AssetType => {
    const ext = extension.toLowerCase()
    
    // 3D模型
    if (['.gltf', '.glb', '.fbx', '.obj', '.dae', '.3ds', '.ply'].includes(ext)) {
      return AssetType.MODEL
    }
    
    // 图像和纹理
    if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tga', '.webp', '.svg'].includes(ext)) {
      return AssetType.TEXTURE
    }
    
    // 音频
    if (['.mp3', '.wav', '.ogg', '.m4a', '.aac', '.flac'].includes(ext)) {
      return AssetType.AUDIO
    }
    
    // 视频
    if (['.mp4', '.webm', '.ogv', '.avi', '.mov', '.wmv'].includes(ext)) {
      return AssetType.VIDEO
    }
    
    // 脚本
    if (['.js', '.ts', '.json', '.lua', '.py', '.cs'].includes(ext)) {
      return AssetType.SCRIPT
    }
    
    // 材质
    if (['.mat', '.mtl'].includes(ext)) {
      return AssetType.MATERIAL
    }
    
    // 字体
    if (['.ttf', '.otf', '.woff', '.woff2'].includes(ext)) {
      return AssetType.FONT
    }
    
    // 文档
    if (['.pdf', '.doc', '.docx', '.txt', '.md'].includes(ext)) {
      return AssetType.DOCUMENT
    }
    
    // 数据
    if (['.xml', '.csv'].includes(ext)) {
      return ext === '.xml' ? AssetType.XML : AssetType.CSV
    }
    
    return AssetType.UNKNOWN
  },
  
  /**
   * 获取文件类型图标
   */
  getAssetTypeIcon: (type: AssetType): string => {
    switch (type) {
      case AssetType.FOLDER: return '📁'
      case AssetType.MODEL: return '🎲'
      case AssetType.TEXTURE: return '🖼️'
      case AssetType.IMAGE: return '🖼️'
      case AssetType.AUDIO: return '🎵'
      case AssetType.MUSIC: return '🎵'
      case AssetType.SOUND_EFFECT: return '🔊'
      case AssetType.VIDEO: return '🎬'
      case AssetType.ANIMATION: return '🎬'
      case AssetType.MATERIAL: return '🎨'
      case AssetType.SHADER: return '✨'
      case AssetType.SCRIPT: return '📜'
      case AssetType.VISUAL_SCRIPT: return '🔗'
      case AssetType.SCENE: return '🌍'
      case AssetType.PREFAB: return '🧩'
      case AssetType.FONT: return '🔤'
      case AssetType.JSON: return '📋'
      case AssetType.XML: return '📋'
      case AssetType.CSV: return '📊'
      case AssetType.DOCUMENT: return '📄'
      case AssetType.PDF: return '📕'
      default: return '📄'
    }
  },
  
  /**
   * 获取文件类型颜色
   */
  getAssetTypeColor: (type: AssetType): string => {
    switch (type) {
      case AssetType.FOLDER: return '#FFA726'
      case AssetType.MODEL: return '#42A5F5'
      case AssetType.TEXTURE: return '#66BB6A'
      case AssetType.IMAGE: return '#66BB6A'
      case AssetType.AUDIO: return '#AB47BC'
      case AssetType.MUSIC: return '#AB47BC'
      case AssetType.SOUND_EFFECT: return '#AB47BC'
      case AssetType.VIDEO: return '#EF5350'
      case AssetType.ANIMATION: return '#EF5350'
      case AssetType.MATERIAL: return '#FF7043'
      case AssetType.SHADER: return '#FFCA28'
      case AssetType.SCRIPT: return '#26A69A'
      case AssetType.VISUAL_SCRIPT: return '#5C6BC0'
      case AssetType.SCENE: return '#8D6E63'
      case AssetType.PREFAB: return '#78909C'
      case AssetType.FONT: return '#9CCC65'
      case AssetType.JSON: return '#FFA726'
      case AssetType.XML: return '#FFA726'
      case AssetType.CSV: return '#29B6F6'
      case AssetType.DOCUMENT: return '#BDBDBD'
      case AssetType.PDF: return '#F44336'
      default: return '#9E9E9E'
    }
  },
  
  /**
   * 检查文件类型是否支持预览
   */
  isPreviewable: (type: AssetType): boolean => {
    return [
      AssetType.IMAGE,
      AssetType.TEXTURE,
      AssetType.MODEL,
      AssetType.VIDEO,
      AssetType.AUDIO,
      AssetType.DOCUMENT,
      AssetType.PDF,
      AssetType.JSON,
      AssetType.XML
    ].includes(type)
  }
}

/**
 * 文件大小格式化工具
 */
export const fileSizeUtils = {
  /**
   * 格式化文件大小
   */
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`
  },
  
  /**
   * 解析文件大小字符串
   */
  parseFileSize: (sizeStr: string): number => {
    const match = sizeStr.match(/^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB|TB)$/i)
    if (!match) return 0
    
    const value = parseFloat(match[1])
    const unit = match[2].toUpperCase()
    
    const multipliers = {
      'B': 1,
      'KB': 1024,
      'MB': 1024 * 1024,
      'GB': 1024 * 1024 * 1024,
      'TB': 1024 * 1024 * 1024 * 1024
    }
    
    return value * (multipliers[unit as keyof typeof multipliers] || 1)
  }
}

/**
 * 路径处理工具
 */
export const pathUtils = {
  /**
   * 获取文件名（不含扩展名）
   */
  getBaseName: (path: string): string => {
    const fileName = path.split('/').pop() || ''
    const lastDotIndex = fileName.lastIndexOf('.')
    return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName
  },
  
  /**
   * 获取文件扩展名
   */
  getExtension: (path: string): string => {
    const fileName = path.split('/').pop() || ''
    const lastDotIndex = fileName.lastIndexOf('.')
    return lastDotIndex > 0 ? fileName.substring(lastDotIndex) : ''
  },
  
  /**
   * 获取目录路径
   */
  getDirectory: (path: string): string => {
    const parts = path.split('/')
    parts.pop()
    return parts.join('/')
  },
  
  /**
   * 连接路径
   */
  join: (...paths: string[]): string => {
    return paths
      .filter(Boolean)
      .join('/')
      .replace(/\/+/g, '/')
  },
  
  /**
   * 规范化路径
   */
  normalize: (path: string): string => {
    return path
      .replace(/\/+/g, '/')
      .replace(/\/$/, '')
      .replace(/^\//, '')
  },
  
  /**
   * 检查路径是否为绝对路径
   */
  isAbsolute: (path: string): boolean => {
    return path.startsWith('/')
  },
  
  /**
   * 获取相对路径
   */
  relative: (from: string, to: string): string => {
    const fromParts = from.split('/').filter(Boolean)
    const toParts = to.split('/').filter(Boolean)
    
    let commonLength = 0
    for (let i = 0; i < Math.min(fromParts.length, toParts.length); i++) {
      if (fromParts[i] === toParts[i]) {
        commonLength++
      } else {
        break
      }
    }
    
    const upLevels = fromParts.length - commonLength
    const downPath = toParts.slice(commonLength)
    
    return '../'.repeat(upLevels) + downPath.join('/')
  }
}

/**
 * 资产搜索工具
 */
export const searchUtils = {
  /**
   * 模糊搜索匹配
   */
  fuzzyMatch: (query: string, text: string): boolean => {
    const queryLower = query.toLowerCase()
    const textLower = text.toLowerCase()
    
    let queryIndex = 0
    for (let i = 0; i < textLower.length && queryIndex < queryLower.length; i++) {
      if (textLower[i] === queryLower[queryIndex]) {
        queryIndex++
      }
    }
    
    return queryIndex === queryLower.length
  },
  
  /**
   * 计算搜索相关性分数
   */
  calculateRelevance: (query: string, asset: Asset): number => {
    let score = 0
    const queryLower = query.toLowerCase()
    const nameLower = asset.name.toLowerCase()
    
    // 完全匹配
    if (nameLower === queryLower) {
      score += 100
    }
    
    // 开头匹配
    if (nameLower.startsWith(queryLower)) {
      score += 50
    }
    
    // 包含匹配
    if (nameLower.includes(queryLower)) {
      score += 25
    }
    
    // 标签匹配
    for (const tag of asset.metadata.tags) {
      if (tag.toLowerCase().includes(queryLower)) {
        score += 10
      }
    }
    
    // 描述匹配
    if (asset.description?.toLowerCase().includes(queryLower)) {
      score += 5
    }
    
    return score
  },
  
  /**
   * 高亮搜索关键词
   */
  highlightMatch: (text: string, query: string): string => {
    if (!query) return text
    
    const regex = new RegExp(`(${query})`, 'gi')
    return text.replace(regex, '<mark>$1</mark>')
  }
}

/**
 * 资产验证工具
 */
export const validationUtils = {
  /**
   * 验证资产名称
   */
  validateAssetName: (name: string): { valid: boolean; error?: string } => {
    if (!name || name.trim().length === 0) {
      return { valid: false, error: '名称不能为空' }
    }
    
    if (name.length > 255) {
      return { valid: false, error: '名称过长（最多255个字符）' }
    }
    
    const invalidChars = /[<>:"/\\|?*]/
    if (invalidChars.test(name)) {
      return { valid: false, error: '名称包含无效字符' }
    }
    
    return { valid: true }
  },
  
  /**
   * 验证文件大小
   */
  validateFileSize: (size: number, maxSize: number): { valid: boolean; error?: string } => {
    if (size > maxSize) {
      return { 
        valid: false, 
        error: `文件过大（最大 ${fileSizeUtils.formatFileSize(maxSize)}）` 
      }
    }
    
    return { valid: true }
  },
  
  /**
   * 验证文件类型
   */
  validateFileType: (extension: string, allowedTypes: string[]): { valid: boolean; error?: string } => {
    if (allowedTypes.length === 0) {
      return { valid: true }
    }
    
    if (!allowedTypes.includes(extension.toLowerCase())) {
      return { 
        valid: false, 
        error: `不支持的文件类型（支持：${allowedTypes.join(', ')}）` 
      }
    }
    
    return { valid: true }
  }
}

/**
 * 缓存工具
 */
export const cacheUtils = {
  /**
   * 生成缓存键
   */
  generateCacheKey: (prefix: string, ...parts: string[]): string => {
    return `${prefix}:${parts.join(':')}`
  },
  
  /**
   * 检查缓存是否过期
   */
  isCacheExpired: (timestamp: Date, ttl: number): boolean => {
    return Date.now() - timestamp.getTime() > ttl
  },
  
  /**
   * 清理过期缓存
   */
  cleanExpiredCache: <T>(cache: Map<string, { data: T; timestamp: Date }>, ttl: number): void => {
    const now = Date.now()
    for (const [key, entry] of cache.entries()) {
      if (now - entry.timestamp.getTime() > ttl) {
        cache.delete(key)
      }
    }
  }
}
