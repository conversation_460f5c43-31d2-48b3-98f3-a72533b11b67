/**
 * DL-Engine 编辑器国际化配置
 * 
 * 中文优先的多语言支持
 */

import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import zhCN from './locales/zh-CN.json'
import enUS from './locales/en-US.json'

/**
 * 支持的语言类型
 */
export type SupportedLanguage = 'zh-CN' | 'en-US'

/**
 * 语言配置
 */
export const languages = {
  'zh-CN': {
    name: '简体中文',
    nativeName: '简体中文',
    flag: '🇨🇳'
  },
  'en-US': {
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸'
  }
} as const

/**
 * 默认语言
 */
export const defaultLanguage: SupportedLanguage = 'zh-CN'

/**
 * 翻译资源
 */
const resources = {
  'zh-CN': {
    translation: zhCN
  },
  'en-US': {
    translation: enUS
  }
}

/**
 * 初始化 i18n
 */
i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: defaultLanguage,
    fallbackLng: defaultLanguage,
    
    interpolation: {
      escapeValue: false // React 已经处理了 XSS
    },
    
    react: {
      useSuspense: false
    },
    
    debug: process.env.NODE_ENV === 'development'
  })

/**
 * 语言工具函数
 */
export const i18nUtils = {
  /**
   * 获取当前语言
   */
  getCurrentLanguage(): SupportedLanguage {
    return i18n.language as SupportedLanguage
  },
  
  /**
   * 切换语言
   */
  changeLanguage(language: SupportedLanguage): Promise<void> {
    return i18n.changeLanguage(language)
  },
  
  /**
   * 获取所有支持的语言
   */
  getSupportedLanguages(): SupportedLanguage[] {
    return Object.keys(languages) as SupportedLanguage[]
  },
  
  /**
   * 获取语言显示名称
   */
  getLanguageDisplayName(language: SupportedLanguage): string {
    return languages[language]?.name || language
  },
  
  /**
   * 获取语言本地名称
   */
  getLanguageNativeName(language: SupportedLanguage): string {
    return languages[language]?.nativeName || language
  },
  
  /**
   * 获取语言标志
   */
  getLanguageFlag(language: SupportedLanguage): string {
    return languages[language]?.flag || '🌐'
  },
  
  /**
   * 检查是否为支持的语言
   */
  isSupportedLanguage(language: string): language is SupportedLanguage {
    return language in languages
  }
}

export default i18n
