/**
 * 移动操作工具组件
 */

import React from 'react'

export interface MoveGizmoProps {
  size?: number
}

const MoveGizmo: React.FC<MoveGizmoProps> = ({ size = 1 }) => {
  return (
    <group>
      {/* X轴 - 红色 */}
      <group>
        <mesh position={[size, 0, 0]}>
          <cylinderGeometry args={[0.02 * size, 0.02 * size, size * 2, 8]} rotation={[0, 0, Math.PI / 2]} />
          <meshBasicMaterial color="red" />
        </mesh>
        <mesh position={[size * 2, 0, 0]}>
          <coneGeometry args={[0.1 * size, 0.2 * size, 8]} rotation={[0, 0, -Math.PI / 2]} />
          <meshBasicMaterial color="red" />
        </mesh>
      </group>
      
      {/* Y轴 - 绿色 */}
      <group>
        <mesh position={[0, size, 0]}>
          <cylinderGeometry args={[0.02 * size, 0.02 * size, size * 2, 8]} />
          <meshBasicMaterial color="green" />
        </mesh>
        <mesh position={[0, size * 2, 0]}>
          <coneGeometry args={[0.1 * size, 0.2 * size, 8]} />
          <meshBasicMaterial color="green" />
        </mesh>
      </group>
      
      {/* Z轴 - 蓝色 */}
      <group>
        <mesh position={[0, 0, size]}>
          <cylinderGeometry args={[0.02 * size, 0.02 * size, size * 2, 8]} rotation={[Math.PI / 2, 0, 0]} />
          <meshBasicMaterial color="blue" />
        </mesh>
        <mesh position={[0, 0, size * 2]}>
          <coneGeometry args={[0.1 * size, 0.2 * size, 8]} rotation={[Math.PI / 2, 0, 0]} />
          <meshBasicMaterial color="blue" />
        </mesh>
      </group>
    </group>
  )
}

export default MoveGizmo
