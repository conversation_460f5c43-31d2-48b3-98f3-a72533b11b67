/**
 * DL按钮组件测试
 */

import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import DLButton from '../components/basic/DLButton'
import { I18nextProvider } from 'react-i18next'
import i18n from '../i18n'

// 测试包装器
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <I18nextProvider i18n={i18n}>
    {children}
  </I18nextProvider>
)

describe('DLButton', () => {
  it('should render with text', () => {
    render(
      <TestWrapper>
        <DLButton>测试按钮</DLButton>
      </TestWrapper>
    )
    
    expect(screen.getByText('测试按钮')).toBeInTheDocument()
  })

  it('should handle click events', () => {
    const handleClick = vi.fn()
    
    render(
      <TestWrapper>
        <DLButton onClick={handleClick}>点击我</DLButton>
      </TestWrapper>
    )
    
    fireEvent.click(screen.getByText('点击我'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('should render with icon', () => {
    render(
      <TestWrapper>
        <DLButton icon="add">添加</DLButton>
      </TestWrapper>
    )
    
    expect(screen.getByText('添加')).toBeInTheDocument()
    // 图标测试需要根据实际的图标实现来调整
  })

  it('should show tooltip when provided', () => {
    render(
      <TestWrapper>
        <DLButton tooltip="这是一个工具提示">悬停我</DLButton>
      </TestWrapper>
    )
    
    expect(screen.getByText('悬停我')).toBeInTheDocument()
  })

  it('should apply active state for tool buttons', () => {
    render(
      <TestWrapper>
        <DLButton tool active>工具按钮</DLButton>
      </TestWrapper>
    )
    
    const button = screen.getByText('工具按钮')
    expect(button).toHaveAttribute('data-active', 'true')
    expect(button).toHaveAttribute('data-tool', 'true')
  })

  it('should be disabled when loading', () => {
    render(
      <TestWrapper>
        <DLButton loading>加载中</DLButton>
      </TestWrapper>
    )
    
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
  })

  it('should apply variant styles', () => {
    render(
      <TestWrapper>
        <DLButton variant="danger">危险按钮</DLButton>
      </TestWrapper>
    )
    
    const button = screen.getByText('危险按钮')
    expect(button).toHaveClass('dl-button--danger')
  })
})
