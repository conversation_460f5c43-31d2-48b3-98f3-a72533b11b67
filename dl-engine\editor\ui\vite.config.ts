import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import dts from 'vite-plugin-dts'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    react(),
    dts({
      insertTypesEntry: true,
      include: ['src/**/*'],
      exclude: ['src/**/*.test.*', 'src/**/*.spec.*', 'src/**/*.stories.*']
    })
  ],
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'DLEngineEditorUI',
      formats: ['es', 'umd'],
      fileName: (format) => `index.${format}.js`
    },
    rollupOptions: {
      external: [
        'react',
        'react-dom',
        'antd',
        '@ant-design/icons',
        '@ant-design/colors',
        'react-i18next',
        'i18next',
        '@hookstate/core',
        '@dl-engine/shared-common'
      ],
      output: {
        globals: {
          'react': 'React',
          'react-dom': 'ReactDOM',
          'antd': 'antd',
          '@ant-design/icons': 'AntdIcons',
          'react-i18next': 'ReactI18next',
          'i18next': 'i18next'
        }
      }
    },
    target: 'es2020',
    sourcemap: true
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        modifyVars: {
          // Ant Design 主题定制
          '@primary-color': '#007acc',
          '@link-color': '#007acc',
          '@success-color': '#52c41a',
          '@warning-color': '#faad14',
          '@error-color': '#f5222d',
          '@font-size-base': '14px',
          '@heading-color': 'rgba(255, 255, 255, 0.85)',
          '@text-color': 'rgba(255, 255, 255, 0.65)',
          '@text-color-secondary': 'rgba(255, 255, 255, 0.45)',
          '@disabled-color': 'rgba(255, 255, 255, 0.25)',
          '@border-radius-base': '4px',
          '@border-color-base': '#3c3c3c',
          '@component-background': '#1e1e1e',
          '@body-background': '#1e1e1e',
          '@popover-background': '#2d2d30',
          '@item-hover-bg': 'rgba(255, 255, 255, 0.08)',
          '@item-active-bg': '#007acc'
        }
      }
    }
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  }
})
