/**
 * DL-Engine 工具提示组件
 * 
 * 基于 Ant Design Tooltip 的增强版本
 */

import React from 'react'
import { Tooltip, TooltipProps } from 'antd'
import { useTranslation } from 'react-i18next'
import classNames from 'classnames'

/**
 * DL工具提示属性
 */
export interface DLTooltipProps extends TooltipProps {
  /** 是否显示快捷键 */
  showShortcut?: boolean
  /** 快捷键文本 */
  shortcut?: string
  /** 延迟显示时间（毫秒） */
  delay?: number
}

/**
 * DL工具提示组件
 */
const DLTooltip: React.FC<DLTooltipProps> = ({
  title,
  showShortcut = false,
  shortcut,
  delay = 500,
  className,
  children,
  ...props
}) => {
  const { t } = useTranslation()
  
  // 构建工具提示内容
  const tooltipContent = React.useMemo(() => {
    if (!title) return undefined
    
    // 如果只是简单文本且没有快捷键，直接返回
    if (typeof title === 'string' && !shortcut && !showShortcut) {
      return title
    }
    
    // 构建复杂内容
    return (
      <div className="dl-tooltip-content">
        <div className="dl-tooltip-title">
          {title}
        </div>
        {(shortcut || showShortcut) && (
          <div className="dl-tooltip-shortcut">
            {shortcut}
          </div>
        )}
      </div>
    )
  }, [title, shortcut, showShortcut])
  
  // 构建类名
  const tooltipClassName = classNames(
    'dl-tooltip',
    {
      'dl-tooltip--with-shortcut': !!(shortcut || showShortcut)
    },
    className
  )
  
  return (
    <Tooltip
      {...props}
      title={tooltipContent}
      mouseEnterDelay={delay / 1000}
      overlayClassName={tooltipClassName}
    >
      {children}
    </Tooltip>
  )
}

export default DLTooltip
