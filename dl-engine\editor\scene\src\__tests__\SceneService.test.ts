/**
 * 场景服务测试
 */

import { describe, it, expect, beforeEach } from 'vitest'
import SceneService, { SceneData } from '../services/SceneService'
import { ObjectType } from '../types'

describe('SceneService', () => {
  let sceneService: SceneService

  beforeEach(() => {
    sceneService = new SceneService()
  })

  describe('createScene', () => {
    it('should create a new scene with correct properties', async () => {
      const scene = await sceneService.createScene('测试场景', '这是一个测试场景')
      
      expect(scene.id).toBeDefined()
      expect(scene.name).toBe('测试场景')
      expect(scene.description).toBe('这是一个测试场景')
      expect(scene.objects).toEqual([])
      expect(scene.config).toBeDefined()
      expect(scene.metadata.version).toBe('1.0.0')
      expect(scene.metadata.createdAt).toBeDefined()
      expect(scene.metadata.updatedAt).toBeDefined()
    })

    it('should create scene without description', async () => {
      const scene = await sceneService.createScene('简单场景')
      
      expect(scene.name).toBe('简单场景')
      expect(scene.description).toBeUndefined()
    })
  })

  describe('validateScene', () => {
    it('should validate a correct scene', async () => {
      const scene = await sceneService.createScene('有效场景')
      const validation = sceneService.validateScene(scene)
      
      expect(validation.valid).toBe(true)
      expect(validation.errors).toEqual([])
    })

    it('should detect missing scene ID', () => {
      const invalidScene: SceneData = {
        id: '',
        name: '无效场景',
        objects: [],
        config: {} as any,
        metadata: {
          version: '1.0.0',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }
      
      const validation = sceneService.validateScene(invalidScene)
      
      expect(validation.valid).toBe(false)
      expect(validation.errors).toContain('场景ID不能为空')
    })

    it('should detect missing scene name', () => {
      const invalidScene: SceneData = {
        id: 'test-id',
        name: '',
        objects: [],
        config: {} as any,
        metadata: {
          version: '1.0.0',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }
      
      const validation = sceneService.validateScene(invalidScene)
      
      expect(validation.valid).toBe(false)
      expect(validation.errors).toContain('场景名称不能为空')
    })

    it('should detect invalid objects array', () => {
      const invalidScene: SceneData = {
        id: 'test-id',
        name: '测试场景',
        objects: null as any,
        config: {} as any,
        metadata: {
          version: '1.0.0',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }
      
      const validation = sceneService.validateScene(invalidScene)
      
      expect(validation.valid).toBe(false)
      expect(validation.errors).toContain('场景对象列表无效')
    })

    it('should detect objects with missing properties', async () => {
      const scene = await sceneService.createScene('测试场景')
      scene.objects = [
        {
          id: '',
          name: '无效对象',
          type: ObjectType.MESH,
          children: [],
          transform: null as any,
          visible: true,
          locked: false,
          static: false,
          layer: 0,
          tags: [],
          userData: {}
        }
      ]
      
      const validation = sceneService.validateScene(scene)
      
      expect(validation.valid).toBe(false)
      expect(validation.errors).toContain('对象缺少ID: 无效对象')
      expect(validation.errors).toContain('对象缺少变换信息: 无效对象')
    })
  })

  describe('getSceneStatistics', () => {
    it('should calculate correct statistics for empty scene', async () => {
      const scene = await sceneService.createScene('空场景')
      const stats = sceneService.getSceneStatistics(scene)
      
      expect(stats.objectCount).toBe(0)
      expect(stats.meshCount).toBe(0)
      expect(stats.lightCount).toBe(0)
      expect(stats.cameraCount).toBe(0)
    })

    it('should calculate correct statistics for scene with objects', async () => {
      const scene = await sceneService.createScene('测试场景')
      scene.objects = [
        {
          id: 'mesh1',
          name: '网格1',
          type: ObjectType.MESH,
          children: [],
          transform: {} as any,
          visible: true,
          locked: false,
          static: false,
          layer: 0,
          tags: [],
          userData: {}
        },
        {
          id: 'light1',
          name: '光源1',
          type: ObjectType.LIGHT,
          children: [],
          transform: {} as any,
          visible: true,
          locked: false,
          static: false,
          layer: 0,
          tags: [],
          userData: {}
        },
        {
          id: 'camera1',
          name: '相机1',
          type: ObjectType.CAMERA,
          children: [],
          transform: {} as any,
          visible: true,
          locked: false,
          static: false,
          layer: 0,
          tags: [],
          userData: {}
        }
      ]
      
      const stats = sceneService.getSceneStatistics(scene)
      
      expect(stats.objectCount).toBe(3)
      expect(stats.meshCount).toBe(1)
      expect(stats.lightCount).toBe(1)
      expect(stats.cameraCount).toBe(1)
    })
  })

  describe('duplicateScene', () => {
    it('should create a copy of the scene with new ID', async () => {
      // 由于 loadScene 返回模拟数据，我们直接测试复制逻辑
      const originalScene = await sceneService.createScene('原始场景')
      const duplicatedScene = await sceneService.duplicateScene(originalScene.id)
      
      expect(duplicatedScene.id).not.toBe(originalScene.id)
      expect(duplicatedScene.name).toBe('原始场景 (副本)')
      expect(duplicatedScene.metadata.createdAt).not.toBe(originalScene.metadata.createdAt)
    })

    it('should use custom name for duplicated scene', async () => {
      const originalScene = await sceneService.createScene('原始场景')
      const duplicatedScene = await sceneService.duplicateScene(originalScene.id, '自定义副本')
      
      expect(duplicatedScene.name).toBe('自定义副本')
    })
  })

  describe('exportScene', () => {
    it('should export scene as JSON blob', async () => {
      const scene = await sceneService.createScene('导出测试')
      const blob = await sceneService.exportScene(scene, 'json')
      
      expect(blob.type).toBe('application/json')
      expect(blob.size).toBeGreaterThan(0)
      
      // 验证内容
      const text = await blob.text()
      const parsed = JSON.parse(text)
      expect(parsed.name).toBe('导出测试')
    })

    it('should throw error for unsupported format', async () => {
      const scene = await sceneService.createScene('测试场景')
      
      await expect(
        sceneService.exportScene(scene, 'unsupported' as any)
      ).rejects.toThrow('不支持的导出格式: unsupported')
    })
  })

  describe('importScene', () => {
    it('should import scene from JSON file', async () => {
      const scene = await sceneService.createScene('导入测试')
      const jsonBlob = await sceneService.exportScene(scene, 'json')
      const file = new File([jsonBlob], 'test.json', { type: 'application/json' })
      
      const importedScene = await sceneService.importScene(file)
      
      expect(importedScene.name).toBe('导入测试')
      expect(importedScene.id).toBe(scene.id)
    })

    it('should throw error for unsupported file format', async () => {
      const file = new File(['test'], 'test.xyz', { type: 'application/octet-stream' })
      
      await expect(
        sceneService.importScene(file)
      ).rejects.toThrow('不支持的文件格式: xyz')
    })

    it('should throw error for invalid JSON', async () => {
      const file = new File(['invalid json'], 'test.json', { type: 'application/json' })
      
      await expect(
        sceneService.importScene(file)
      ).rejects.toThrow()
    })
  })
})
