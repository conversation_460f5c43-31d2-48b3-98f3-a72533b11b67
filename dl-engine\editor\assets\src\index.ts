/**
 * Digital Learning Engine - 资产管理系统
 * 
 * 提供完整的资产管理功能，包括导入、组织、预览、版本控制等
 */

// 核心资产管理器
export { default as AssetManager } from './core/AssetManager'
export type { AssetManagerConfig } from './core/AssetManager'

// 资产类型和接口
export * from './types'

// 资产导入器
export * from './importers'

// 资产处理器
export * from './processors'

// 资产存储
export * from './storage'

// 资产预览
export * from './preview'

// 资产搜索
export * from './search'

// 版本控制
export * from './versioning'

// UI组件
export { default as AssetBrowser } from './components/AssetBrowser'
export { default as AssetImporter } from './components/AssetImporter'
export { default as AssetPreview } from './components/AssetPreview'
export { default as AssetProperties } from './components/AssetProperties'
export { default as AssetThumbnail } from './components/AssetThumbnail'
export { default as AssetTree } from './components/AssetTree'
export { default as AssetGrid } from './components/AssetGrid'
export { default as AssetList } from './components/AssetList'

// 工具函数
export * from './utils'

// 常量
export * from './constants'

// 钩子函数
export * from './hooks'

// 上下文
export * from './contexts'

// 状态管理
export { AssetState, AssetActions } from './state/AssetState'

// 服务
export { default as AssetService } from './services/AssetService'
export { default as ThumbnailService } from './services/ThumbnailService'
export { default as MetadataService } from './services/MetadataService'
