/**
 * 资产面板组件
 * 
 * 显示和管理项目资产，支持文件浏览、预览、导入等功能
 */

import React, { useState, useCallback } from 'react'
import { useHookstate } from '@hookstate/core'
import { EditorState } from '../../services/EditorState'

/**
 * 资产类型
 */
export enum AssetType {
  MODEL = 'model',
  TEXTURE = 'texture',
  MATERIAL = 'material',
  AUDIO = 'audio',
  VIDEO = 'video',
  SCRIPT = 'script',
  SCENE = 'scene',
  FOLDER = 'folder'
}

/**
 * 资产项数据
 */
interface AssetItem {
  id: string
  name: string
  type: AssetType
  path: string
  size?: number
  thumbnail?: string
  lastModified?: Date
  children?: AssetItem[]
}

/**
 * 资产面板属性
 */
export interface AssetsPanelProps {
  /** 面板类名 */
  className?: string
  /** 面板样式 */
  style?: React.CSSProperties
}

/**
 * 资产面板组件
 */
const AssetsPanel: React.FC<AssetsPanelProps> = ({
  className = '',
  style = {}
}) => {
  const editorState = useHookstate(EditorState)
  const [currentPath, setCurrentPath] = useState('/')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedAssets, setSelectedAssets] = useState<Set<string>>(new Set())
  const [sortBy, setSortBy] = useState<'name' | 'type' | 'size' | 'date'>('name')
  
  // 模拟资产数据
  const [assetsData] = useState<AssetItem[]>([
    {
      id: 'models',
      name: '模型',
      type: AssetType.FOLDER,
      path: '/models',
      children: [
        {
          id: 'cube.gltf',
          name: 'cube.gltf',
          type: AssetType.MODEL,
          path: '/models/cube.gltf',
          size: 1024,
          lastModified: new Date('2024-01-15')
        },
        {
          id: 'sphere.gltf',
          name: 'sphere.gltf',
          type: AssetType.MODEL,
          path: '/models/sphere.gltf',
          size: 2048,
          lastModified: new Date('2024-01-16')
        }
      ]
    },
    {
      id: 'textures',
      name: '纹理',
      type: AssetType.FOLDER,
      path: '/textures',
      children: [
        {
          id: 'brick.jpg',
          name: 'brick.jpg',
          type: AssetType.TEXTURE,
          path: '/textures/brick.jpg',
          size: 512,
          lastModified: new Date('2024-01-10')
        },
        {
          id: 'wood.png',
          name: 'wood.png',
          type: AssetType.TEXTURE,
          path: '/textures/wood.png',
          size: 1024,
          lastModified: new Date('2024-01-12')
        }
      ]
    },
    {
      id: 'materials',
      name: '材质',
      type: AssetType.FOLDER,
      path: '/materials',
      children: [
        {
          id: 'metal.mat',
          name: 'metal.mat',
          type: AssetType.MATERIAL,
          path: '/materials/metal.mat',
          size: 256,
          lastModified: new Date('2024-01-14')
        }
      ]
    },
    {
      id: 'audio',
      name: '音频',
      type: AssetType.FOLDER,
      path: '/audio',
      children: [
        {
          id: 'bgm.mp3',
          name: 'bgm.mp3',
          type: AssetType.AUDIO,
          path: '/audio/bgm.mp3',
          size: 4096,
          lastModified: new Date('2024-01-08')
        }
      ]
    }
  ])
  
  /**
   * 获取资产图标
   */
  const getAssetIcon = (type: AssetType) => {
    switch (type) {
      case AssetType.FOLDER: return '📁'
      case AssetType.MODEL: return '🎲'
      case AssetType.TEXTURE: return '🖼️'
      case AssetType.MATERIAL: return '🎨'
      case AssetType.AUDIO: return '🎵'
      case AssetType.VIDEO: return '🎬'
      case AssetType.SCRIPT: return '📜'
      case AssetType.SCENE: return '🌍'
      default: return '📄'
    }
  }
  
  /**
   * 格式化文件大小
   */
  const formatFileSize = (bytes?: number) => {
    if (!bytes) return ''
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }
  
  /**
   * 获取当前目录的资产
   */
  const getCurrentAssets = (): AssetItem[] => {
    if (currentPath === '/') {
      return assetsData
    }
    
    // 递归查找当前路径的资产
    const findAssets = (items: AssetItem[], path: string): AssetItem[] => {
      for (const item of items) {
        if (item.path === path && item.children) {
          return item.children
        }
        if (item.children) {
          const found = findAssets(item.children, path)
          if (found.length > 0) return found
        }
      }
      return []
    }
    
    return findAssets(assetsData, currentPath)
  }
  
  /**
   * 过滤和排序资产
   */
  const getFilteredAssets = (): AssetItem[] => {
    let assets = getCurrentAssets()
    
    // 搜索过滤
    if (searchTerm) {
      assets = assets.filter(asset => 
        asset.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }
    
    // 排序
    assets.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'type':
          return a.type.localeCompare(b.type)
        case 'size':
          return (b.size || 0) - (a.size || 0)
        case 'date':
          return (b.lastModified?.getTime() || 0) - (a.lastModified?.getTime() || 0)
        default:
          return 0
      }
    })
    
    return assets
  }
  
  /**
   * 处理资产选择
   */
  const handleAssetSelect = useCallback((assetId: string, event: React.MouseEvent) => {
    if (event.ctrlKey || event.metaKey) {
      // 多选模式
      setSelectedAssets(prev => {
        const newSet = new Set(prev)
        if (newSet.has(assetId)) {
          newSet.delete(assetId)
        } else {
          newSet.add(assetId)
        }
        return newSet
      })
    } else {
      // 单选模式
      setSelectedAssets(new Set([assetId]))
    }
  }, [])
  
  /**
   * 处理资产双击
   */
  const handleAssetDoubleClick = useCallback((asset: AssetItem) => {
    if (asset.type === AssetType.FOLDER) {
      // 进入文件夹
      setCurrentPath(asset.path)
    } else {
      // 打开资产
      console.log('打开资产:', asset.name)
    }
  }, [])
  
  /**
   * 处理拖拽开始
   */
  const handleDragStart = useCallback((event: React.DragEvent, asset: AssetItem) => {
    event.dataTransfer.setData('application/json', JSON.stringify(asset))
    event.dataTransfer.effectAllowed = 'copy'
  }, [])
  
  /**
   * 导航到上级目录
   */
  const navigateUp = () => {
    const pathParts = currentPath.split('/').filter(Boolean)
    if (pathParts.length > 0) {
      pathParts.pop()
      setCurrentPath('/' + pathParts.join('/'))
    }
  }
  
  /**
   * 处理文件导入
   */
  const handleImport = () => {
    // TODO: 实现文件导入逻辑
    console.log('导入文件')
  }
  
  /**
   * 处理新建文件夹
   */
  const handleNewFolder = () => {
    // TODO: 实现新建文件夹逻辑
    console.log('新建文件夹')
  }
  
  /**
   * 渲染网格视图
   */
  const renderGridView = (assets: AssetItem[]) => (
    <div className="assets-grid">
      {assets.map(asset => (
        <div
          key={asset.id}
          className={`asset-item ${selectedAssets.has(asset.id) ? 'selected' : ''}`}
          onClick={(e) => handleAssetSelect(asset.id, e)}
          onDoubleClick={() => handleAssetDoubleClick(asset)}
          draggable={asset.type !== AssetType.FOLDER}
          onDragStart={(e) => handleDragStart(e, asset)}
          title={`${asset.name}\n${formatFileSize(asset.size)}\n${asset.lastModified?.toLocaleDateString()}`}
        >
          <div className="asset-thumbnail">
            {getAssetIcon(asset.type)}
          </div>
          <div className="asset-name">{asset.name}</div>
        </div>
      ))}
    </div>
  )
  
  /**
   * 渲染列表视图
   */
  const renderListView = (assets: AssetItem[]) => (
    <div className="assets-list">
      <div className="list-header grid grid-cols-4 gap-2 p-2 text-xs font-semibold text-gray-400 border-b border-gray-600">
        <div>名称</div>
        <div>类型</div>
        <div>大小</div>
        <div>修改时间</div>
      </div>
      {assets.map(asset => (
        <div
          key={asset.id}
          className={`list-item grid grid-cols-4 gap-2 p-2 text-sm cursor-pointer hover:bg-gray-700 ${
            selectedAssets.has(asset.id) ? 'bg-blue-600' : ''
          }`}
          onClick={(e) => handleAssetSelect(asset.id, e)}
          onDoubleClick={() => handleAssetDoubleClick(asset)}
          draggable={asset.type !== AssetType.FOLDER}
          onDragStart={(e) => handleDragStart(e, asset)}
        >
          <div className="flex items-center gap-2">
            <span>{getAssetIcon(asset.type)}</span>
            <span>{asset.name}</span>
          </div>
          <div>{asset.type}</div>
          <div>{formatFileSize(asset.size)}</div>
          <div>{asset.lastModified?.toLocaleDateString()}</div>
        </div>
      ))}
    </div>
  )
  
  const filteredAssets = getFilteredAssets()
  
  return (
    <div className={`assets-panel ${className}`} style={style}>
      {/* 面板头部 */}
      <div className="panel-header">
        <span>资产</span>
        <div className="flex items-center gap-2">
          <button
            className="text-xs px-2 py-1 bg-blue-600 hover:bg-blue-500 rounded"
            onClick={handleImport}
            title="导入资产"
          >
            📥
          </button>
          <button
            className="text-xs px-2 py-1 bg-green-600 hover:bg-green-500 rounded"
            onClick={handleNewFolder}
            title="新建文件夹"
          >
            📁+
          </button>
        </div>
      </div>
      
      {/* 工具栏 */}
      <div className="p-2 border-b border-gray-600 space-y-2">
        {/* 路径导航 */}
        <div className="flex items-center gap-2 text-sm">
          <button
            className="px-2 py-1 bg-gray-600 hover:bg-gray-500 rounded disabled:opacity-50"
            onClick={navigateUp}
            disabled={currentPath === '/'}
            title="上级目录"
          >
            ⬆️
          </button>
          <span className="text-gray-400">{currentPath}</span>
        </div>
        
        {/* 搜索和视图控制 */}
        <div className="flex items-center gap-2">
          <input
            type="text"
            placeholder="搜索资产..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1 px-2 py-1 bg-gray-700 border border-gray-600 rounded text-sm text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
          />
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-2 py-1 bg-gray-700 border border-gray-600 rounded text-sm"
          >
            <option value="name">名称</option>
            <option value="type">类型</option>
            <option value="size">大小</option>
            <option value="date">日期</option>
          </select>
          <button
            className={`px-2 py-1 rounded text-sm ${viewMode === 'grid' ? 'bg-blue-600' : 'bg-gray-600 hover:bg-gray-500'}`}
            onClick={() => setViewMode('grid')}
            title="网格视图"
          >
            ⊞
          </button>
          <button
            className={`px-2 py-1 rounded text-sm ${viewMode === 'list' ? 'bg-blue-600' : 'bg-gray-600 hover:bg-gray-500'}`}
            onClick={() => setViewMode('list')}
            title="列表视图"
          >
            ☰
          </button>
        </div>
      </div>
      
      {/* 资产内容 */}
      <div className="panel-content">
        {filteredAssets.length > 0 ? (
          viewMode === 'grid' ? renderGridView(filteredAssets) : renderListView(filteredAssets)
        ) : (
          <div className="text-center text-gray-400 py-8">
            <div className="text-4xl mb-2">📁</div>
            <div>没有找到资产</div>
            <div className="text-sm">
              {searchTerm ? '尝试修改搜索条件' : '点击导入按钮添加资产'}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default AssetsPanel
