/**
 * DL-Engine 编辑器主题系统
 * 
 * 基于 Ant Design 5.0 的主题定制，专为数字化学习场景优化
 */

import type { ThemeConfig } from 'antd'
import { theme } from 'antd'

/**
 * 主题类型
 */
export type ThemeType = 'light' | 'dark' | 'education'

/**
 * 自定义主题配置
 */
export interface DLThemeConfig extends ThemeConfig {
  name: ThemeType
  displayName: string
  description: string
}

/**
 * 基础颜色配置
 */
export const colors = {
  // 主色调
  primary: '#007acc',
  primaryHover: '#1890ff',
  primaryActive: '#0056b3',
  
  // 功能色
  success: '#52c41a',
  warning: '#faad14',
  error: '#f5222d',
  info: '#1890ff',
  
  // 中性色
  white: '#ffffff',
  black: '#000000',
  
  // 灰度色
  gray: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#f0f0f0',
    300: '#d9d9d9',
    400: '#bfbfbf',
    500: '#8c8c8c',
    600: '#595959',
    700: '#434343',
    800: '#262626',
    900: '#1f1f1f'
  },
  
  // 编辑器专用色
  editor: {
    background: '#1e1e1e',
    surface: '#2d2d30',
    border: '#3c3c3c',
    text: '#ffffff',
    textSecondary: '#cccccc',
    textMuted: '#888888',
    selection: '#007acc',
    hover: '#37373d'
  }
}

/**
 * 深色主题配置
 */
export const darkTheme: DLThemeConfig = {
  name: 'dark',
  displayName: '深色主题',
  description: '适合长时间编辑工作的深色主题',
  algorithm: theme.darkAlgorithm,
  token: {
    // 基础色彩
    colorPrimary: colors.primary,
    colorSuccess: colors.success,
    colorWarning: colors.warning,
    colorError: colors.error,
    colorInfo: colors.info,
    
    // 背景色
    colorBgBase: colors.editor.background,
    colorBgContainer: colors.editor.surface,
    colorBgElevated: colors.editor.surface,
    colorBgLayout: colors.editor.background,
    
    // 边框色
    colorBorder: colors.editor.border,
    colorBorderSecondary: colors.gray[700],
    
    // 文字色
    colorText: colors.editor.text,
    colorTextSecondary: colors.editor.textSecondary,
    colorTextTertiary: colors.editor.textMuted,
    colorTextQuaternary: colors.gray[600],
    
    // 字体
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif',
    fontSize: 14,
    fontSizeHeading1: 32,
    fontSizeHeading2: 24,
    fontSizeHeading3: 20,
    fontSizeHeading4: 16,
    fontSizeHeading5: 14,
    
    // 圆角
    borderRadius: 4,
    borderRadiusLG: 6,
    borderRadiusSM: 3,
    
    // 间距
    padding: 16,
    paddingLG: 24,
    paddingSM: 12,
    paddingXS: 8,
    paddingXXS: 4,
    
    margin: 16,
    marginLG: 24,
    marginSM: 12,
    marginXS: 8,
    marginXXS: 4,
    
    // 阴影
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
    boxShadowSecondary: '0 4px 12px rgba(0, 0, 0, 0.15)',
    
    // 动画
    motionDurationFast: '0.1s',
    motionDurationMid: '0.2s',
    motionDurationSlow: '0.3s'
  },
  components: {
    // 按钮组件
    Button: {
      colorPrimary: colors.primary,
      algorithm: true
    },
    
    // 输入框组件
    Input: {
      colorBgContainer: colors.gray[800],
      colorBorder: colors.editor.border,
      colorText: colors.editor.text,
      algorithm: true
    },
    
    // 选择器组件
    Select: {
      colorBgContainer: colors.gray[800],
      colorBgElevated: colors.editor.surface,
      algorithm: true
    },
    
    // 表格组件
    Table: {
      colorBgContainer: colors.editor.surface,
      colorBorderSecondary: colors.editor.border,
      algorithm: true
    },
    
    // 菜单组件
    Menu: {
      colorBgContainer: colors.editor.surface,
      colorItemBg: 'transparent',
      colorItemBgHover: colors.editor.hover,
      colorItemBgSelected: colors.primary,
      algorithm: true
    },
    
    // 标签页组件
    Tabs: {
      colorBgContainer: colors.editor.surface,
      colorBorderSecondary: colors.editor.border,
      algorithm: true
    },
    
    // 卡片组件
    Card: {
      colorBgContainer: colors.editor.surface,
      colorBorderSecondary: colors.editor.border,
      algorithm: true
    },
    
    // 模态框组件
    Modal: {
      colorBgElevated: colors.editor.surface,
      algorithm: true
    },
    
    // 抽屉组件
    Drawer: {
      colorBgElevated: colors.editor.surface,
      algorithm: true
    },
    
    // 工具提示组件
    Tooltip: {
      colorBgSpotlight: colors.gray[800],
      algorithm: true
    },
    
    // 下拉菜单组件
    Dropdown: {
      colorBgElevated: colors.editor.surface,
      algorithm: true
    }
  }
}

/**
 * 浅色主题配置
 */
export const lightTheme: DLThemeConfig = {
  name: 'light',
  displayName: '浅色主题',
  description: '经典的浅色主题，适合明亮环境',
  algorithm: theme.defaultAlgorithm,
  token: {
    colorPrimary: colors.primary,
    colorSuccess: colors.success,
    colorWarning: colors.warning,
    colorError: colors.error,
    colorInfo: colors.info,
    
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif',
    fontSize: 14,
    borderRadius: 4
  }
}

/**
 * 教育主题配置
 */
export const educationTheme: DLThemeConfig = {
  name: 'education',
  displayName: '教育主题',
  description: '专为教育场景设计的温和主题',
  algorithm: theme.defaultAlgorithm,
  token: {
    // 教育场景专用色彩
    colorPrimary: '#52c41a', // 绿色主调，象征成长和学习
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff7875',
    colorInfo: '#40a9ff',
    
    // 温和的背景色
    colorBgBase: '#f8f9fa',
    colorBgContainer: '#ffffff',
    colorBgElevated: '#ffffff',
    
    // 柔和的边框色
    colorBorder: '#e8e8e8',
    colorBorderSecondary: '#f0f0f0',
    
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif',
    fontSize: 14,
    borderRadius: 6, // 更圆润的边角
    
    // 更大的间距，便于触摸操作
    padding: 20,
    paddingLG: 28,
    paddingSM: 16,
    
    // 柔和的阴影
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
    boxShadowSecondary: '0 4px 12px rgba(0, 0, 0, 0.08)'
  },
  components: {
    Button: {
      colorPrimary: '#52c41a',
      borderRadius: 6,
      algorithm: true
    },
    
    Card: {
      borderRadius: 8,
      algorithm: true
    },
    
    Input: {
      borderRadius: 6,
      algorithm: true
    }
  }
}

/**
 * 主题映射
 */
export const themes: Record<ThemeType, DLThemeConfig> = {
  light: lightTheme,
  dark: darkTheme,
  education: educationTheme
}

/**
 * 默认主题
 */
export const defaultTheme = darkTheme

/**
 * 获取主题配置
 */
export function getTheme(type: ThemeType): DLThemeConfig {
  return themes[type] || defaultTheme
}

/**
 * 主题工具函数
 */
export const themeUtils = {
  /**
   * 获取所有可用主题
   */
  getAllThemes(): DLThemeConfig[] {
    return Object.values(themes)
  },
  
  /**
   * 检查主题是否存在
   */
  hasTheme(type: string): type is ThemeType {
    return type in themes
  },
  
  /**
   * 获取主题显示名称
   */
  getThemeDisplayName(type: ThemeType): string {
    return themes[type]?.displayName || type
  },
  
  /**
   * 获取主题描述
   */
  getThemeDescription(type: ThemeType): string {
    return themes[type]?.description || ''
  }
}
