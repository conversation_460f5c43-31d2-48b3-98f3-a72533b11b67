/**
 * 可视化脚本编辑器类型定义
 */

import { ReactNode } from 'react'

/**
 * 节点类型
 */
export enum NodeType {
  // 基础节点
  START = 'start',
  END = 'end',
  COMMENT = 'comment',
  
  // 数据节点
  CONSTANT = 'constant',
  VARIABLE = 'variable',
  PARAMETER = 'parameter',
  
  // 数学节点
  ADD = 'add',
  SUBTRACT = 'subtract',
  MULTIPLY = 'multiply',
  DIVIDE = 'divide',
  MODULO = 'modulo',
  POWER = 'power',
  SQRT = 'sqrt',
  ABS = 'abs',
  MIN = 'min',
  MAX = 'max',
  RANDOM = 'random',
  
  // 逻辑节点
  AND = 'and',
  OR = 'or',
  NOT = 'not',
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  GREATER = 'greater',
  LESS = 'less',
  GREATER_EQUAL = 'greater_equal',
  LESS_EQUAL = 'less_equal',
  
  // 控制流节点
  IF = 'if',
  WHILE = 'while',
  FOR = 'for',
  SWITCH = 'switch',
  BREAK = 'break',
  CONTINUE = 'continue',
  
  // 函数节点
  FUNCTION = 'function',
  CALL = 'call',
  RETURN = 'return',
  
  // 事件节点
  ON_START = 'on_start',
  ON_UPDATE = 'on_update',
  ON_CLICK = 'on_click',
  ON_COLLISION = 'on_collision',
  ON_TRIGGER = 'on_trigger',
  
  // 对象节点
  GET_COMPONENT = 'get_component',
  SET_PROPERTY = 'set_property',
  GET_PROPERTY = 'get_property',
  CREATE_OBJECT = 'create_object',
  DESTROY_OBJECT = 'destroy_object',
  
  // 教育专用节点
  SHOW_MESSAGE = 'show_message',
  PLAY_SOUND = 'play_sound',
  MOVE_OBJECT = 'move_object',
  ROTATE_OBJECT = 'rotate_object',
  SCALE_OBJECT = 'scale_object',
  CHANGE_COLOR = 'change_color',
  WAIT = 'wait',
  REPEAT = 'repeat',
  
  // 传感器节点
  DISTANCE_SENSOR = 'distance_sensor',
  COLLISION_SENSOR = 'collision_sensor',
  TIMER = 'timer',
  COUNTER = 'counter',
  
  // 输入输出节点
  KEYBOARD_INPUT = 'keyboard_input',
  MOUSE_INPUT = 'mouse_input',
  PRINT = 'print',
  LOG = 'log'
}

/**
 * 数据类型
 */
export enum DataType {
  ANY = 'any',
  VOID = 'void',
  BOOLEAN = 'boolean',
  NUMBER = 'number',
  STRING = 'string',
  VECTOR2 = 'vector2',
  VECTOR3 = 'vector3',
  COLOR = 'color',
  OBJECT = 'object',
  ARRAY = 'array',
  FUNCTION = 'function',
  EVENT = 'event'
}

/**
 * 连接点类型
 */
export enum SocketType {
  INPUT = 'input',
  OUTPUT = 'output'
}

/**
 * 执行状态
 */
export enum ExecutionState {
  IDLE = 'idle',
  RUNNING = 'running',
  PAUSED = 'paused',
  STOPPED = 'stopped',
  ERROR = 'error',
  COMPLETED = 'completed'
}

/**
 * 节点连接点定义
 */
export interface NodeSocket {
  /** 连接点ID */
  id: string
  /** 连接点名称 */
  name: string
  /** 连接点类型 */
  type: SocketType
  /** 数据类型 */
  dataType: DataType
  /** 是否必需 */
  required?: boolean
  /** 默认值 */
  defaultValue?: any
  /** 描述 */
  description?: string
  /** 是否为执行连接点 */
  isExecution?: boolean
}

/**
 * 节点定义
 */
export interface NodeDefinition {
  /** 节点类型 */
  type: NodeType
  /** 节点名称 */
  name: string
  /** 节点描述 */
  description: string
  /** 节点分类 */
  category: string
  /** 节点图标 */
  icon?: string
  /** 节点颜色 */
  color?: string
  /** 输入连接点 */
  inputs: NodeSocket[]
  /** 输出连接点 */
  outputs: NodeSocket[]
  /** 是否为纯函数（无副作用） */
  pure?: boolean
  /** 是否为教育节点 */
  educational?: boolean
  /** 教育级别 */
  educationLevel?: 'beginner' | 'intermediate' | 'advanced'
  /** 执行函数 */
  execute?: (inputs: Record<string, any>, context: ExecutionContext) => Record<string, any>
}

/**
 * 节点实例
 */
export interface NodeInstance {
  /** 节点ID */
  id: string
  /** 节点类型 */
  type: NodeType
  /** 节点定义 */
  definition: NodeDefinition
  /** 节点位置 */
  position: { x: number; y: number }
  /** 节点大小 */
  size?: { width: number; height: number }
  /** 节点数据 */
  data: Record<string, any>
  /** 是否选中 */
  selected?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 自定义样式 */
  style?: React.CSSProperties
  /** 自定义类名 */
  className?: string
}

/**
 * 连接定义
 */
export interface Connection {
  /** 连接ID */
  id: string
  /** 源节点ID */
  sourceNodeId: string
  /** 源连接点ID */
  sourceSocketId: string
  /** 目标节点ID */
  targetNodeId: string
  /** 目标连接点ID */
  targetSocketId: string
  /** 连接数据 */
  data?: any
  /** 是否为执行连接 */
  isExecution?: boolean
  /** 自定义样式 */
  style?: React.CSSProperties
}

/**
 * 脚本图形
 */
export interface ScriptGraph {
  /** 图形ID */
  id: string
  /** 图形名称 */
  name: string
  /** 图形描述 */
  description?: string
  /** 节点列表 */
  nodes: NodeInstance[]
  /** 连接列表 */
  connections: Connection[]
  /** 变量定义 */
  variables: Variable[]
  /** 参数定义 */
  parameters: Parameter[]
  /** 图形元数据 */
  metadata: Record<string, any>
  /** 创建时间 */
  createdAt: Date
  /** 修改时间 */
  updatedAt: Date
}

/**
 * 变量定义
 */
export interface Variable {
  /** 变量ID */
  id: string
  /** 变量名称 */
  name: string
  /** 变量类型 */
  type: DataType
  /** 默认值 */
  defaultValue?: any
  /** 变量描述 */
  description?: string
  /** 是否为全局变量 */
  global?: boolean
}

/**
 * 参数定义
 */
export interface Parameter {
  /** 参数ID */
  id: string
  /** 参数名称 */
  name: string
  /** 参数类型 */
  type: DataType
  /** 默认值 */
  defaultValue?: any
  /** 参数描述 */
  description?: string
  /** 是否必需 */
  required?: boolean
}

/**
 * 执行上下文
 */
export interface ExecutionContext {
  /** 当前图形 */
  graph: ScriptGraph
  /** 变量存储 */
  variables: Map<string, any>
  /** 参数值 */
  parameters: Map<string, any>
  /** 执行堆栈 */
  callStack: ExecutionFrame[]
  /** 当前帧 */
  currentFrame?: ExecutionFrame
  /** 调试信息 */
  debugInfo?: DebugInfo
  /** 事件系统 */
  eventSystem?: EventSystem
  /** 对象引用 */
  objectRefs: Map<string, any>
}

/**
 * 执行帧
 */
export interface ExecutionFrame {
  /** 帧ID */
  id: string
  /** 当前节点ID */
  currentNodeId: string
  /** 局部变量 */
  localVariables: Map<string, any>
  /** 返回地址 */
  returnAddress?: string
  /** 帧类型 */
  type: 'function' | 'loop' | 'condition'
}

/**
 * 调试信息
 */
export interface DebugInfo {
  /** 断点列表 */
  breakpoints: Set<string>
  /** 当前执行节点 */
  currentNode?: string
  /** 执行历史 */
  executionHistory: ExecutionStep[]
  /** 变量监视 */
  watchedVariables: Set<string>
}

/**
 * 执行步骤
 */
export interface ExecutionStep {
  /** 步骤ID */
  id: string
  /** 节点ID */
  nodeId: string
  /** 执行时间 */
  timestamp: Date
  /** 输入值 */
  inputs: Record<string, any>
  /** 输出值 */
  outputs: Record<string, any>
  /** 执行时长 */
  duration: number
  /** 错误信息 */
  error?: string
}

/**
 * 事件系统
 */
export interface EventSystem {
  /** 注册事件监听器 */
  on(event: string, handler: Function): void
  /** 移除事件监听器 */
  off(event: string, handler: Function): void
  /** 触发事件 */
  emit(event: string, ...args: any[]): void
  /** 一次性事件监听器 */
  once(event: string, handler: Function): void
}

/**
 * 节点编辑器配置
 */
export interface NodeEditorConfig {
  /** 是否只读 */
  readonly?: boolean
  /** 是否显示网格 */
  showGrid?: boolean
  /** 网格大小 */
  gridSize?: number
  /** 是否启用捕捉 */
  snapToGrid?: boolean
  /** 缩放范围 */
  zoomRange?: [number, number]
  /** 是否启用小地图 */
  minimap?: boolean
  /** 是否启用撤销重做 */
  undoRedo?: boolean
  /** 最大撤销步数 */
  maxUndoSteps?: number
  /** 自定义主题 */
  theme?: NodeEditorTheme
}

/**
 * 节点编辑器主题
 */
export interface NodeEditorTheme {
  /** 背景色 */
  backgroundColor?: string
  /** 网格颜色 */
  gridColor?: string
  /** 节点默认颜色 */
  nodeColor?: string
  /** 连接线颜色 */
  connectionColor?: string
  /** 选中颜色 */
  selectionColor?: string
  /** 文字颜色 */
  textColor?: string
}

/**
 * 教育配置
 */
export interface EducationConfig {
  /** 教育级别 */
  level: 'beginner' | 'intermediate' | 'advanced'
  /** 是否启用提示系统 */
  enableHints?: boolean
  /** 是否启用教程 */
  enableTutorial?: boolean
  /** 是否启用进度跟踪 */
  enableProgress?: boolean
  /** 是否启用中文编程 */
  enableChineseProgramming?: boolean
  /** 可用节点类型 */
  allowedNodeTypes?: NodeType[]
  /** 禁用的功能 */
  disabledFeatures?: string[]
}

/**
 * 教程步骤
 */
export interface TutorialStep {
  /** 步骤ID */
  id: string
  /** 步骤标题 */
  title: string
  /** 步骤描述 */
  description: string
  /** 目标元素选择器 */
  target?: string
  /** 步骤位置 */
  position?: 'top' | 'bottom' | 'left' | 'right'
  /** 是否高亮目标 */
  highlight?: boolean
  /** 步骤动作 */
  action?: 'click' | 'drag' | 'input' | 'wait'
  /** 验证函数 */
  validate?: () => boolean
  /** 下一步条件 */
  nextCondition?: () => boolean
}

/**
 * 进度数据
 */
export interface ProgressData {
  /** 用户ID */
  userId: string
  /** 课程ID */
  courseId?: string
  /** 完成的教程 */
  completedTutorials: string[]
  /** 解锁的节点类型 */
  unlockedNodeTypes: NodeType[]
  /** 创建的脚本数量 */
  scriptsCreated: number
  /** 总执行时间 */
  totalExecutionTime: number
  /** 成就列表 */
  achievements: string[]
  /** 最后活动时间 */
  lastActivity: Date
}

/**
 * 提示信息
 */
export interface HintInfo {
  /** 提示ID */
  id: string
  /** 提示类型 */
  type: 'info' | 'warning' | 'error' | 'success'
  /** 提示标题 */
  title: string
  /** 提示内容 */
  content: string
  /** 目标元素 */
  target?: string
  /** 显示时长 */
  duration?: number
  /** 是否自动关闭 */
  autoClose?: boolean
}
