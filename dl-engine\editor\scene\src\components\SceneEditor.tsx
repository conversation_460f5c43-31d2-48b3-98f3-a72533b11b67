/**
 * 场景编辑器主组件
 * 
 * 集成3D视口、对象层次结构、属性面板等功能
 */

import React, { useRef, useEffect, useCallback } from 'react'
import { Canvas } from '@react-three/fiber'
import { useHookstate } from '@hookstate/core'
import { useTranslation } from 'react-i18next'
import { Layout } from '@dl-engine/editor-ui'
import { SceneState, SceneActions } from '../state/SceneState'
import { EditorMode, ViewportType, SceneObject } from '../types'
import Viewport from './Viewport'
import ViewportControls from './ViewportControls'
import ObjectHierarchy from './ObjectHierarchy'
import ObjectInspector from './ObjectInspector'
import LightingPanel from './LightingPanel'
import MaterialEditor from './MaterialEditor'

/**
 * 场景编辑器属性
 */
export interface SceneEditorProps {
  /** 是否只读模式 */
  readonly?: boolean
  
  /** 初始场景数据 */
  initialScene?: any
  
  /** 场景保存回调 */
  onSceneSave?: (sceneData: any) => void
  
  /** 场景加载回调 */
  onSceneLoad?: () => void
  
  /** 对象选择回调 */
  onObjectSelect?: (objects: SceneObject[]) => void
  
  /** 错误回调 */
  onError?: (error: Error) => void
  
  /** 自定义类名 */
  className?: string
  
  /** 自定义样式 */
  style?: React.CSSProperties
}

/**
 * 场景编辑器组件
 */
const SceneEditor: React.FC<SceneEditorProps> = ({
  readonly = false,
  initialScene,
  onSceneSave,
  onSceneLoad,
  onObjectSelect,
  onError,
  className = '',
  style = {}
}) => {
  const { t } = useTranslation()
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const sceneState = useHookstate(SceneState)
  
  // 获取状态
  const currentMode = sceneState.mode.get()
  const viewportType = sceneState.viewportType.get()
  const selectedObjects = sceneState.selection.selectedObjects.get()
  const sceneObjects = sceneState.objects.get()
  const ui = sceneState.ui.get()
  const loading = sceneState.loading.get()
  const error = sceneState.error.get()
  
  /**
   * 初始化场景
   */
  useEffect(() => {
    if (initialScene) {
      SceneActions.loadScene(initialScene)
    }
  }, [initialScene])
  
  /**
   * 处理模式切换
   */
  const handleModeChange = useCallback((mode: EditorMode) => {
    SceneActions.setMode(mode)
  }, [])
  
  /**
   * 处理视口类型切换
   */
  const handleViewportTypeChange = useCallback((type: ViewportType) => {
    SceneActions.setViewportType(type)
  }, [])
  
  /**
   * 处理对象选择
   */
  const handleObjectSelect = useCallback((objectIds: string[]) => {
    SceneActions.setSelectedObjects(objectIds)
    
    const selectedObjectData = objectIds
      .map(id => sceneObjects.find(obj => obj.id === id))
      .filter(Boolean) as SceneObject[]
    
    onObjectSelect?.(selectedObjectData)
  }, [sceneObjects, onObjectSelect])
  
  /**
   * 处理对象创建
   */
  const handleObjectCreate = useCallback((type: string, position?: [number, number, number]) => {
    if (readonly) return
    
    try {
      SceneActions.createObject(type, position)
    } catch (error) {
      onError?.(error as Error)
    }
  }, [readonly, onError])
  
  /**
   * 处理对象删除
   */
  const handleObjectDelete = useCallback((objectIds: string[]) => {
    if (readonly) return
    
    try {
      SceneActions.deleteObjects(objectIds)
    } catch (error) {
      onError?.(error as Error)
    }
  }, [readonly, onError])
  
  /**
   * 处理场景保存
   */
  const handleSceneSave = useCallback(() => {
    try {
      const sceneData = SceneActions.exportScene()
      onSceneSave?.(sceneData)
    } catch (error) {
      onError?.(error as Error)
    }
  }, [onSceneSave, onError])
  
  /**
   * 处理场景加载
   */
  const handleSceneLoad = useCallback(() => {
    try {
      onSceneLoad?.()
    } catch (error) {
      onError?.(error as Error)
    }
  }, [onSceneLoad, onError])
  
  /**
   * 渲染工具栏
   */
  const renderToolbar = () => (
    <div className="scene-editor-toolbar border-b border-gray-300 p-2 flex items-center justify-between">
      {/* 左侧工具 */}
      <div className="flex items-center gap-2">
        {/* 模式切换 */}
        <div className="flex border border-gray-300 rounded overflow-hidden">
          <button
            className={`px-3 py-1 text-sm ${currentMode === EditorMode.SELECT ? 'bg-blue-500 text-white' : 'bg-white hover:bg-gray-100'}`}
            onClick={() => handleModeChange(EditorMode.SELECT)}
            title="选择模式"
          >
            选择
          </button>
          <button
            className={`px-3 py-1 text-sm ${currentMode === EditorMode.MOVE ? 'bg-blue-500 text-white' : 'bg-white hover:bg-gray-100'}`}
            onClick={() => handleModeChange(EditorMode.MOVE)}
            title="移动模式"
          >
            移动
          </button>
          <button
            className={`px-3 py-1 text-sm ${currentMode === EditorMode.ROTATE ? 'bg-blue-500 text-white' : 'bg-white hover:bg-gray-100'}`}
            onClick={() => handleModeChange(EditorMode.ROTATE)}
            title="旋转模式"
          >
            旋转
          </button>
          <button
            className={`px-3 py-1 text-sm ${currentMode === EditorMode.SCALE ? 'bg-blue-500 text-white' : 'bg-white hover:bg-gray-100'}`}
            onClick={() => handleModeChange(EditorMode.SCALE)}
            title="缩放模式"
          >
            缩放
          </button>
        </div>
        
        {/* 创建对象 */}
        {!readonly && (
          <div className="flex gap-1">
            <button
              className="px-2 py-1 bg-green-500 text-white rounded hover:bg-green-600"
              onClick={() => handleObjectCreate('cube')}
              title="创建立方体"
            >
              📦
            </button>
            <button
              className="px-2 py-1 bg-green-500 text-white rounded hover:bg-green-600"
              onClick={() => handleObjectCreate('sphere')}
              title="创建球体"
            >
              ⚪
            </button>
            <button
              className="px-2 py-1 bg-green-500 text-white rounded hover:bg-green-600"
              onClick={() => handleObjectCreate('light')}
              title="创建光源"
            >
              💡
            </button>
            <button
              className="px-2 py-1 bg-green-500 text-white rounded hover:bg-green-600"
              onClick={() => handleObjectCreate('camera')}
              title="创建相机"
            >
              📷
            </button>
          </div>
        )}
      </div>
      
      {/* 中间信息 */}
      <div className="flex items-center gap-4 text-sm text-gray-600">
        <span>对象: {sceneObjects.length}</span>
        <span>选中: {selectedObjects.length}</span>
        <span>模式: {currentMode}</span>
      </div>
      
      {/* 右侧操作 */}
      <div className="flex items-center gap-2">
        {!readonly && (
          <>
            <button
              className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
              onClick={handleSceneSave}
              title="保存场景"
            >
              保存
            </button>
            <button
              className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600"
              onClick={handleSceneLoad}
              title="加载场景"
            >
              加载
            </button>
          </>
        )}
        
        {/* 视口类型切换 */}
        <select
          value={viewportType}
          onChange={(e) => handleViewportTypeChange(e.target.value as ViewportType)}
          className="px-2 py-1 border border-gray-300 rounded text-sm"
        >
          <option value={ViewportType.PERSPECTIVE}>透视</option>
          <option value={ViewportType.ORTHOGRAPHIC}>正交</option>
          <option value={ViewportType.TOP}>顶视图</option>
          <option value={ViewportType.FRONT}>前视图</option>
          <option value={ViewportType.RIGHT}>右视图</option>
        </select>
      </div>
    </div>
  )
  
  /**
   * 渲染主内容
   */
  const renderMainContent = () => {
    if (loading.isLoading) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="text-2xl mb-2">⏳</div>
            <div>{loading.message || '加载中...'}</div>
          </div>
        </div>
      )
    }
    
    if (error.hasError) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-red-500">
            <div className="text-2xl mb-2">❌</div>
            <div>{error.message}</div>
            <button
              className="mt-2 px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
              onClick={() => SceneActions.clearError()}
            >
              重试
            </button>
          </div>
        </div>
      )
    }
    
    return (
      <div className="flex-1 relative">
        <Canvas
          ref={canvasRef}
          camera={{ position: [5, 5, 5], fov: 75 }}
          shadows
          className="w-full h-full"
        >
          <Viewport
            mode={currentMode}
            viewportType={viewportType}
            selectedObjects={selectedObjects}
            onObjectSelect={handleObjectSelect}
            onObjectCreate={handleObjectCreate}
            readonly={readonly}
          />
        </Canvas>
        
        {/* 视口控制 */}
        <ViewportControls
          mode={currentMode}
          viewportType={viewportType}
          onModeChange={handleModeChange}
          onViewportTypeChange={handleViewportTypeChange}
        />
      </div>
    )
  }
  
  return (
    <div className={`scene-editor ${className}`} style={style}>
      <Layout direction="horizontal" className="h-full">
        {/* 左侧面板 - 对象层次结构 */}
        <Layout.Sider width={ui.hierarchyWidth} className="border-r border-gray-300">
          <ObjectHierarchy
            objects={sceneObjects}
            selectedObjects={selectedObjects}
            onObjectSelect={handleObjectSelect}
            onObjectCreate={handleObjectCreate}
            onObjectDelete={handleObjectDelete}
            readonly={readonly}
          />
        </Layout.Sider>
        
        {/* 中央编辑区域 */}
        <Layout.Content className="flex-1">
          <Layout direction="vertical" className="h-full">
            {/* 工具栏 */}
            {renderToolbar()}
            
            {/* 主视口 */}
            {renderMainContent()}
          </Layout>
        </Layout.Content>
        
        {/* 右侧面板 */}
        <Layout.Sider width={ui.inspectorWidth} className="border-l border-gray-300">
          <Layout direction="vertical" className="h-full">
            {/* 对象检查器 */}
            <div className="flex-1 border-b border-gray-300">
              <ObjectInspector
                selectedObjects={selectedObjects.map(id => 
                  sceneObjects.find(obj => obj.id === id)
                ).filter(Boolean) as SceneObject[]}
                readonly={readonly}
                onObjectUpdate={(objectId, updates) => {
                  SceneActions.updateObject(objectId, updates)
                }}
              />
            </div>
            
            {/* 光照面板 */}
            {ui.showLightingPanel && (
              <div className="h-48 border-b border-gray-300">
                <LightingPanel
                  readonly={readonly}
                  onLightCreate={(lightType) => {
                    handleObjectCreate(lightType)
                  }}
                />
              </div>
            )}
            
            {/* 材质编辑器 */}
            {ui.showMaterialEditor && (
              <div className="h-64">
                <MaterialEditor
                  readonly={readonly}
                  selectedObjects={selectedObjects}
                />
              </div>
            )}
          </Layout>
        </Layout.Sider>
      </Layout>
    </div>
  )
}

export default SceneEditor
