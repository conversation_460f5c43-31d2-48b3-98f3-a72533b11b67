/**
 * 变换操作工具组件
 * 
 * 提供移动、旋转、缩放的可视化操作工具
 */

import React from 'react'
import { EditorTool } from '../../types'
import MoveGizmo from './MoveGizmo'
import RotateGizmo from './RotateGizmo'
import ScaleGizmo from './ScaleGizmo'

/**
 * 变换工具属性
 */
export interface TransformGizmoProps {
  /** 当前工具模式 */
  mode: EditorTool
  /** 是否可见 */
  visible?: boolean
  /** 工具大小 */
  size?: number
  /** 工具位置 */
  position?: [number, number, number]
}

/**
 * 变换工具组件
 */
const TransformGizmo: React.FC<TransformGizmoProps> = ({
  mode,
  visible = true,
  size = 1,
  position = [0, 0, 0]
}) => {
  if (!visible) return null

  return (
    <group position={position}>
      {mode === EditorTool.MOVE && <MoveGizmo size={size} />}
      {mode === EditorTool.ROTATE && <RotateGizmo size={size} />}
      {mode === EditorTool.SCALE && <ScaleGizmo size={size} />}
    </group>
  )
}

export default TransformGizmo
