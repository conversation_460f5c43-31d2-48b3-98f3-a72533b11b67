/**
 * 缩放操作工具组件
 */

import React from 'react'

export interface ScaleGizmoProps {
  size?: number
}

const ScaleGizmo: React.FC<ScaleGizmoProps> = ({ size = 1 }) => {
  return (
    <group>
      {/* X轴 - 红色 */}
      <group>
        <mesh position={[size, 0, 0]}>
          <cylinderGeometry args={[0.02 * size, 0.02 * size, size * 2, 8]} rotation={[0, 0, Math.PI / 2]} />
          <meshBasicMaterial color="red" />
        </mesh>
        <mesh position={[size * 2, 0, 0]}>
          <boxGeometry args={[0.2 * size, 0.2 * size, 0.2 * size]} />
          <meshBasicMaterial color="red" />
        </mesh>
      </group>
      
      {/* Y轴 - 绿色 */}
      <group>
        <mesh position={[0, size, 0]}>
          <cylinderGeometry args={[0.02 * size, 0.02 * size, size * 2, 8]} />
          <meshBasicMaterial color="green" />
        </mesh>
        <mesh position={[0, size * 2, 0]}>
          <boxGeometry args={[0.2 * size, 0.2 * size, 0.2 * size]} />
          <meshBasicMaterial color="green" />
        </mesh>
      </group>
      
      {/* Z轴 - 蓝色 */}
      <group>
        <mesh position={[0, 0, size]}>
          <cylinderGeometry args={[0.02 * size, 0.02 * size, size * 2, 8]} rotation={[Math.PI / 2, 0, 0]} />
          <meshBasicMaterial color="blue" />
        </mesh>
        <mesh position={[0, 0, size * 2]}>
          <boxGeometry args={[0.2 * size, 0.2 * size, 0.2 * size]} />
          <meshBasicMaterial color="blue" />
        </mesh>
      </group>
      
      {/* 中心立方体 - 白色 */}
      <mesh>
        <boxGeometry args={[0.15 * size, 0.15 * size, 0.15 * size]} />
        <meshBasicMaterial color="white" />
      </mesh>
    </group>
  )
}

export default ScaleGizmo
