/**
 * DL-Engine 图标组件
 * 
 * 统一的图标系统，支持 Ant Design 图标和自定义图标
 */

import React from 'react'
import * as AntdIcons from '@ant-design/icons'
import classNames from 'classnames'

/**
 * DL图标属性
 */
export interface DLIconProps {
  /** 图标名称 */
  name: string
  /** 图标大小 */
  size?: 'small' | 'medium' | 'large' | number
  /** 图标颜色 */
  color?: string
  /** 是否旋转 */
  spin?: boolean
  /** 自定义类名 */
  className?: string
  /** 自定义样式 */
  style?: React.CSSProperties
  /** 点击事件 */
  onClick?: (event: React.MouseEvent) => void
}

/**
 * 图标名称映射
 */
const iconMap: Record<string, React.ComponentType<any>> = {
  // 文件操作
  'file-new': AntdIcons.FileAddOutlined,
  'file-open': AntdIcons.FolderOpenOutlined,
  'file-save': AntdIcons.SaveOutlined,
  'file-close': AntdIcons.CloseOutlined,
  
  // 编辑操作
  'edit-undo': AntdIcons.UndoOutlined,
  'edit-redo': AntdIcons.RedoOutlined,
  'edit-copy': AntdIcons.CopyOutlined,
  'edit-paste': AntdIcons.SnippetsOutlined,
  'edit-cut': AntdIcons.ScissorOutlined,
  'edit-delete': AntdIcons.DeleteOutlined,
  
  // 工具
  'tool-select': AntdIcons.AimOutlined,
  'tool-move': AntdIcons.DragOutlined,
  'tool-rotate': AntdIcons.RotateRightOutlined,
  'tool-scale': AntdIcons.ExpandAltOutlined,
  'tool-brush': AntdIcons.BgColorsOutlined,
  'tool-terrain': AntdIcons.EnvironmentOutlined,
  
  // 视图
  'view-perspective': AntdIcons.EyeOutlined,
  'view-orthographic': AntdIcons.BorderOutlined,
  'view-grid': AntdIcons.BorderOuterOutlined,
  'view-wireframe': AntdIcons.NodeIndexOutlined,
  
  // 对象类型
  'object-cube': AntdIcons.BorderOutlined,
  'object-sphere': AntdIcons.RadiusSettingOutlined,
  'object-cylinder': AntdIcons.ColumnHeightOutlined,
  'object-plane': AntdIcons.BorderOutlined,
  'object-camera': AntdIcons.CameraOutlined,
  'object-light': AntdIcons.BulbOutlined,
  'object-group': AntdIcons.GroupOutlined,
  
  // 面板
  'panel-hierarchy': AntdIcons.ApartmentOutlined,
  'panel-properties': AntdIcons.SettingOutlined,
  'panel-assets': AntdIcons.FolderOutlined,
  'panel-materials': AntdIcons.BgColorsOutlined,
  'panel-console': AntdIcons.CodeOutlined,
  'panel-inspector': AntdIcons.SearchOutlined,
  
  // 播放控制
  'play': AntdIcons.CaretRightOutlined,
  'pause': AntdIcons.PauseOutlined,
  'stop': AntdIcons.BorderOutlined,
  'record': AntdIcons.VideoCameraOutlined,
  
  // 常用图标
  'add': AntdIcons.PlusOutlined,
  'remove': AntdIcons.MinusOutlined,
  'search': AntdIcons.SearchOutlined,
  'filter': AntdIcons.FilterOutlined,
  'sort': AntdIcons.SortAscendingOutlined,
  'refresh': AntdIcons.ReloadOutlined,
  'settings': AntdIcons.SettingOutlined,
  'help': AntdIcons.QuestionCircleOutlined,
  'info': AntdIcons.InfoCircleOutlined,
  'warning': AntdIcons.WarningOutlined,
  'error': AntdIcons.CloseCircleOutlined,
  'success': AntdIcons.CheckCircleOutlined,
  'loading': AntdIcons.LoadingOutlined,
  
  // 方向
  'arrow-up': AntdIcons.ArrowUpOutlined,
  'arrow-down': AntdIcons.ArrowDownOutlined,
  'arrow-left': AntdIcons.ArrowLeftOutlined,
  'arrow-right': AntdIcons.ArrowRightOutlined,
  'expand': AntdIcons.ExpandOutlined,
  'collapse': AntdIcons.ShrinkOutlined,
  
  // 可见性
  'visible': AntdIcons.EyeOutlined,
  'hidden': AntdIcons.EyeInvisibleOutlined,
  'lock': AntdIcons.LockOutlined,
  'unlock': AntdIcons.UnlockOutlined,
  
  // 媒体
  'image': AntdIcons.PictureOutlined,
  'video': AntdIcons.VideoCameraOutlined,
  'audio': AntdIcons.AudioOutlined,
  'model': AntdIcons.BoxPlotOutlined,
  'material': AntdIcons.BgColorsOutlined,
  'texture': AntdIcons.PictureOutlined,
  'script': AntdIcons.CodeOutlined,
  'scene': AntdIcons.GlobalOutlined,
  'folder': AntdIcons.FolderOutlined,
  'file': AntdIcons.FileOutlined
}

/**
 * DL图标组件
 */
const DLIcon: React.FC<DLIconProps> = ({
  name,
  size = 'medium',
  color,
  spin = false,
  className,
  style,
  onClick
}) => {
  // 获取图标组件
  const IconComponent = iconMap[name] || AntdIcons.QuestionOutlined
  
  // 计算图标大小
  const getIconSize = () => {
    if (typeof size === 'number') return size
    switch (size) {
      case 'small': return 12
      case 'medium': return 16
      case 'large': return 20
      default: return 16
    }
  }
  
  // 构建样式
  const iconStyle: React.CSSProperties = {
    fontSize: getIconSize(),
    color,
    ...style
  }
  
  // 构建类名
  const iconClassName = classNames(
    'dl-icon',
    `dl-icon--${name}`,
    `dl-icon--${size}`,
    {
      'dl-icon--spin': spin,
      'dl-icon--clickable': !!onClick
    },
    className
  )
  
  return (
    <IconComponent
      className={iconClassName}
      style={iconStyle}
      spin={spin}
      onClick={onClick}
    />
  )
}

export default DLIcon
