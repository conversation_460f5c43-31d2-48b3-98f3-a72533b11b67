/**
 * 编辑器核心类型定义
 */

import { Entity } from '@dl-engine/engine-ecs'
import { Vector3, Quaternion, Euler } from 'three'

/**
 * 编辑器模式
 */
export enum EditorMode {
  /** 选择模式 */
  SELECT = 'select',
  /** 移动模式 */
  TRANSLATE = 'translate',
  /** 旋转模式 */
  ROTATE = 'rotate',
  /** 缩放模式 */
  SCALE = 'scale',
  /** 飞行模式 */
  FLY = 'fly'
}

/**
 * 编辑器工具类型
 */
export enum EditorTool {
  /** 选择工具 */
  SELECT = 'select',
  /** 移动工具 */
  MOVE = 'move',
  /** 旋转工具 */
  ROTATE = 'rotate',
  /** 缩放工具 */
  SCALE = 'scale',
  /** 画笔工具 */
  BRUSH = 'brush',
  /** 地形工具 */
  TERRAIN = 'terrain'
}

/**
 * 编辑器视口类型
 */
export enum ViewportType {
  /** 透视视图 */
  PERSPECTIVE = 'perspective',
  /** 正交视图 */
  ORTHOGRAPHIC = 'orthographic',
  /** 顶视图 */
  TOP = 'top',
  /** 前视图 */
  FRONT = 'front',
  /** 右视图 */
  RIGHT = 'right'
}

/**
 * 编辑器面板类型
 */
export enum PanelType {
  /** 场景面板 */
  SCENE = 'scene',
  /** 层次结构面板 */
  HIERARCHY = 'hierarchy',
  /** 属性面板 */
  PROPERTIES = 'properties',
  /** 资产面板 */
  ASSETS = 'assets',
  /** 材质面板 */
  MATERIALS = 'materials',
  /** 控制台面板 */
  CONSOLE = 'console',
  /** 检查器面板 */
  INSPECTOR = 'inspector'
}

/**
 * 编辑器操作类型
 */
export interface EditorAction {
  /** 操作类型 */
  type: string
  /** 操作数据 */
  payload?: any
  /** 操作时间戳 */
  timestamp: number
  /** 操作描述 */
  description?: string
}

/**
 * 编辑器选择状态
 */
export interface SelectionState {
  /** 选中的实体列表 */
  selectedEntities: Entity[]
  /** 主选中实体 */
  primaryEntity?: Entity
  /** 选择框 */
  selectionBox?: {
    start: Vector3
    end: Vector3
  }
}

/**
 * 编辑器变换状态
 */
export interface TransformState {
  /** 位置 */
  position: Vector3
  /** 旋转 */
  rotation: Quaternion
  /** 欧拉角 */
  euler: Euler
  /** 缩放 */
  scale: Vector3
}

/**
 * 编辑器相机状态
 */
export interface CameraState {
  /** 相机位置 */
  position: Vector3
  /** 相机目标 */
  target: Vector3
  /** 相机旋转 */
  rotation: Quaternion
  /** 视野角度 */
  fov: number
  /** 近裁剪面 */
  near: number
  /** 远裁剪面 */
  far: number
}

/**
 * 编辑器网格设置
 */
export interface GridSettings {
  /** 是否显示网格 */
  visible: boolean
  /** 网格大小 */
  size: number
  /** 网格分割数 */
  divisions: number
  /** 网格颜色 */
  color: string
  /** 中心线颜色 */
  centerLineColor: string
}

/**
 * 编辑器捕捉设置
 */
export interface SnapSettings {
  /** 是否启用位置捕捉 */
  position: boolean
  /** 是否启用旋转捕捉 */
  rotation: boolean
  /** 是否启用缩放捕捉 */
  scale: boolean
  /** 位置捕捉增量 */
  positionIncrement: number
  /** 旋转捕捉增量（度） */
  rotationIncrement: number
  /** 缩放捕捉增量 */
  scaleIncrement: number
}

/**
 * 编辑器历史记录项
 */
export interface HistoryItem {
  /** 操作ID */
  id: string
  /** 操作描述 */
  description: string
  /** 撤销数据 */
  undoData: any
  /** 重做数据 */
  redoData: any
  /** 操作时间 */
  timestamp: number
}

/**
 * 编辑器配置
 */
export interface EditorConfiguration {
  /** 网格设置 */
  grid: GridSettings
  /** 捕捉设置 */
  snap: SnapSettings
  /** 自动保存间隔（毫秒） */
  autoSaveInterval: number
  /** 历史记录最大数量 */
  maxHistoryItems: number
  /** 默认编辑器模式 */
  defaultMode: EditorMode
  /** 默认工具 */
  defaultTool: EditorTool
}

/**
 * 编辑器事件类型
 */
export interface EditorEvents {
  /** 选择改变事件 */
  'selection-changed': SelectionState
  /** 变换改变事件 */
  'transform-changed': TransformState
  /** 模式改变事件 */
  'mode-changed': EditorMode
  /** 工具改变事件 */
  'tool-changed': EditorTool
  /** 场景保存事件 */
  'scene-saved': void
  /** 场景加载事件 */
  'scene-loaded': void
}
