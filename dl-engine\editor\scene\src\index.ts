/**
 * Digital Learning Engine - 场景编辑器
 * 
 * 提供完整的3D场景编辑功能，包括对象操作、光照设置、相机控制等
 */

// 核心场景编辑器
export { default as SceneEditor } from './components/SceneEditor'
export type { SceneEditorProps } from './components/SceneEditor'

// 视口组件
export { default as Viewport } from './components/Viewport'
export { default as ViewportControls } from './components/ViewportControls'
export { default as ViewportGrid } from './components/ViewportGrid'
export { default as ViewportGizmos } from './components/ViewportGizmos'

// 操作工具
export * from './gizmos'

// 选择系统
export * from './selection'

// 相机控制
export * from './camera'

// 场景管理
export * from './scene'

// 对象操作
export * from './objects'

// 光照系统
export * from './lighting'

// 材质编辑
export * from './materials'

// 地形编辑
export * from './terrain'

// 物理系统
export * from './physics'

// 动画系统
export * from './animation'

// 类型定义
export * from './types'

// 工具函数
export * from './utils'

// 常量
export * from './constants'

// 钩子函数
export * from './hooks'

// 上下文
export * from './contexts'

// 状态管理
export { SceneState, SceneActions } from './state/SceneState'

// 服务
export { default as SceneService } from './services/SceneService'
export { default as ObjectService } from './services/ObjectService'
export { default as LightingService } from './services/LightingService'

// 主要组件
export { default as ObjectHierarchy } from './components/ObjectHierarchy'
export { default as ObjectInspector } from './components/ObjectInspector'
export { default as LightingPanel } from './components/LightingPanel'
export { default as MaterialEditor } from './components/MaterialEditor'
export { default as TerrainEditor } from './components/TerrainEditor'
export { default as PhysicsPanel } from './components/PhysicsPanel'
export { default as AnimationTimeline } from './components/AnimationTimeline'
