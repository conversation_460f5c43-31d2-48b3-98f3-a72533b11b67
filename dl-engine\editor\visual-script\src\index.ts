/**
 * Digital Learning Engine - 可视化脚本编辑器
 * 
 * 专为教育场景设计的可视化编程环境
 * 支持节点式编程、教育专用节点库、中文编程等功能
 */

// 核心编辑器
export { default as VisualScriptEditor } from './components/VisualScriptEditor'
export type { VisualScriptEditorProps } from './components/VisualScriptEditor'

// 节点系统
export * from './nodes'

// 连接系统
export * from './connections'

// 图形系统
export * from './graph'

// 执行引擎
export * from './execution'

// 教育节点库
export * from './education'

// 工具函数
export * from './utils'

// 类型定义
export * from './types'

// 常量
export * from './constants'

// 钩子函数
export * from './hooks'

// 上下文
export * from './contexts'

// 主要组件
export { default as NodeEditor } from './components/NodeEditor'
export { default as NodePalette } from './components/NodePalette'
export { default as NodeInspector } from './components/NodeInspector'
export { default as ScriptRunner } from './components/ScriptRunner'
export { default as DebugPanel } from './components/DebugPanel'

// 教育组件
export { default as TutorialOverlay } from './education/components/TutorialOverlay'
export { default as ProgressTracker } from './education/components/ProgressTracker'
export { default as HintSystem } from './education/components/HintSystem'

// 节点库
export { default as BasicNodes } from './nodes/basic'
export { default as MathNodes } from './nodes/math'
export { default as LogicNodes } from './nodes/logic'
export { default as EventNodes } from './nodes/events'
export { default as EducationNodes } from './education/nodes'

// 执行器
export { default as ScriptExecutor } from './execution/ScriptExecutor'
export { default as DebugExecutor } from './execution/DebugExecutor'
