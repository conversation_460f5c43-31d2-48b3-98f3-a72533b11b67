/**
 * DL-Engine 知识点组件
 * 管理学习内容的知识点结构和关联关系
 */

import { defineComponent } from '../ComponentFunctions'

/**
 * 知识点类型
 */
export enum KnowledgePointType {
  CONCEPT = 'concept',           // 概念
  PRINCIPLE = 'principle',       // 原理
  PROCEDURE = 'procedure',       // 程序
  FACT = 'fact',                // 事实
  SKILL = 'skill',              // 技能
  ATTITUDE = 'attitude'         // 态度
}

/**
 * 知识点难度级别
 */
export enum DifficultyLevel {
  BEGINNER = 1,     // 初级
  ELEMENTARY = 2,   // 基础
  INTERMEDIATE = 3, // 中级
  ADVANCED = 4,     // 高级
  EXPERT = 5        // 专家
}

/**
 * 学习状态
 */
export enum KnowledgeStatus {
  NOT_LEARNED = 'not_learned',     // 未学习
  LEARNING = 'learning',           // 学习中
  UNDERSTOOD = 'understood',       // 已理解
  MASTERED = 'mastered',          // 已掌握
  NEEDS_REVIEW = 'needs_review'   // 需要复习
}

/**
 * 知识点关联关系
 */
export interface KnowledgeRelation {
  /** 关联的知识点ID */
  targetKnowledgeId: string
  
  /** 关联类型 */
  relationType: 'prerequisite' | 'successor' | 'related' | 'example' | 'application'
  
  /** 关联强度 (0-1) */
  strength: number
  
  /** 关联描述 */
  description?: string
}

/**
 * 学习资源
 */
export interface LearningResource {
  /** 资源ID */
  id: string
  
  /** 资源类型 */
  type: 'text' | 'video' | 'audio' | 'image' | 'animation' | 'simulation' | 'quiz' | 'game'
  
  /** 资源标题 */
  title: string
  
  /** 资源URL或路径 */
  url: string
  
  /** 资源描述 */
  description?: string
  
  /** 资源时长（秒） */
  duration?: number
  
  /** 资源大小（字节） */
  size?: number
  
  /** 是否必需 */
  required: boolean
  
  /** 推荐学习顺序 */
  order: number
}

/**
 * 学习目标
 */
export interface LearningObjective {
  /** 目标ID */
  id: string
  
  /** 目标描述 */
  description: string
  
  /** 目标类型 */
  type: 'knowledge' | 'comprehension' | 'application' | 'analysis' | 'synthesis' | 'evaluation'
  
  /** 是否可测量 */
  measurable: boolean
  
  /** 评估标准 */
  assessmentCriteria: string[]
}

/**
 * 知识点数据
 */
export interface KnowledgePointData {
  /** 知识点ID */
  id: string
  
  /** 知识点标题 */
  title: string
  
  /** 知识点描述 */
  description: string
  
  /** 知识点类型 */
  type: KnowledgePointType
  
  /** 难度级别 */
  difficulty: DifficultyLevel
  
  /** 学科领域 */
  subject: string
  
  /** 章节ID */
  chapterId: string
  
  /** 课程ID */
  courseId: string
  
  /** 学习状态 */
  status: KnowledgeStatus
  
  /** 掌握程度 (0-100) */
  masteryLevel: number
  
  /** 学习时间（分钟） */
  studyTime: number
  
  /** 复习次数 */
  reviewCount: number
  
  /** 最后学习时间 */
  lastStudyTime?: Date
  
  /** 下次复习时间 */
  nextReviewTime?: Date
  
  /** 知识点关联 */
  relations: KnowledgeRelation[]
  
  /** 学习资源 */
  resources: LearningResource[]
  
  /** 学习目标 */
  objectives: LearningObjective[]
  
  /** 关键词标签 */
  keywords: string[]
  
  /** 学习笔记 */
  notes: string[]
  
  /** 常见错误 */
  commonMistakes: string[]
  
  /** 学习提示 */
  hints: string[]
  
  /** 是否启用 */
  enabled: boolean
  
  /** 创建时间 */
  createdAt: Date
  
  /** 更新时间 */
  updatedAt: Date
}

/**
 * 知识点组件
 */
export const KnowledgePointComponent = defineComponent({
  name: 'KnowledgePoint',
  schema: {
    id: '',
    title: '',
    description: '',
    type: KnowledgePointType.CONCEPT,
    difficulty: DifficultyLevel.BEGINNER,
    subject: '',
    chapterId: '',
    courseId: '',
    status: KnowledgeStatus.NOT_LEARNED,
    masteryLevel: 0,
    studyTime: 0,
    reviewCount: 0,
    lastStudyTime: undefined as Date | undefined,
    nextReviewTime: undefined as Date | undefined,
    relations: [] as KnowledgeRelation[],
    resources: [] as LearningResource[],
    objectives: [] as LearningObjective[],
    keywords: [] as string[],
    notes: [] as string[],
    commonMistakes: [] as string[],
    hints: [] as string[],
    enabled: true,
    createdAt: new Date(),
    updatedAt: new Date()
  } as KnowledgePointData,
  
  onAdd: (entity, component) => {
    console.log(`Knowledge point added: ${component.title}`)
    component.createdAt = new Date()
    component.updatedAt = new Date()
  },
  
  onRemove: (entity, component) => {
    console.log(`Knowledge point removed: ${component.title}`)
  },
  
  onSet: (entity, component) => {
    component.updatedAt = new Date()
    
    // 根据掌握程度更新学习状态
    if (component.masteryLevel >= 90) {
      component.status = KnowledgeStatus.MASTERED
    } else if (component.masteryLevel >= 70) {
      component.status = KnowledgeStatus.UNDERSTOOD
    } else if (component.masteryLevel > 0) {
      component.status = KnowledgeStatus.LEARNING
    }
  }
})

/**
 * 知识点工具函数
 */
export const KnowledgePointUtils = {
  /**
   * 更新掌握程度
   */
  updateMasteryLevel: (knowledge: KnowledgePointData, level: number): void => {
    knowledge.masteryLevel = Math.max(0, Math.min(100, level))
    knowledge.updatedAt = new Date()
    
    // 更新学习状态
    if (knowledge.masteryLevel >= 90) {
      knowledge.status = KnowledgeStatus.MASTERED
    } else if (knowledge.masteryLevel >= 70) {
      knowledge.status = KnowledgeStatus.UNDERSTOOD
    } else if (knowledge.masteryLevel > 0) {
      knowledge.status = KnowledgeStatus.LEARNING
    }
  },
  
  /**
   * 添加学习时间
   */
  addStudyTime: (knowledge: KnowledgePointData, minutes: number): void => {
    knowledge.studyTime += minutes
    knowledge.lastStudyTime = new Date()
    knowledge.updatedAt = new Date()
  },
  
  /**
   * 添加关联关系
   */
  addRelation: (knowledge: KnowledgePointData, relation: KnowledgeRelation): void => {
    const existing = knowledge.relations.find(r => 
      r.targetKnowledgeId === relation.targetKnowledgeId && 
      r.relationType === relation.relationType
    )
    
    if (!existing) {
      knowledge.relations.push(relation)
      knowledge.updatedAt = new Date()
    }
  },
  
  /**
   * 添加学习资源
   */
  addResource: (knowledge: KnowledgePointData, resource: LearningResource): void => {
    const existing = knowledge.resources.find(r => r.id === resource.id)
    if (!existing) {
      knowledge.resources.push(resource)
      knowledge.resources.sort((a, b) => a.order - b.order)
      knowledge.updatedAt = new Date()
    }
  },
  
  /**
   * 计算复习间隔（基于遗忘曲线）
   */
  calculateReviewInterval: (knowledge: KnowledgePointData): number => {
    const baseInterval = 1 // 基础间隔（天）
    const factor = Math.pow(2, knowledge.reviewCount) // 指数增长
    const difficultyMultiplier = knowledge.difficulty / 3 // 难度调整
    const masteryBonus = knowledge.masteryLevel / 100 // 掌握程度奖励
    
    return Math.round(baseInterval * factor * difficultyMultiplier * (1 + masteryBonus))
  },
  
  /**
   * 安排下次复习
   */
  scheduleNextReview: (knowledge: KnowledgePointData): void => {
    const intervalDays = KnowledgePointUtils.calculateReviewInterval(knowledge)
    const nextReview = new Date()
    nextReview.setDate(nextReview.getDate() + intervalDays)
    
    knowledge.nextReviewTime = nextReview
    knowledge.reviewCount++
    knowledge.updatedAt = new Date()
  },
  
  /**
   * 检查是否需要复习
   */
  needsReview: (knowledge: KnowledgePointData): boolean => {
    if (!knowledge.nextReviewTime) return false
    return new Date() >= knowledge.nextReviewTime
  },
  
  /**
   * 获取前置知识点
   */
  getPrerequisites: (knowledge: KnowledgePointData): string[] => {
    return knowledge.relations
      .filter(r => r.relationType === 'prerequisite')
      .map(r => r.targetKnowledgeId)
  },
  
  /**
   * 获取后续知识点
   */
  getSuccessors: (knowledge: KnowledgePointData): string[] => {
    return knowledge.relations
      .filter(r => r.relationType === 'successor')
      .map(r => r.targetKnowledgeId)
  },
  
  /**
   * 获取相关知识点
   */
  getRelatedKnowledge: (knowledge: KnowledgePointData): string[] => {
    return knowledge.relations
      .filter(r => r.relationType === 'related')
      .map(r => r.targetKnowledgeId)
  }
}
