{"name": "@dl-engine/editor-assets", "version": "1.0.0", "description": "Digital Learning Engine - 资产管理系统", "main": "dist/index.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "scripts": {"build": "vite build", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "dev": "vite --mode development", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@dl-engine/engine-core": "workspace:*", "@dl-engine/shared-common": "workspace:*", "@dl-engine/editor-ui": "workspace:*", "react": "18.2.0", "react-dom": "18.2.0", "lodash": "4.17.21", "uuid": "9.0.0", "mime-types": "^2.1.35", "file-saver": "^2.0.5", "jszip": "^3.10.1", "fuse.js": "^7.0.0", "@hookstate/core": "^4.0.1", "react-i18next": "11.16.6", "react-dropzone": "^14.2.3", "react-virtualized": "^9.22.5"}, "devDependencies": {"@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@types/lodash": "^4.17.0", "@types/uuid": "^9.0.0", "@types/mime-types": "^2.1.4", "@types/file-saver": "^2.0.7", "@types/react-virtualized": "^9.21.29", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@vitest/coverage-v8": "^2.0.0", "eslint": "^9.0.0", "rimraf": "^6.0.0", "typescript": "5.6.3", "vite": "5.4.8", "vite-plugin-dts": "^4.0.0", "vitest": "^2.0.0"}, "peerDependencies": {"react": "18.2.0", "react-dom": "18.2.0"}, "engines": {"node": ">=22.0.0"}}