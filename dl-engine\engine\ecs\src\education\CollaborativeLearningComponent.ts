/**
 * DL-Engine 协作学习组件
 * 支持多人协作学习、小组讨论、同伴评议等功能
 */

import { defineComponent } from '../ComponentFunctions'

/**
 * 协作类型
 */
export enum CollaborationType {
  PAIR_LEARNING = 'pair_learning',         // 结对学习
  GROUP_PROJECT = 'group_project',         // 小组项目
  PEER_REVIEW = 'peer_review',            // 同伴评议
  DISCUSSION = 'discussion',               // 讨论
  BRAINSTORMING = 'brainstorming',        // 头脑风暴
  PROBLEM_SOLVING = 'problem_solving',     // 协作解题
  KNOWLEDGE_SHARING = 'knowledge_sharing', // 知识分享
  MENTORING = 'mentoring'                 // 导师指导
}

/**
 * 协作角色
 */
export enum CollaborationRole {
  LEADER = 'leader',           // 组长
  MEMBER = 'member',           // 成员
  FACILITATOR = 'facilitator', // 协调者
  OBSERVER = 'observer',       // 观察者
  MENTOR = 'mentor',           // 导师
  MENTEE = 'mentee'           // 学员
}

/**
 * 协作状态
 */
export enum CollaborationStatus {
  PENDING = 'pending',         // 待开始
  ACTIVE = 'active',           // 进行中
  PAUSED = 'paused',          // 暂停
  COMPLETED = 'completed',     // 已完成
  CANCELLED = 'cancelled'      // 已取消
}

/**
 * 协作参与者
 */
export interface CollaborationParticipant {
  /** 参与者ID */
  userId: string
  
  /** 参与者姓名 */
  name: string
  
  /** 参与者角色 */
  role: CollaborationRole
  
  /** 加入时间 */
  joinedAt: Date
  
  /** 最后活跃时间 */
  lastActiveAt: Date
  
  /** 贡献度评分 (0-100) */
  contributionScore: number
  
  /** 参与度评分 (0-100) */
  participationScore: number
  
  /** 是否在线 */
  isOnline: boolean
  
  /** 权限列表 */
  permissions: string[]
  
  /** 个人目标 */
  personalGoals: string[]
  
  /** 技能标签 */
  skills: string[]
}

/**
 * 协作活动
 */
export interface CollaborationActivity {
  /** 活动ID */
  id: string
  
  /** 活动类型 */
  type: 'message' | 'file_share' | 'edit' | 'comment' | 'vote' | 'task_assign' | 'milestone'
  
  /** 活动发起者ID */
  initiatorId: string
  
  /** 活动时间 */
  timestamp: Date
  
  /** 活动内容 */
  content: string
  
  /** 相关文件或资源 */
  attachments?: Array<{
    id: string
    name: string
    type: string
    url: string
    size: number
  }>
  
  /** 活动目标用户 */
  targetUsers?: string[]
  
  /** 活动元数据 */
  metadata: Record<string, any>
}

/**
 * 协作任务
 */
export interface CollaborationTask {
  /** 任务ID */
  id: string
  
  /** 任务标题 */
  title: string
  
  /** 任务描述 */
  description: string
  
  /** 任务负责人ID */
  assigneeId: string
  
  /** 任务状态 */
  status: 'todo' | 'in_progress' | 'review' | 'completed'
  
  /** 任务优先级 */
  priority: 'low' | 'medium' | 'high' | 'urgent'
  
  /** 预计工作量（小时） */
  estimatedHours: number
  
  /** 实际工作量（小时） */
  actualHours: number
  
  /** 截止时间 */
  dueDate: Date
  
  /** 创建时间 */
  createdAt: Date
  
  /** 完成时间 */
  completedAt?: Date
  
  /** 任务依赖 */
  dependencies: string[]
  
  /** 任务标签 */
  tags: string[]
}

/**
 * 协作评估
 */
export interface CollaborationAssessment {
  /** 评估ID */
  id: string
  
  /** 评估者ID */
  assessorId: string
  
  /** 被评估者ID */
  assesseeId: string
  
  /** 评估类型 */
  type: 'peer_review' | 'self_assessment' | 'group_evaluation'
  
  /** 评估维度 */
  dimensions: Array<{
    name: string
    score: number
    maxScore: number
    comment?: string
  }>
  
  /** 总体评分 */
  overallScore: number
  
  /** 评估反馈 */
  feedback: string
  
  /** 改进建议 */
  suggestions: string[]
  
  /** 评估时间 */
  assessedAt: Date
}

/**
 * 协作学习数据
 */
export interface CollaborativeLearningData {
  /** 协作会话ID */
  sessionId: string
  
  /** 协作名称 */
  name: string
  
  /** 协作描述 */
  description: string
  
  /** 协作类型 */
  type: CollaborationType
  
  /** 协作状态 */
  status: CollaborationStatus
  
  /** 课程ID */
  courseId: string
  
  /** 相关知识点ID */
  knowledgePointIds: string[]
  
  /** 参与者列表 */
  participants: CollaborationParticipant[]
  
  /** 协作活动记录 */
  activities: CollaborationActivity[]
  
  /** 协作任务 */
  tasks: CollaborationTask[]
  
  /** 协作评估 */
  assessments: CollaborationAssessment[]
  
  /** 协作目标 */
  objectives: string[]
  
  /** 协作规则 */
  rules: string[]
  
  /** 协作工具配置 */
  tools: {
    chatEnabled: boolean
    fileShareEnabled: boolean
    whiteboardEnabled: boolean
    videoCallEnabled: boolean
    screenShareEnabled: boolean
  }
  
  /** 协作设置 */
  settings: {
    maxParticipants: number
    allowJoinAnytime: boolean
    requireApproval: boolean
    autoAssignRoles: boolean
    enableNotifications: boolean
  }
  
  /** 协作统计 */
  statistics: {
    totalMessages: number
    totalFiles: number
    totalEdits: number
    averageParticipation: number
    collaborationEfficiency: number
    goalAchievementRate: number
  }
  
  /** 开始时间 */
  startTime: Date
  
  /** 结束时间 */
  endTime?: Date
  
  /** 创建时间 */
  createdAt: Date
  
  /** 更新时间 */
  updatedAt: Date
}

/**
 * 协作学习组件
 */
export const CollaborativeLearningComponent = defineComponent({
  name: 'CollaborativeLearning',
  schema: {
    sessionId: '',
    name: '',
    description: '',
    type: CollaborationType.GROUP_PROJECT,
    status: CollaborationStatus.PENDING,
    courseId: '',
    knowledgePointIds: [] as string[],
    participants: [] as CollaborationParticipant[],
    activities: [] as CollaborationActivity[],
    tasks: [] as CollaborationTask[],
    assessments: [] as CollaborationAssessment[],
    objectives: [] as string[],
    rules: [] as string[],
    tools: {
      chatEnabled: true,
      fileShareEnabled: true,
      whiteboardEnabled: true,
      videoCallEnabled: false,
      screenShareEnabled: false
    },
    settings: {
      maxParticipants: 6,
      allowJoinAnytime: true,
      requireApproval: false,
      autoAssignRoles: true,
      enableNotifications: true
    },
    statistics: {
      totalMessages: 0,
      totalFiles: 0,
      totalEdits: 0,
      averageParticipation: 0,
      collaborationEfficiency: 0,
      goalAchievementRate: 0
    },
    startTime: new Date(),
    endTime: undefined as Date | undefined,
    createdAt: new Date(),
    updatedAt: new Date()
  } as CollaborativeLearningData,
  
  onAdd: (entity, component) => {
    console.log(`Collaborative learning session created: ${component.name}`)
    component.createdAt = new Date()
    component.updatedAt = new Date()
  },
  
  onRemove: (entity, component) => {
    console.log(`Collaborative learning session removed: ${component.name}`)
  },
  
  onSet: (entity, component) => {
    component.updatedAt = new Date()
    
    // 更新统计信息
    CollaborativeLearningUtils.updateStatistics(component)
  }
})

/**
 * 协作学习工具函数
 */
export const CollaborativeLearningUtils = {
  /**
   * 开始协作会话
   */
  startSession: (collaboration: CollaborativeLearningData): void => {
    collaboration.status = CollaborationStatus.ACTIVE
    collaboration.startTime = new Date()
    collaboration.updatedAt = new Date()
    
    console.log(`Collaborative learning session started: ${collaboration.name}`)
  },
  
  /**
   * 添加参与者
   */
  addParticipant: (collaboration: CollaborativeLearningData, participant: CollaborationParticipant): boolean => {
    if (collaboration.participants.length >= collaboration.settings.maxParticipants) {
      console.warn('Maximum participants reached')
      return false
    }
    
    const existing = collaboration.participants.find(p => p.userId === participant.userId)
    if (existing) {
      console.warn('Participant already exists')
      return false
    }
    
    collaboration.participants.push(participant)
    
    // 记录活动
    CollaborativeLearningUtils.addActivity(collaboration, {
      id: `activity_${Date.now()}`,
      type: 'milestone',
      initiatorId: participant.userId,
      timestamp: new Date(),
      content: `${participant.name} joined the collaboration`,
      metadata: { action: 'join' }
    })
    
    collaboration.updatedAt = new Date()
    return true
  },
  
  /**
   * 移除参与者
   */
  removeParticipant: (collaboration: CollaborativeLearningData, userId: string): boolean => {
    const index = collaboration.participants.findIndex(p => p.userId === userId)
    if (index === -1) return false
    
    const participant = collaboration.participants[index]
    collaboration.participants.splice(index, 1)
    
    // 记录活动
    CollaborativeLearningUtils.addActivity(collaboration, {
      id: `activity_${Date.now()}`,
      type: 'milestone',
      initiatorId: userId,
      timestamp: new Date(),
      content: `${participant.name} left the collaboration`,
      metadata: { action: 'leave' }
    })
    
    collaboration.updatedAt = new Date()
    return true
  },
  
  /**
   * 添加活动记录
   */
  addActivity: (collaboration: CollaborativeLearningData, activity: CollaborationActivity): void => {
    collaboration.activities.push(activity)
    
    // 限制活动记录数量
    if (collaboration.activities.length > 1000) {
      collaboration.activities = collaboration.activities.slice(-1000)
    }
    
    collaboration.updatedAt = new Date()
  },
  
  /**
   * 分配任务
   */
  assignTask: (collaboration: CollaborativeLearningData, task: CollaborationTask): void => {
    collaboration.tasks.push(task)
    
    // 记录活动
    CollaborativeLearningUtils.addActivity(collaboration, {
      id: `activity_${Date.now()}`,
      type: 'task_assign',
      initiatorId: task.assigneeId,
      timestamp: new Date(),
      content: `Task assigned: ${task.title}`,
      targetUsers: [task.assigneeId],
      metadata: { taskId: task.id }
    })
    
    collaboration.updatedAt = new Date()
  },
  
  /**
   * 完成任务
   */
  completeTask: (collaboration: CollaborativeLearningData, taskId: string): boolean => {
    const task = collaboration.tasks.find(t => t.id === taskId)
    if (!task) return false
    
    task.status = 'completed'
    task.completedAt = new Date()
    
    // 记录活动
    CollaborativeLearningUtils.addActivity(collaboration, {
      id: `activity_${Date.now()}`,
      type: 'milestone',
      initiatorId: task.assigneeId,
      timestamp: new Date(),
      content: `Task completed: ${task.title}`,
      metadata: { taskId: task.id }
    })
    
    collaboration.updatedAt = new Date()
    return true
  },
  
  /**
   * 添加评估
   */
  addAssessment: (collaboration: CollaborativeLearningData, assessment: CollaborationAssessment): void => {
    collaboration.assessments.push(assessment)
    
    // 记录活动
    CollaborativeLearningUtils.addActivity(collaboration, {
      id: `activity_${Date.now()}`,
      type: 'milestone',
      initiatorId: assessment.assessorId,
      timestamp: new Date(),
      content: `Assessment submitted for ${assessment.type}`,
      targetUsers: [assessment.assesseeId],
      metadata: { assessmentId: assessment.id }
    })
    
    collaboration.updatedAt = new Date()
  },
  
  /**
   * 更新统计信息
   */
  updateStatistics: (collaboration: CollaborativeLearningData): void => {
    const stats = collaboration.statistics
    
    // 统计消息数量
    stats.totalMessages = collaboration.activities.filter(a => a.type === 'message').length
    
    // 统计文件数量
    stats.totalFiles = collaboration.activities.filter(a => a.type === 'file_share').length
    
    // 统计编辑次数
    stats.totalEdits = collaboration.activities.filter(a => a.type === 'edit').length
    
    // 计算平均参与度
    if (collaboration.participants.length > 0) {
      const totalParticipation = collaboration.participants.reduce(
        (sum, p) => sum + p.participationScore, 0
      )
      stats.averageParticipation = totalParticipation / collaboration.participants.length
    }
    
    // 计算协作效率
    const completedTasks = collaboration.tasks.filter(t => t.status === 'completed').length
    const totalTasks = collaboration.tasks.length
    stats.collaborationEfficiency = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0
    
    // 计算目标达成率
    // 这里简化为任务完成率，实际应该基于具体目标
    stats.goalAchievementRate = stats.collaborationEfficiency
  },
  
  /**
   * 计算参与者贡献度
   */
  calculateContribution: (collaboration: CollaborativeLearningData, userId: string): number => {
    const userActivities = collaboration.activities.filter(a => a.initiatorId === userId)
    const totalActivities = collaboration.activities.length
    
    if (totalActivities === 0) return 0
    
    // 基于活动数量和类型计算贡献度
    let contributionScore = 0
    const weights = {
      message: 1,
      file_share: 3,
      edit: 2,
      comment: 1,
      vote: 1,
      task_assign: 4,
      milestone: 5
    }
    
    userActivities.forEach(activity => {
      contributionScore += weights[activity.type as keyof typeof weights] || 1
    })
    
    // 归一化到0-100
    const maxPossibleScore = totalActivities * 5 // 假设所有活动都是最高权重
    return Math.min(100, (contributionScore / maxPossibleScore) * 100)
  },
  
  /**
   * 生成协作报告
   */
  generateReport: (collaboration: CollaborativeLearningData) => {
    return {
      sessionInfo: {
        name: collaboration.name,
        type: collaboration.type,
        status: collaboration.status,
        duration: collaboration.endTime 
          ? collaboration.endTime.getTime() - collaboration.startTime.getTime()
          : Date.now() - collaboration.startTime.getTime()
      },
      participants: collaboration.participants.map(p => ({
        name: p.name,
        role: p.role,
        contributionScore: p.contributionScore,
        participationScore: p.participationScore
      })),
      statistics: collaboration.statistics,
      achievements: {
        tasksCompleted: collaboration.tasks.filter(t => t.status === 'completed').length,
        totalTasks: collaboration.tasks.length,
        assessmentsCompleted: collaboration.assessments.length,
        objectivesAchieved: collaboration.objectives.length // 简化计算
      },
      recommendations: CollaborativeLearningUtils.generateRecommendations(collaboration)
    }
  },
  
  /**
   * 生成改进建议
   */
  generateRecommendations: (collaboration: CollaborativeLearningData): string[] => {
    const recommendations: string[] = []
    const stats = collaboration.statistics
    
    if (stats.averageParticipation < 50) {
      recommendations.push('建议增加互动活动以提高参与度')
    }
    
    if (stats.collaborationEfficiency < 70) {
      recommendations.push('建议优化任务分配和时间管理')
    }
    
    if (collaboration.activities.filter(a => a.type === 'message').length < 10) {
      recommendations.push('建议增加讨论和交流')
    }
    
    return recommendations
  }
}
