{"name": "@dl-engine/editor-core", "version": "1.0.0", "description": "Digital Learning Engine - 编辑器核心模块", "main": "dist/index.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "scripts": {"build": "vite build", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "dev": "vite --mode development", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@dl-engine/engine-core": "workspace:*", "@dl-engine/engine-ecs": "workspace:*", "@dl-engine/engine-state": "workspace:*", "@dl-engine/shared-common": "workspace:*", "react": "18.2.0", "react-dom": "18.2.0", "three": "0.176.0", "@types/three": "0.176.0", "rc-dock": "3.2.18", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-hotkeys-hook": "4.3.8", "react-i18next": "11.16.6", "i18next": "21.6.16", "uuid": "9.0.0", "lodash": "4.17.21"}, "devDependencies": {"@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@types/uuid": "^9.0.0", "@types/lodash": "^4.17.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@vitest/coverage-v8": "^2.0.0", "eslint": "^9.0.0", "rimraf": "^6.0.0", "typescript": "5.6.3", "vite": "5.4.8", "vite-plugin-dts": "^4.0.0", "vitest": "^2.0.0"}, "peerDependencies": {"react": "18.2.0", "react-dom": "18.2.0"}, "engines": {"node": ">=22.0.0"}}