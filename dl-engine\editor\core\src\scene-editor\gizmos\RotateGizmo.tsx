/**
 * 旋转操作工具组件
 */

import React from 'react'

export interface RotateGizmoProps {
  size?: number
}

const RotateGizmo: React.FC<RotateGizmoProps> = ({ size = 1 }) => {
  return (
    <group>
      {/* X轴旋转环 - 红色 */}
      <mesh rotation={[0, Math.PI / 2, 0]}>
        <torusGeometry args={[size, 0.02 * size, 8, 64]} />
        <meshBasicMaterial color="red" />
      </mesh>
      
      {/* Y轴旋转环 - 绿色 */}
      <mesh rotation={[Math.PI / 2, 0, 0]}>
        <torusGeometry args={[size, 0.02 * size, 8, 64]} />
        <meshBasicMaterial color="green" />
      </mesh>
      
      {/* Z轴旋转环 - 蓝色 */}
      <mesh>
        <torusGeometry args={[size, 0.02 * size, 8, 64]} />
        <meshBasicMaterial color="blue" />
      </mesh>
    </group>
  )
}

export default RotateGizmo
