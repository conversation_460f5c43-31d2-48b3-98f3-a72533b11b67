/**
 * 脚本图形工具函数
 */

import { v4 as uuidv4 } from 'uuid'
import { 
  ScriptGraph, 
  NodeInstance, 
  Connection, 
  NodeType, 
  DataType,
  Variable,
  Parameter 
} from '../types'

/**
 * 创建默认脚本图形
 */
export function createDefaultGraph(): ScriptGraph {
  const now = new Date()
  
  return {
    id: uuidv4(),
    name: '新脚本',
    description: '',
    nodes: [],
    connections: [],
    variables: [],
    parameters: [],
    metadata: {},
    createdAt: now,
    updatedAt: now
  }
}

/**
 * 创建新节点实例
 */
export function createNodeInstance(
  type: NodeType,
  position: { x: number; y: number },
  data: Record<string, any> = {}
): NodeInstance {
  return {
    id: uuidv4(),
    type,
    definition: getNodeDefinition(type), // 需要实现节点定义获取
    position,
    data,
    selected: false,
    disabled: false
  }
}

/**
 * 创建新连接
 */
export function createConnection(
  sourceNodeId: string,
  sourceSocketId: string,
  targetNodeId: string,
  targetSocketId: string,
  isExecution: boolean = false
): Connection {
  return {
    id: uuidv4(),
    sourceNodeId,
    sourceSocketId,
    targetNodeId,
    targetSocketId,
    isExecution
  }
}

/**
 * 创建新变量
 */
export function createVariable(
  name: string,
  type: DataType,
  defaultValue?: any,
  global: boolean = false
): Variable {
  return {
    id: uuidv4(),
    name,
    type,
    defaultValue,
    global
  }
}

/**
 * 创建新参数
 */
export function createParameter(
  name: string,
  type: DataType,
  defaultValue?: any,
  required: boolean = false
): Parameter {
  return {
    id: uuidv4(),
    name,
    type,
    defaultValue,
    required
  }
}

/**
 * 验证图形有效性
 */
export function validateGraph(graph: ScriptGraph): { valid: boolean; errors: string[] } {
  const errors: string[] = []
  
  // 检查节点
  for (const node of graph.nodes) {
    if (!node.id) {
      errors.push(`节点缺少ID: ${node.type}`)
    }
    
    if (!node.definition) {
      errors.push(`节点缺少定义: ${node.id}`)
    }
  }
  
  // 检查连接
  for (const connection of graph.connections) {
    const sourceNode = graph.nodes.find(n => n.id === connection.sourceNodeId)
    const targetNode = graph.nodes.find(n => n.id === connection.targetNodeId)
    
    if (!sourceNode) {
      errors.push(`连接的源节点不存在: ${connection.sourceNodeId}`)
    }
    
    if (!targetNode) {
      errors.push(`连接的目标节点不存在: ${connection.targetNodeId}`)
    }
    
    if (sourceNode && targetNode) {
      const sourceSocket = sourceNode.definition.outputs.find(s => s.id === connection.sourceSocketId)
      const targetSocket = targetNode.definition.inputs.find(s => s.id === connection.targetSocketId)
      
      if (!sourceSocket) {
        errors.push(`源连接点不存在: ${connection.sourceSocketId}`)
      }
      
      if (!targetSocket) {
        errors.push(`目标连接点不存在: ${connection.targetSocketId}`)
      }
      
      // 检查数据类型兼容性
      if (sourceSocket && targetSocket) {
        if (!isDataTypeCompatible(sourceSocket.dataType, targetSocket.dataType)) {
          errors.push(`数据类型不兼容: ${sourceSocket.dataType} -> ${targetSocket.dataType}`)
        }
      }
    }
  }
  
  // 检查循环依赖
  if (hasCircularDependency(graph)) {
    errors.push('图形中存在循环依赖')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 检查数据类型兼容性
 */
export function isDataTypeCompatible(sourceType: DataType, targetType: DataType): boolean {
  // ANY 类型兼容所有类型
  if (sourceType === DataType.ANY || targetType === DataType.ANY) {
    return true
  }
  
  // 相同类型兼容
  if (sourceType === targetType) {
    return true
  }
  
  // 数字可以转换为字符串
  if (sourceType === DataType.NUMBER && targetType === DataType.STRING) {
    return true
  }
  
  // 布尔值可以转换为字符串
  if (sourceType === DataType.BOOLEAN && targetType === DataType.STRING) {
    return true
  }
  
  return false
}

/**
 * 检查循环依赖
 */
export function hasCircularDependency(graph: ScriptGraph): boolean {
  const visited = new Set<string>()
  const recursionStack = new Set<string>()
  
  function dfs(nodeId: string): boolean {
    if (recursionStack.has(nodeId)) {
      return true // 发现循环
    }
    
    if (visited.has(nodeId)) {
      return false
    }
    
    visited.add(nodeId)
    recursionStack.add(nodeId)
    
    // 查找所有从当前节点出发的连接
    const outgoingConnections = graph.connections.filter(c => c.sourceNodeId === nodeId)
    
    for (const connection of outgoingConnections) {
      if (dfs(connection.targetNodeId)) {
        return true
      }
    }
    
    recursionStack.delete(nodeId)
    return false
  }
  
  // 检查所有节点
  for (const node of graph.nodes) {
    if (!visited.has(node.id)) {
      if (dfs(node.id)) {
        return true
      }
    }
  }
  
  return false
}

/**
 * 获取节点的输入连接
 */
export function getNodeInputConnections(graph: ScriptGraph, nodeId: string): Connection[] {
  return graph.connections.filter(c => c.targetNodeId === nodeId)
}

/**
 * 获取节点的输出连接
 */
export function getNodeOutputConnections(graph: ScriptGraph, nodeId: string): Connection[] {
  return graph.connections.filter(c => c.sourceNodeId === nodeId)
}

/**
 * 获取连接到指定连接点的连接
 */
export function getSocketConnection(
  graph: ScriptGraph, 
  nodeId: string, 
  socketId: string, 
  isInput: boolean
): Connection | undefined {
  if (isInput) {
    return graph.connections.find(c => c.targetNodeId === nodeId && c.targetSocketId === socketId)
  } else {
    return graph.connections.find(c => c.sourceNodeId === nodeId && c.sourceSocketId === socketId)
  }
}

/**
 * 删除节点及其相关连接
 */
export function removeNode(graph: ScriptGraph, nodeId: string): ScriptGraph {
  return {
    ...graph,
    nodes: graph.nodes.filter(n => n.id !== nodeId),
    connections: graph.connections.filter(c => 
      c.sourceNodeId !== nodeId && c.targetNodeId !== nodeId
    ),
    updatedAt: new Date()
  }
}

/**
 * 删除连接
 */
export function removeConnection(graph: ScriptGraph, connectionId: string): ScriptGraph {
  return {
    ...graph,
    connections: graph.connections.filter(c => c.id !== connectionId),
    updatedAt: new Date()
  }
}

/**
 * 添加节点
 */
export function addNode(graph: ScriptGraph, node: NodeInstance): ScriptGraph {
  return {
    ...graph,
    nodes: [...graph.nodes, node],
    updatedAt: new Date()
  }
}

/**
 * 添加连接
 */
export function addConnection(graph: ScriptGraph, connection: Connection): ScriptGraph {
  return {
    ...graph,
    connections: [...graph.connections, connection],
    updatedAt: new Date()
  }
}

/**
 * 更新节点
 */
export function updateNode(graph: ScriptGraph, nodeId: string, updates: Partial<NodeInstance>): ScriptGraph {
  return {
    ...graph,
    nodes: graph.nodes.map(n => 
      n.id === nodeId ? { ...n, ...updates } : n
    ),
    updatedAt: new Date()
  }
}

/**
 * 克隆图形
 */
export function cloneGraph(graph: ScriptGraph): ScriptGraph {
  return {
    ...graph,
    id: uuidv4(),
    name: `${graph.name} (副本)`,
    nodes: graph.nodes.map(node => ({ ...node, id: uuidv4() })),
    connections: graph.connections.map(conn => ({ ...conn, id: uuidv4() })),
    variables: graph.variables.map(variable => ({ ...variable, id: uuidv4() })),
    parameters: graph.parameters.map(param => ({ ...param, id: uuidv4() })),
    createdAt: new Date(),
    updatedAt: new Date()
  }
}

/**
 * 获取节点定义（临时实现）
 */
function getNodeDefinition(type: NodeType): any {
  // TODO: 实现从节点库获取定义的逻辑
  return {
    type,
    name: type,
    description: '',
    category: 'unknown',
    inputs: [],
    outputs: []
  }
}
