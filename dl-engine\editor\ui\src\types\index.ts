/**
 * DL-Engine 编辑器UI类型定义
 */

import { ReactNode } from 'react'

/**
 * 基础组件属性
 */
export interface BaseComponentProps {
  /** 自定义类名 */
  className?: string
  /** 自定义样式 */
  style?: React.CSSProperties
  /** 子组件 */
  children?: ReactNode
  /** 测试ID */
  'data-testid'?: string
}

/**
 * 尺寸类型
 */
export type Size = 'small' | 'medium' | 'large'

/**
 * 颜色变体类型
 */
export type ColorVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info'

/**
 * 位置类型
 */
export type Placement = 'top' | 'bottom' | 'left' | 'right' | 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight'

/**
 * 方向类型
 */
export type Direction = 'horizontal' | 'vertical'

/**
 * 对齐方式类型
 */
export type Alignment = 'start' | 'center' | 'end' | 'stretch'

/**
 * 响应式断点类型
 */
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl'

/**
 * 响应式配置类型
 */
export type ResponsiveConfig<T> = T | Partial<Record<Breakpoint, T>>

/**
 * 事件处理器类型
 */
export interface EventHandlers {
  onClick?: (event: React.MouseEvent) => void
  onDoubleClick?: (event: React.MouseEvent) => void
  onMouseEnter?: (event: React.MouseEvent) => void
  onMouseLeave?: (event: React.MouseEvent) => void
  onFocus?: (event: React.FocusEvent) => void
  onBlur?: (event: React.FocusEvent) => void
  onKeyDown?: (event: React.KeyboardEvent) => void
  onKeyUp?: (event: React.KeyboardEvent) => void
}

/**
 * 表单字段类型
 */
export interface FormField {
  /** 字段名称 */
  name: string
  /** 字段标签 */
  label: string
  /** 字段类型 */
  type: 'text' | 'number' | 'boolean' | 'select' | 'color' | 'file' | 'textarea'
  /** 默认值 */
  defaultValue?: any
  /** 是否必填 */
  required?: boolean
  /** 验证规则 */
  rules?: any[]
  /** 选项（用于 select 类型） */
  options?: Array<{ label: string; value: any }>
  /** 占位符 */
  placeholder?: string
  /** 帮助文本 */
  help?: string
  /** 是否禁用 */
  disabled?: boolean
}

/**
 * 菜单项类型
 */
export interface MenuItem {
  /** 菜单项键值 */
  key: string
  /** 菜单项标签 */
  label: string
  /** 菜单项图标 */
  icon?: string | ReactNode
  /** 子菜单 */
  children?: MenuItem[]
  /** 是否禁用 */
  disabled?: boolean
  /** 是否分割线 */
  divider?: boolean
  /** 点击事件 */
  onClick?: () => void
  /** 快捷键 */
  shortcut?: string
}

/**
 * 表格列类型
 */
export interface TableColumn<T = any> {
  /** 列键值 */
  key: string
  /** 列标题 */
  title: string
  /** 数据索引 */
  dataIndex?: string
  /** 列宽度 */
  width?: number | string
  /** 是否可排序 */
  sortable?: boolean
  /** 是否可筛选 */
  filterable?: boolean
  /** 渲染函数 */
  render?: (value: any, record: T, index: number) => ReactNode
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
  /** 是否固定 */
  fixed?: 'left' | 'right'
}

/**
 * 树节点类型
 */
export interface TreeNode {
  /** 节点键值 */
  key: string
  /** 节点标题 */
  title: string
  /** 节点图标 */
  icon?: string | ReactNode
  /** 子节点 */
  children?: TreeNode[]
  /** 是否展开 */
  expanded?: boolean
  /** 是否选中 */
  selected?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 是否可拖拽 */
  draggable?: boolean
  /** 节点数据 */
  data?: any
}

/**
 * 通知类型
 */
export interface NotificationConfig {
  /** 通知类型 */
  type: 'info' | 'success' | 'warning' | 'error'
  /** 通知标题 */
  title: string
  /** 通知内容 */
  message?: string
  /** 显示时长（毫秒） */
  duration?: number
  /** 是否显示关闭按钮 */
  closable?: boolean
  /** 关闭回调 */
  onClose?: () => void
}

/**
 * 模态框配置类型
 */
export interface ModalConfig {
  /** 模态框标题 */
  title: string
  /** 模态框内容 */
  content: ReactNode
  /** 模态框宽度 */
  width?: number | string
  /** 是否可拖拽 */
  draggable?: boolean
  /** 是否可调整大小 */
  resizable?: boolean
  /** 确认按钮文本 */
  okText?: string
  /** 取消按钮文本 */
  cancelText?: string
  /** 确认回调 */
  onOk?: () => void | Promise<void>
  /** 取消回调 */
  onCancel?: () => void
}

/**
 * 拖拽数据类型
 */
export interface DragData {
  /** 拖拽类型 */
  type: string
  /** 拖拽数据 */
  data: any
  /** 拖拽源 */
  source?: string
}

/**
 * 快捷键配置类型
 */
export interface ShortcutConfig {
  /** 快捷键组合 */
  key: string
  /** 快捷键描述 */
  description: string
  /** 快捷键处理函数 */
  handler: (event: KeyboardEvent) => void
  /** 是否全局快捷键 */
  global?: boolean
  /** 是否阻止默认行为 */
  preventDefault?: boolean
}

/**
 * 主题令牌类型
 */
export interface ThemeToken {
  /** 主色调 */
  colorPrimary: string
  /** 成功色 */
  colorSuccess: string
  /** 警告色 */
  colorWarning: string
  /** 错误色 */
  colorError: string
  /** 信息色 */
  colorInfo: string
  /** 背景色 */
  colorBgBase: string
  /** 容器背景色 */
  colorBgContainer: string
  /** 文字色 */
  colorText: string
  /** 次要文字色 */
  colorTextSecondary: string
  /** 边框色 */
  colorBorder: string
  /** 字体族 */
  fontFamily: string
  /** 字体大小 */
  fontSize: number
  /** 圆角大小 */
  borderRadius: number
}

/**
 * 动画配置类型
 */
export interface AnimationConfig {
  /** 动画名称 */
  name: string
  /** 动画持续时间 */
  duration: number
  /** 动画缓动函数 */
  easing: string
  /** 动画延迟 */
  delay?: number
  /** 动画迭代次数 */
  iterations?: number
  /** 动画方向 */
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse'
  /** 动画填充模式 */
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both'
}
