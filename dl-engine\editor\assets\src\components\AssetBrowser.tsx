/**
 * 资产浏览器组件
 * 
 * 提供完整的资产浏览和管理界面
 */

import React, { useEffect, useCallback } from 'react'
import { useHookstate } from '@hookstate/core'
import { useTranslation } from 'react-i18next'
import { Layout, Splitter } from '@dl-engine/editor-ui'
import { AssetState, AssetActions, ViewMode } from '../state/AssetState'
import { Asset, AssetType } from '../types'
import AssetTree from './AssetTree'
import AssetGrid from './AssetGrid'
import AssetList from './AssetList'
import AssetPreview from './AssetPreview'
import AssetProperties from './AssetProperties'
import AssetImporter from './AssetImporter'

/**
 * 资产浏览器属性
 */
export interface AssetBrowserProps {
  /** 是否只读模式 */
  readonly?: boolean
  /** 是否显示导入按钮 */
  showImporter?: boolean
  /** 是否显示预览面板 */
  showPreview?: boolean
  /** 是否显示属性面板 */
  showProperties?: boolean
  /** 资产选择回调 */
  onAssetSelect?: (assets: Asset[]) => void
  /** 资产双击回调 */
  onAssetDoubleClick?: (asset: Asset) => void
  /** 自定义类名 */
  className?: string
  /** 自定义样式 */
  style?: React.CSSProperties
}

/**
 * 资产浏览器组件
 */
const AssetBrowser: React.FC<AssetBrowserProps> = ({
  readonly = false,
  showImporter = true,
  showPreview = true,
  showProperties = true,
  onAssetSelect,
  onAssetDoubleClick,
  className = '',
  style = {}
}) => {
  const { t } = useTranslation()
  const assetState = useHookstate(AssetState)
  
  // 获取状态
  const assets = assetState.assets.get()
  const selectedAssets = assetState.selectedAssets.get()
  const currentFolderId = assetState.currentFolderId.get()
  const viewMode = assetState.viewMode.get()
  const filters = assetState.filters.get()
  const ui = assetState.ui.get()
  const loading = assetState.loading.get()
  const error = assetState.error.get()
  
  // 获取当前文件夹的资产
  const currentAssets = assets.filter(asset => {
    // 文件夹过滤
    if (asset.parentId !== currentFolderId) {
      return false
    }
    
    // 类型过滤
    if (filters.types.length > 0 && !filters.types.includes(asset.type)) {
      return false
    }
    
    // 标签过滤
    if (filters.tags.length > 0) {
      const hasMatchingTag = filters.tags.some(tag => 
        asset.metadata.tags.includes(tag)
      )
      if (!hasMatchingTag) {
        return false
      }
    }
    
    // 收藏过滤
    if (filters.favoritesOnly && !asset.isFavorite) {
      return false
    }
    
    // 隐藏文件过滤
    if (!filters.showHidden && asset.isHidden) {
      return false
    }
    
    return true
  })
  
  // 获取选中的资产对象
  const selectedAssetObjects = selectedAssets
    .map(id => assets.find(asset => asset.id === id))
    .filter(Boolean) as Asset[]
  
  /**
   * 处理资产选择
   */
  const handleAssetSelect = useCallback((assetIds: string[]) => {
    AssetActions.setSelectedAssets(assetIds)
    
    const selectedObjects = assetIds
      .map(id => assets.find(asset => asset.id === id))
      .filter(Boolean) as Asset[]
    
    onAssetSelect?.(selectedObjects)
  }, [assets, onAssetSelect])
  
  /**
   * 处理资产双击
   */
  const handleAssetDoubleClick = useCallback((asset: Asset) => {
    if (asset.type === AssetType.FOLDER) {
      // 进入文件夹
      AssetActions.setCurrentFolder(asset.id)
    } else {
      // 触发双击回调
      onAssetDoubleClick?.(asset)
    }
  }, [onAssetDoubleClick])
  
  /**
   * 处理文件夹导航
   */
  const handleFolderNavigate = useCallback((folderId: string | null) => {
    AssetActions.setCurrentFolder(folderId)
    AssetActions.clearSelectedAssets()
  }, [])
  
  /**
   * 处理视图模式切换
   */
  const handleViewModeChange = useCallback((mode: ViewMode) => {
    AssetActions.setViewMode(mode)
  }, [])
  
  /**
   * 处理搜索
   */
  const handleSearch = useCallback((query: string) => {
    AssetActions.setSearchCriteria({ query })
  }, [])
  
  /**
   * 处理过滤器变化
   */
  const handleFilterChange = useCallback((filterType: string, value: any) => {
    AssetActions.setFilters({ [filterType]: value })
  }, [])
  
  /**
   * 渲染工具栏
   */
  const renderToolbar = () => (
    <div className="asset-browser-toolbar border-b border-gray-300 p-2 flex items-center justify-between">
      {/* 左侧工具 */}
      <div className="flex items-center gap-2">
        {/* 导航按钮 */}
        <button
          className="px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded disabled:opacity-50"
          onClick={() => handleFolderNavigate(null)}
          disabled={!currentFolderId}
          title="返回根目录"
        >
          🏠
        </button>
        
        {/* 新建文件夹 */}
        {!readonly && (
          <button
            className="px-2 py-1 bg-blue-500 text-white hover:bg-blue-600 rounded"
            onClick={() => {
              // TODO: 实现新建文件夹逻辑
              console.log('新建文件夹')
            }}
            title="新建文件夹"
          >
            📁+
          </button>
        )}
        
        {/* 导入按钮 */}
        {!readonly && showImporter && (
          <AssetImporter
            targetFolderId={currentFolderId}
            onImportComplete={() => {
              // 刷新资产列表
              console.log('导入完成')
            }}
          />
        )}
      </div>
      
      {/* 中间搜索 */}
      <div className="flex-1 max-w-md mx-4">
        <input
          type="text"
          placeholder="搜索资产..."
          className="w-full px-3 py-1 border border-gray-300 rounded focus:outline-none focus:border-blue-500"
          onChange={(e) => handleSearch(e.target.value)}
        />
      </div>
      
      {/* 右侧视图控制 */}
      <div className="flex items-center gap-2">
        {/* 视图模式切换 */}
        <div className="flex border border-gray-300 rounded overflow-hidden">
          <button
            className={`px-2 py-1 ${viewMode === ViewMode.GRID ? 'bg-blue-500 text-white' : 'bg-white hover:bg-gray-100'}`}
            onClick={() => handleViewModeChange(ViewMode.GRID)}
            title="网格视图"
          >
            ⊞
          </button>
          <button
            className={`px-2 py-1 ${viewMode === ViewMode.LIST ? 'bg-blue-500 text-white' : 'bg-white hover:bg-gray-100'}`}
            onClick={() => handleViewModeChange(ViewMode.LIST)}
            title="列表视图"
          >
            ☰
          </button>
        </div>
        
        {/* 缩略图大小 */}
        <select
          value={ui.thumbnailSize}
          onChange={(e) => AssetActions.setThumbnailSize(e.target.value as any)}
          className="px-2 py-1 border border-gray-300 rounded"
        >
          <option value="small">小</option>
          <option value="medium">中</option>
          <option value="large">大</option>
        </select>
      </div>
    </div>
  )
  
  /**
   * 渲染主内容区域
   */
  const renderMainContent = () => {
    if (loading.isLoading) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="text-2xl mb-2">⏳</div>
            <div>{loading.message || '加载中...'}</div>
          </div>
        </div>
      )
    }
    
    if (error.hasError) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-red-500">
            <div className="text-2xl mb-2">❌</div>
            <div>{error.message}</div>
            <button
              className="mt-2 px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
              onClick={() => AssetActions.clearError()}
            >
              重试
            </button>
          </div>
        </div>
      )
    }
    
    if (currentAssets.length === 0) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-gray-500">
            <div className="text-4xl mb-2">📁</div>
            <div>此文件夹为空</div>
            {!readonly && (
              <div className="text-sm mt-2">
                拖拽文件到这里或点击导入按钮添加资产
              </div>
            )}
          </div>
        </div>
      )
    }
    
    // 根据视图模式渲染不同的组件
    switch (viewMode) {
      case ViewMode.GRID:
        return (
          <AssetGrid
            assets={currentAssets}
            selectedAssets={selectedAssets}
            thumbnailSize={ui.thumbnailSize}
            onAssetSelect={handleAssetSelect}
            onAssetDoubleClick={handleAssetDoubleClick}
            readonly={readonly}
          />
        )
      
      case ViewMode.LIST:
        return (
          <AssetList
            assets={currentAssets}
            selectedAssets={selectedAssets}
            onAssetSelect={handleAssetSelect}
            onAssetDoubleClick={handleAssetDoubleClick}
            readonly={readonly}
          />
        )
      
      default:
        return null
    }
  }
  
  return (
    <div className={`asset-browser ${className}`} style={style}>
      <Layout direction="horizontal" className="h-full">
        {/* 左侧文件夹树 */}
        <Layout.Sider width={ui.sidebarWidth} className="border-r border-gray-300">
          <AssetTree
            assets={assets.filter(asset => asset.type === AssetType.FOLDER)}
            currentFolderId={currentFolderId}
            onFolderSelect={handleFolderNavigate}
            readonly={readonly}
          />
        </Layout.Sider>
        
        {/* 中央内容区域 */}
        <Layout.Content className="flex-1">
          <Layout direction="vertical" className="h-full">
            {/* 工具栏 */}
            {renderToolbar()}
            
            {/* 主内容 */}
            <div className="flex-1 overflow-hidden">
              {renderMainContent()}
            </div>
          </Layout>
        </Layout.Content>
        
        {/* 右侧面板 */}
        {(showPreview || showProperties) && (
          <Layout.Sider width={ui.previewWidth} className="border-l border-gray-300">
            <Layout direction="vertical" className="h-full">
              {/* 预览面板 */}
              {showPreview && ui.showPreview && (
                <div className="flex-1 border-b border-gray-300">
                  <AssetPreview
                    asset={selectedAssetObjects[0]}
                    onClose={() => AssetActions.toggleUIPanel('showPreview')}
                  />
                </div>
              )}
              
              {/* 属性面板 */}
              {showProperties && ui.showProperties && (
                <div className="h-64">
                  <AssetProperties
                    assets={selectedAssetObjects}
                    readonly={readonly}
                    onClose={() => AssetActions.toggleUIPanel('showProperties')}
                  />
                </div>
              )}
            </Layout>
          </Layout.Sider>
        )}
      </Layout>
    </div>
  )
}

export default AssetBrowser
