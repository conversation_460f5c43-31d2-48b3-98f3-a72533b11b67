/**
 * 控制台面板组件
 * 
 * 显示系统日志、错误信息和调试输出
 */

import React, { useState, useEffect, useRef } from 'react'

/**
 * 日志级别
 */
export enum LogLevel {
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  DEBUG = 'debug'
}

/**
 * 日志消息
 */
interface LogMessage {
  id: string
  level: LogLevel
  message: string
  timestamp: Date
  source?: string
}

/**
 * 控制台面板属性
 */
export interface ConsolePanelProps {
  className?: string
  style?: React.CSSProperties
}

/**
 * 控制台面板组件
 */
const ConsolePanel: React.FC<ConsolePanelProps> = ({
  className = '',
  style = {}
}) => {
  const [messages, setMessages] = useState<LogMessage[]>([])
  const [filter, setFilter] = useState<LogLevel | 'all'>('all')
  const [autoScroll, setAutoScroll] = useState(true)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // 模拟日志消息
  useEffect(() => {
    const initialMessages: LogMessage[] = [
      {
        id: '1',
        level: LogLevel.INFO,
        message: 'DL-Engine 编辑器已启动',
        timestamp: new Date(),
        source: 'Editor'
      },
      {
        id: '2',
        level: LogLevel.INFO,
        message: '场景加载完成',
        timestamp: new Date(),
        source: 'Scene'
      },
      {
        id: '3',
        level: LogLevel.WARN,
        message: '纹理分辨率过大，建议优化',
        timestamp: new Date(),
        source: 'Renderer'
      }
    ]
    setMessages(initialMessages)
  }, [])

  // 自动滚动到底部
  useEffect(() => {
    if (autoScroll && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [messages, autoScroll])

  /**
   * 过滤消息
   */
  const filteredMessages = messages.filter(msg => 
    filter === 'all' || msg.level === filter
  )

  /**
   * 获取日志级别图标
   */
  const getLevelIcon = (level: LogLevel) => {
    switch (level) {
      case LogLevel.INFO: return 'ℹ️'
      case LogLevel.WARN: return '⚠️'
      case LogLevel.ERROR: return '❌'
      case LogLevel.DEBUG: return '🐛'
      default: return '📝'
    }
  }

  /**
   * 清空控制台
   */
  const clearConsole = () => {
    setMessages([])
  }

  /**
   * 添加新消息
   */
  const addMessage = (level: LogLevel, message: string, source?: string) => {
    const newMessage: LogMessage = {
      id: Date.now().toString(),
      level,
      message,
      timestamp: new Date(),
      source
    }
    setMessages(prev => [...prev, newMessage])
  }

  return (
    <div className={`console-panel ${className}`} style={style}>
      {/* 面板头部 */}
      <div className="panel-header">
        <span>控制台</span>
        <div className="flex items-center gap-2">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as LogLevel | 'all')}
            className="text-xs px-2 py-1 bg-gray-700 border border-gray-600 rounded"
          >
            <option value="all">全部</option>
            <option value={LogLevel.INFO}>信息</option>
            <option value={LogLevel.WARN}>警告</option>
            <option value={LogLevel.ERROR}>错误</option>
            <option value={LogLevel.DEBUG}>调试</option>
          </select>
          <button
            className={`text-xs px-2 py-1 rounded ${autoScroll ? 'bg-blue-600' : 'bg-gray-600 hover:bg-gray-500'}`}
            onClick={() => setAutoScroll(!autoScroll)}
            title="自动滚动"
          >
            📜
          </button>
          <button
            className="text-xs px-2 py-1 bg-red-600 hover:bg-red-500 rounded"
            onClick={clearConsole}
            title="清空控制台"
          >
            🗑️
          </button>
        </div>
      </div>

      {/* 控制台内容 */}
      <div className="console-content">
        {filteredMessages.length > 0 ? (
          <>
            {filteredMessages.map(msg => (
              <div key={msg.id} className={`console-message ${msg.level}`}>
                <span className="mr-2">{getLevelIcon(msg.level)}</span>
                <span className="text-gray-400 text-xs mr-2">
                  {msg.timestamp.toLocaleTimeString()}
                </span>
                {msg.source && (
                  <span className="text-blue-400 text-xs mr-2">[{msg.source}]</span>
                )}
                <span>{msg.message}</span>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </>
        ) : (
          <div className="text-center text-gray-400 py-8">
            <div className="text-4xl mb-2">📝</div>
            <div>控制台为空</div>
            <div className="text-sm">系统消息将在这里显示</div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ConsolePanel
