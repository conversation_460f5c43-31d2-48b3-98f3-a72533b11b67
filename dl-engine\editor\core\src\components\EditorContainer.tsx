/**
 * 编辑器容器组件
 * 
 * 这是DL-Engine编辑器的主容器，集成了：
 * - 停靠面板系统 (Docking System)
 * - 拖拽功能 (Drag & Drop)
 * - 工具栏 (Toolbar)
 * - 各种编辑器面板
 */

import React, { useRef, useEffect, useMemo } from 'react'
import { useHookstate } from '@hookstate/core'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { DockLayout, LayoutData, TabData } from 'rc-dock'
import { EditorState } from '../services/EditorState'
import { PanelType } from '../types'
import SceneEditor from '../scene-editor/components/SceneEditor'
import Toolbar from '../toolbar/components/Toolbar'
import HierarchyPanel from '../hierarchy/components/HierarchyPanel'
import PropertiesPanel from '../properties/components/PropertiesPanel'
import AssetsPanel from '../assets/components/AssetsPanel'
import MaterialsPanel from '../materials/components/MaterialsPanel'
import ConsolePanel from '../console/components/ConsolePanel'
import InspectorPanel from '../inspector/components/InspectorPanel'

// 引入样式
import 'rc-dock/dist/rc-dock.css'
import './EditorContainer.css'

/**
 * 编辑器容器属性
 */
export interface EditorContainerProps {
  /** 容器类名 */
  className?: string
  /** 容器样式 */
  style?: React.CSSProperties
  /** 是否启用拖拽 */
  enableDragDrop?: boolean
}

/**
 * 编辑器容器组件
 */
const EditorContainer: React.FC<EditorContainerProps> = ({
  className = '',
  style = {},
  enableDragDrop = true
}) => {
  const dockLayoutRef = useRef<DockLayout>(null)
  const editorState = useHookstate(EditorState)
  
  // 获取编辑器状态
  const uiEnabled = editorState.uiEnabled.get()
  const panels = editorState.panels.get()
  const scenePath = editorState.scenePath.get()
  const loading = editorState.loading.get()
  const error = editorState.error.get()
  const warning = editorState.warning.get()
  
  /**
   * 创建面板标签页数据
   */
  const createTabData = (
    id: string, 
    title: string, 
    content: React.ReactNode,
    closable: boolean = true
  ): TabData => ({
    id,
    title,
    content,
    closable
  })
  
  /**
   * 默认布局配置
   */
  const defaultLayout: LayoutData = useMemo(() => ({
    dockbox: {
      mode: 'horizontal',
      children: [
        // 左侧面板
        {
          mode: 'vertical',
          size: 300,
          children: [
            {
              tabs: [
                createTabData(
                  'hierarchy',
                  '层次结构',
                  <HierarchyPanel />,
                  false
                )
              ]
            },
            {
              tabs: [
                createTabData(
                  'assets',
                  '资产',
                  <AssetsPanel />
                )
              ]
            }
          ]
        },
        // 中央编辑区域
        {
          mode: 'vertical',
          children: [
            {
              tabs: [
                createTabData(
                  'scene',
                  '场景',
                  <SceneEditor />,
                  false
                )
              ]
            }
          ]
        },
        // 右侧面板
        {
          mode: 'vertical',
          size: 350,
          children: [
            {
              tabs: [
                createTabData(
                  'properties',
                  '属性',
                  <PropertiesPanel />,
                  false
                ),
                createTabData(
                  'inspector',
                  '检查器',
                  <InspectorPanel />
                )
              ]
            },
            {
              tabs: [
                createTabData(
                  'materials',
                  '材质',
                  <MaterialsPanel />
                ),
                createTabData(
                  'console',
                  '控制台',
                  <ConsolePanel />
                )
              ]
            }
          ]
        }
      ]
    }
  }), [])
  
  /**
   * 处理面板关闭
   */
  const handleTabClose = (tabId: string) => {
    const panelKey = tabId as keyof typeof panels
    if (panelKey in panels) {
      editorState.panels[panelKey].set(false)
    }
  }
  
  /**
   * 处理布局变化
   */
  const handleLayoutChange = (newLayout: LayoutData) => {
    // 保存布局到本地存储
    localStorage.setItem('dl-editor-layout', JSON.stringify(newLayout))
  }
  
  /**
   * 加载保存的布局
   */
  useEffect(() => {
    const savedLayout = localStorage.getItem('dl-editor-layout')
    if (savedLayout && dockLayoutRef.current) {
      try {
        const layout = JSON.parse(savedLayout)
        dockLayoutRef.current.loadLayout(layout)
      } catch (error) {
        console.warn('加载保存的布局失败:', error)
      }
    }
  }, [])
  
  /**
   * 渲染加载状态
   */
  if (loading) {
    return (
      <div className="flex items-center justify-center w-full h-full bg-gray-900 text-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <div>正在加载编辑器...</div>
        </div>
      </div>
    )
  }
  
  /**
   * 渲染错误状态
   */
  if (error.hasError) {
    return (
      <div className="flex items-center justify-center w-full h-full bg-red-900 text-white">
        <div className="text-center max-w-md">
          <div className="text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-bold mb-2">编辑器错误</h2>
          <p className="mb-4">{error.message}</p>
          <button 
            className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded"
            onClick={() => editorState.error.set({ hasError: false, message: null, details: null })}
          >
            重试
          </button>
        </div>
      </div>
    )
  }
  
  /**
   * 编辑器内容
   */
  const editorContent = (
    <main className={`editor-container flex flex-col w-full h-full ${className}`} style={style}>
      {/* 工具栏 */}
      {uiEnabled && <Toolbar />}
      
      {/* 警告提示 */}
      {warning.hasWarning && (
        <div className="bg-yellow-600 text-white px-4 py-2 text-sm flex items-center justify-between">
          <span>{warning.message}</span>
          <button 
            className="ml-4 text-yellow-200 hover:text-white"
            onClick={() => editorState.warning.set({ hasWarning: false, message: null })}
          >
            ✕
          </button>
        </div>
      )}
      
      {/* 主编辑区域 */}
      <div className="flex-1 overflow-hidden">
        {uiEnabled ? (
          <DockLayout
            ref={dockLayoutRef}
            defaultLayout={defaultLayout}
            style={{ 
              position: 'absolute', 
              left: 0, 
              top: 0, 
              right: 0, 
              bottom: 0 
            }}
            onLayoutChange={handleLayoutChange}
            groups={{
              // 定义面板组
              'editor-panels': {
                floatable: true,
                maximizable: true
              }
            }}
          />
        ) : (
          // 无UI模式，只显示场景编辑器
          <SceneEditor />
        )}
      </div>
      
      {/* 状态栏 */}
      {uiEnabled && (
        <div className="bg-gray-800 text-white px-4 py-1 text-xs flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span>场景: {scenePath || '未加载'}</span>
            <span>模式: {editorState.mode.get()}</span>
            <span>工具: {editorState.tool.get()}</span>
          </div>
          <div className="flex items-center space-x-4">
            <span>选中: {editorState.selection.selectedEntities.get().length} 个对象</span>
            <span>FPS: --</span>
          </div>
        </div>
      )}
    </main>
  )
  
  /**
   * 根据是否启用拖拽返回相应的组件
   */
  return enableDragDrop ? (
    <DndProvider backend={HTML5Backend}>
      {editorContent}
    </DndProvider>
  ) : (
    editorContent
  )
}

export default EditorContainer
