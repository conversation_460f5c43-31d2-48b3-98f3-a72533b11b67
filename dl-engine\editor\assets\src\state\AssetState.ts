/**
 * 资产管理状态
 * 
 * 使用 Hookstate 管理资产的全局状态
 */

import { hookstate, State } from '@hookstate/core'
import { 
  Asset, 
  AssetFolder, 
  AssetSearchCriteria, 
  AssetSearchResult,
  AssetProcessingTask,
  AssetStatistics,
  AssetType
} from '../types'

/**
 * 视图模式
 */
export enum ViewMode {
  GRID = 'grid',
  LIST = 'list',
  TREE = 'tree'
}

/**
 * 资产状态接口
 */
export interface AssetStateType {
  /** 所有资产 */
  assets: Asset[]
  
  /** 当前选中的资产ID列表 */
  selectedAssets: string[]
  
  /** 当前文件夹ID */
  currentFolderId: string | null
  
  /** 搜索条件 */
  searchCriteria: AssetSearchCriteria
  
  /** 搜索结果 */
  searchResult: AssetSearchResult | null
  
  /** 处理任务列表 */
  processingTasks: AssetProcessingTask[]
  
  /** 统计信息 */
  statistics: AssetStatistics | null
  
  /** 视图模式 */
  viewMode: ViewMode
  
  /** 排序方式 */
  sortBy: 'name' | 'type' | 'size' | 'date'
  
  /** 排序方向 */
  sortOrder: 'asc' | 'desc'
  
  /** 过滤器 */
  filters: {
    types: AssetType[]
    tags: string[]
    favoritesOnly: boolean
    showHidden: boolean
  }
  
  /** UI状态 */
  ui: {
    showPreview: boolean
    showProperties: boolean
    showThumbnails: boolean
    thumbnailSize: 'small' | 'medium' | 'large'
    sidebarWidth: number
    previewWidth: number
  }
  
  /** 加载状态 */
  loading: {
    isLoading: boolean
    message: string | null
  }
  
  /** 错误状态 */
  error: {
    hasError: boolean
    message: string | null
    details: any
  }
  
  /** 拖拽状态 */
  dragDrop: {
    isDragging: boolean
    draggedAssets: string[]
    dropTarget: string | null
  }
}

/**
 * 默认搜索条件
 */
const defaultSearchCriteria: AssetSearchCriteria = {
  query: '',
  types: [],
  tags: [],
  favoritesOnly: false,
  includeSubfolders: true,
  sortBy: 'name',
  sortOrder: 'asc',
  pagination: {
    page: 1,
    pageSize: 50
  }
}

/**
 * 默认状态
 */
const defaultState: AssetStateType = {
  assets: [],
  selectedAssets: [],
  currentFolderId: null,
  searchCriteria: defaultSearchCriteria,
  searchResult: null,
  processingTasks: [],
  statistics: null,
  viewMode: ViewMode.GRID,
  sortBy: 'name',
  sortOrder: 'asc',
  
  filters: {
    types: [],
    tags: [],
    favoritesOnly: false,
    showHidden: false
  },
  
  ui: {
    showPreview: true,
    showProperties: true,
    showThumbnails: true,
    thumbnailSize: 'medium',
    sidebarWidth: 280,
    previewWidth: 320
  },
  
  loading: {
    isLoading: false,
    message: null
  },
  
  error: {
    hasError: false,
    message: null,
    details: null
  },
  
  dragDrop: {
    isDragging: false,
    draggedAssets: [],
    dropTarget: null
  }
}

/**
 * 资产状态实例
 */
export const AssetState: State<AssetStateType> = hookstate(defaultState)

/**
 * 资产状态操作函数
 */
export const AssetActions = {
  /**
   * 设置所有资产
   */
  setAssets: (assets: Asset[]) => {
    AssetState.assets.set(assets)
  },
  
  /**
   * 添加资产
   */
  addAsset: (asset: Asset) => {
    const current = AssetState.assets.get()
    AssetState.assets.set([...current, asset])
  },
  
  /**
   * 更新资产
   */
  updateAsset: (id: string, updates: Partial<Asset>) => {
    const current = AssetState.assets.get()
    const index = current.findIndex(asset => asset.id === id)
    if (index !== -1) {
      const updated = [...current]
      updated[index] = { ...updated[index], ...updates }
      AssetState.assets.set(updated)
    }
  },
  
  /**
   * 移除资产
   */
  removeAsset: (id: string) => {
    const current = AssetState.assets.get()
    AssetState.assets.set(current.filter(asset => asset.id !== id))
  },
  
  /**
   * 设置选中资产
   */
  setSelectedAssets: (assetIds: string[]) => {
    AssetState.selectedAssets.set(assetIds)
  },
  
  /**
   * 添加选中资产
   */
  addSelectedAsset: (assetId: string) => {
    const current = AssetState.selectedAssets.get()
    if (!current.includes(assetId)) {
      AssetState.selectedAssets.set([...current, assetId])
    }
  },
  
  /**
   * 移除选中资产
   */
  removeSelectedAsset: (assetId: string) => {
    const current = AssetState.selectedAssets.get()
    AssetState.selectedAssets.set(current.filter(id => id !== assetId))
  },
  
  /**
   * 清空选中资产
   */
  clearSelectedAssets: () => {
    AssetState.selectedAssets.set([])
  },
  
  /**
   * 设置当前文件夹
   */
  setCurrentFolder: (folderId: string | null) => {
    AssetState.currentFolderId.set(folderId)
  },
  
  /**
   * 设置搜索条件
   */
  setSearchCriteria: (criteria: Partial<AssetSearchCriteria>) => {
    const current = AssetState.searchCriteria.get()
    AssetState.searchCriteria.set({ ...current, ...criteria })
  },
  
  /**
   * 设置搜索结果
   */
  setSearchResult: (result: AssetSearchResult | null) => {
    AssetState.searchResult.set(result)
  },
  
  /**
   * 添加处理任务
   */
  addProcessingTask: (task: AssetProcessingTask) => {
    const current = AssetState.processingTasks.get()
    AssetState.processingTasks.set([...current, task])
  },
  
  /**
   * 更新处理任务
   */
  updateProcessingTask: (id: string, updates: Partial<AssetProcessingTask>) => {
    const current = AssetState.processingTasks.get()
    const index = current.findIndex(task => task.id === id)
    if (index !== -1) {
      const updated = [...current]
      updated[index] = { ...updated[index], ...updates }
      AssetState.processingTasks.set(updated)
    }
  },
  
  /**
   * 移除处理任务
   */
  removeProcessingTask: (id: string) => {
    const current = AssetState.processingTasks.get()
    AssetState.processingTasks.set(current.filter(task => task.id !== id))
  },
  
  /**
   * 设置统计信息
   */
  setStatistics: (statistics: AssetStatistics) => {
    AssetState.statistics.set(statistics)
  },
  
  /**
   * 设置视图模式
   */
  setViewMode: (mode: ViewMode) => {
    AssetState.viewMode.set(mode)
  },
  
  /**
   * 设置排序
   */
  setSorting: (sortBy: AssetStateType['sortBy'], sortOrder: AssetStateType['sortOrder']) => {
    AssetState.sortBy.set(sortBy)
    AssetState.sortOrder.set(sortOrder)
  },
  
  /**
   * 设置过滤器
   */
  setFilters: (filters: Partial<AssetStateType['filters']>) => {
    const current = AssetState.filters.get()
    AssetState.filters.set({ ...current, ...filters })
  },
  
  /**
   * 切换UI面板
   */
  toggleUIPanel: (panel: keyof AssetStateType['ui']) => {
    const current = AssetState.ui[panel].get()
    AssetState.ui[panel].set(!current)
  },
  
  /**
   * 设置面板宽度
   */
  setPanelWidth: (panel: 'sidebarWidth' | 'previewWidth', width: number) => {
    AssetState.ui[panel].set(width)
  },
  
  /**
   * 设置缩略图大小
   */
  setThumbnailSize: (size: AssetStateType['ui']['thumbnailSize']) => {
    AssetState.ui.thumbnailSize.set(size)
  },
  
  /**
   * 设置加载状态
   */
  setLoading: (isLoading: boolean, message?: string) => {
    AssetState.loading.set({
      isLoading,
      message: message || null
    })
  },
  
  /**
   * 设置错误状态
   */
  setError: (message: string, details?: any) => {
    AssetState.error.set({
      hasError: true,
      message,
      details
    })
  },
  
  /**
   * 清除错误状态
   */
  clearError: () => {
    AssetState.error.set({
      hasError: false,
      message: null,
      details: null
    })
  },
  
  /**
   * 开始拖拽
   */
  startDrag: (assetIds: string[]) => {
    AssetState.dragDrop.set({
      isDragging: true,
      draggedAssets: assetIds,
      dropTarget: null
    })
  },
  
  /**
   * 设置拖拽目标
   */
  setDropTarget: (target: string | null) => {
    AssetState.dragDrop.dropTarget.set(target)
  },
  
  /**
   * 结束拖拽
   */
  endDrag: () => {
    AssetState.dragDrop.set({
      isDragging: false,
      draggedAssets: [],
      dropTarget: null
    })
  }
}
