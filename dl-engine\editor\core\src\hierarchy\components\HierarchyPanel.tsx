/**
 * 层次结构面板组件
 * 
 * 显示场景中所有对象的层次结构，支持选择、重命名、拖拽等操作
 */

import React, { useState, useCallback } from 'react'
import { useHookstate } from '@hookstate/core'
import { EditorState, EditorActions } from '../../services/EditorState'
import { Entity } from '@dl-engine/engine-ecs'

/**
 * 层次结构项数据
 */
interface HierarchyItem {
  id: string
  name: string
  entity: Entity
  children: HierarchyItem[]
  expanded: boolean
  visible: boolean
  locked: boolean
  type: string
}

/**
 * 层次结构面板属性
 */
export interface HierarchyPanelProps {
  /** 面板类名 */
  className?: string
  /** 面板样式 */
  style?: React.CSSProperties
}

/**
 * 层次结构面板组件
 */
const HierarchyPanel: React.FC<HierarchyPanelProps> = ({
  className = '',
  style = {}
}) => {
  const editorState = useHookstate(EditorState)
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  
  // 获取编辑器状态
  const selection = editorState.selection.get()
  const rootEntity = editorState.rootEntity.get()
  
  // 模拟层次结构数据
  const [hierarchyData] = useState<HierarchyItem[]>([
    {
      id: 'scene',
      name: '场景',
      entity: 1 as Entity,
      expanded: true,
      visible: true,
      locked: false,
      type: 'scene',
      children: [
        {
          id: 'camera',
          name: '主相机',
          entity: 2 as Entity,
          expanded: false,
          visible: true,
          locked: false,
          type: 'camera',
          children: []
        },
        {
          id: 'light',
          name: '方向光',
          entity: 3 as Entity,
          expanded: false,
          visible: true,
          locked: false,
          type: 'light',
          children: []
        },
        {
          id: 'objects',
          name: '对象组',
          entity: 4 as Entity,
          expanded: true,
          visible: true,
          locked: false,
          type: 'group',
          children: [
            {
              id: 'cube',
              name: '立方体',
              entity: 5 as Entity,
              expanded: false,
              visible: true,
              locked: false,
              type: 'mesh',
              children: []
            },
            {
              id: 'sphere',
              name: '球体',
              entity: 6 as Entity,
              expanded: false,
              visible: true,
              locked: false,
              type: 'mesh',
              children: []
            }
          ]
        }
      ]
    }
  ])
  
  /**
   * 获取项目图标
   */
  const getItemIcon = (type: string) => {
    switch (type) {
      case 'scene': return '🌍'
      case 'camera': return '📷'
      case 'light': return '💡'
      case 'group': return '📁'
      case 'mesh': return '📦'
      default: return '📄'
    }
  }
  
  /**
   * 处理项目选择
   */
  const handleItemSelect = useCallback((item: HierarchyItem, event: React.MouseEvent) => {
    event.stopPropagation()
    
    if (event.ctrlKey || event.metaKey) {
      // 多选模式
      if (selection.selectedEntities.includes(item.entity)) {
        EditorActions.removeFromSelection(item.entity)
      } else {
        EditorActions.addToSelection(item.entity)
      }
    } else {
      // 单选模式
      EditorActions.setSelection([item.entity], item.entity)
    }
  }, [selection])
  
  /**
   * 处理项目展开/折叠
   */
  const handleItemToggle = useCallback((itemId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    
    setExpandedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(itemId)) {
        newSet.delete(itemId)
      } else {
        newSet.add(itemId)
      }
      return newSet
    })
  }, [])
  
  /**
   * 处理可见性切换
   */
  const handleVisibilityToggle = useCallback((item: HierarchyItem, event: React.MouseEvent) => {
    event.stopPropagation()
    // TODO: 实现可见性切换逻辑
    console.log('切换可见性:', item.name)
  }, [])
  
  /**
   * 处理锁定切换
   */
  const handleLockToggle = useCallback((item: HierarchyItem, event: React.MouseEvent) => {
    event.stopPropagation()
    // TODO: 实现锁定切换逻辑
    console.log('切换锁定:', item.name)
  }, [])
  
  /**
   * 处理右键菜单
   */
  const handleContextMenu = useCallback((item: HierarchyItem, event: React.MouseEvent) => {
    event.preventDefault()
    // TODO: 实现右键菜单
    console.log('右键菜单:', item.name)
  }, [])
  
  /**
   * 渲染层次结构项
   */
  const renderHierarchyItem = (item: HierarchyItem, level: number = 0): React.ReactNode => {
    const isSelected = selection.selectedEntities.includes(item.entity)
    const isExpanded = expandedItems.has(item.id) || item.expanded
    const hasChildren = item.children.length > 0
    const paddingLeft = level * 16 + 8
    
    // 搜索过滤
    if (searchTerm && !item.name.toLowerCase().includes(searchTerm.toLowerCase())) {
      return null
    }
    
    return (
      <div key={item.id}>
        {/* 项目本身 */}
        <div
          className={`hierarchy-item ${isSelected ? 'selected' : ''}`}
          style={{ paddingLeft }}
          onClick={(e) => handleItemSelect(item, e)}
          onContextMenu={(e) => handleContextMenu(item, e)}
        >
          {/* 展开/折叠按钮 */}
          {hasChildren && (
            <button
              className={`hierarchy-icon ${isExpanded ? 'expanded' : ''}`}
              onClick={(e) => handleItemToggle(item.id, e)}
            >
              ▶
            </button>
          )}
          {!hasChildren && <div className="hierarchy-icon" />}
          
          {/* 项目图标 */}
          <span className="hierarchy-icon">{getItemIcon(item.type)}</span>
          
          {/* 项目名称 */}
          <span className="hierarchy-name flex-1">{item.name}</span>
          
          {/* 可见性按钮 */}
          <button
            className={`hierarchy-visibility ${item.visible ? 'visible' : 'hidden'}`}
            onClick={(e) => handleVisibilityToggle(item, e)}
            title={item.visible ? '隐藏' : '显示'}
          >
            {item.visible ? '👁️' : '🙈'}
          </button>
          
          {/* 锁定按钮 */}
          <button
            className={`hierarchy-lock ${item.locked ? 'locked' : 'unlocked'}`}
            onClick={(e) => handleLockToggle(item, e)}
            title={item.locked ? '解锁' : '锁定'}
          >
            {item.locked ? '🔒' : '🔓'}
          </button>
        </div>
        
        {/* 子项目 */}
        {hasChildren && isExpanded && (
          <div className="hierarchy-children">
            {item.children.map(child => renderHierarchyItem(child, level + 1))}
          </div>
        )}
      </div>
    )
  }
  
  /**
   * 处理搜索
   */
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value)
  }
  
  /**
   * 展开所有项目
   */
  const expandAll = () => {
    const getAllIds = (items: HierarchyItem[]): string[] => {
      let ids: string[] = []
      items.forEach(item => {
        ids.push(item.id)
        if (item.children.length > 0) {
          ids = ids.concat(getAllIds(item.children))
        }
      })
      return ids
    }
    
    setExpandedItems(new Set(getAllIds(hierarchyData)))
  }
  
  /**
   * 折叠所有项目
   */
  const collapseAll = () => {
    setExpandedItems(new Set())
  }
  
  return (
    <div className={`hierarchy-panel ${className}`} style={style}>
      {/* 面板头部 */}
      <div className="panel-header">
        <span>层次结构</span>
        <div className="flex items-center gap-2">
          <button
            className="text-xs px-2 py-1 bg-gray-600 hover:bg-gray-500 rounded"
            onClick={expandAll}
            title="展开所有"
          >
            ⊞
          </button>
          <button
            className="text-xs px-2 py-1 bg-gray-600 hover:bg-gray-500 rounded"
            onClick={collapseAll}
            title="折叠所有"
          >
            ⊟
          </button>
        </div>
      </div>
      
      {/* 搜索框 */}
      <div className="p-2 border-b border-gray-600">
        <input
          type="text"
          placeholder="搜索对象..."
          value={searchTerm}
          onChange={handleSearch}
          className="w-full px-3 py-1 bg-gray-700 border border-gray-600 rounded text-sm text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
        />
      </div>
      
      {/* 层次结构内容 */}
      <div className="panel-content">
        {hierarchyData.map(item => renderHierarchyItem(item))}
        
        {/* 空状态 */}
        {hierarchyData.length === 0 && (
          <div className="text-center text-gray-400 py-8">
            <div className="text-4xl mb-2">📁</div>
            <div>场景为空</div>
            <div className="text-sm">拖拽资产到场景中开始创建</div>
          </div>
        )}
      </div>
    </div>
  )
}

export default HierarchyPanel
