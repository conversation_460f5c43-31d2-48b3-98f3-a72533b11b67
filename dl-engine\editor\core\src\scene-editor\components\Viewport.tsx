/**
 * 3D视口组件
 * 
 * 渲染3D场景内容
 */

import React from 'react'
import { useFrame, useThree } from '@react-three/fiber'

/**
 * 视口属性
 */
export interface ViewportProps {
  className?: string
  style?: React.CSSProperties
}

/**
 * 视口组件
 */
const Viewport: React.FC<ViewportProps> = ({
  className = '',
  style = {}
}) => {
  const { scene, camera, gl } = useThree()

  // 渲染循环
  useFrame(() => {
    // 这里可以添加自定义渲染逻辑
  })

  return (
    <>
      {/* 基础几何体用于测试 */}
      <mesh position={[0, 0, 0]}>
        <boxGeometry args={[1, 1, 1]} />
        <meshStandardMaterial color="orange" />
      </mesh>
      
      <mesh position={[2, 0, 0]}>
        <sphereGeometry args={[0.5, 32, 32]} />
        <meshStandardMaterial color="blue" />
      </mesh>
      
      <mesh position={[-2, 0, 0]}>
        <cylinderGeometry args={[0.5, 0.5, 1, 32]} />
        <meshStandardMaterial color="green" />
      </mesh>
    </>
  )
}

export default Viewport
