/**
 * 编辑器状态测试
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { EditorState, EditorActions } from '../services/EditorState'
import { EditorMode, EditorTool } from '../types'

describe('EditorState', () => {
  beforeEach(() => {
    // 重置状态
    EditorActions.setMode(EditorMode.SELECT)
    EditorActions.setTool(EditorTool.SELECT)
    EditorActions.clearSelection()
    EditorActions.clearError()
    EditorActions.clearWarning()
  })

  it('should have default values', () => {
    const state = EditorState.get()
    
    expect(state.mode).toBe(EditorMode.SELECT)
    expect(state.tool).toBe(EditorTool.SELECT)
    expect(state.uiEnabled).toBe(true)
    expect(state.loading).toBe(false)
    expect(state.selection.selectedEntities).toEqual([])
    expect(state.error.hasError).toBe(false)
    expect(state.warning.hasWarning).toBe(false)
  })

  it('should update mode', () => {
    EditorActions.setMode(EditorMode.FLY)
    expect(EditorState.mode.get()).toBe(EditorMode.FLY)
  })

  it('should update tool', () => {
    EditorActions.setTool(EditorTool.MOVE)
    expect(EditorState.tool.get()).toBe(EditorTool.MOVE)
  })

  it('should manage selection', () => {
    const entity1 = 1 as any
    const entity2 = 2 as any
    
    EditorActions.setSelection([entity1, entity2], entity1)
    
    const selection = EditorState.selection.get()
    expect(selection.selectedEntities).toEqual([entity1, entity2])
    expect(selection.primaryEntity).toBe(entity1)
  })

  it('should add to selection', () => {
    const entity1 = 1 as any
    const entity2 = 2 as any
    
    EditorActions.setSelection([entity1])
    EditorActions.addToSelection(entity2)
    
    const selection = EditorState.selection.get()
    expect(selection.selectedEntities).toEqual([entity1, entity2])
  })

  it('should remove from selection', () => {
    const entity1 = 1 as any
    const entity2 = 2 as any
    
    EditorActions.setSelection([entity1, entity2])
    EditorActions.removeFromSelection(entity1)
    
    const selection = EditorState.selection.get()
    expect(selection.selectedEntities).toEqual([entity2])
  })

  it('should clear selection', () => {
    const entity1 = 1 as any
    
    EditorActions.setSelection([entity1])
    EditorActions.clearSelection()
    
    const selection = EditorState.selection.get()
    expect(selection.selectedEntities).toEqual([])
    expect(selection.primaryEntity).toBeUndefined()
  })

  it('should manage error state', () => {
    const errorMessage = 'Test error'
    const errorDetails = { code: 500 }
    
    EditorActions.setError(errorMessage, errorDetails)
    
    const error = EditorState.error.get()
    expect(error.hasError).toBe(true)
    expect(error.message).toBe(errorMessage)
    expect(error.details).toEqual(errorDetails)
    
    EditorActions.clearError()
    
    const clearedError = EditorState.error.get()
    expect(clearedError.hasError).toBe(false)
    expect(clearedError.message).toBeNull()
    expect(clearedError.details).toBeNull()
  })

  it('should manage warning state', () => {
    const warningMessage = 'Test warning'
    
    EditorActions.setWarning(warningMessage)
    
    const warning = EditorState.warning.get()
    expect(warning.hasWarning).toBe(true)
    expect(warning.message).toBe(warningMessage)
    
    EditorActions.clearWarning()
    
    const clearedWarning = EditorState.warning.get()
    expect(clearedWarning.hasWarning).toBe(false)
    expect(clearedWarning.message).toBeNull()
  })

  it('should toggle panel visibility', () => {
    const initialHierarchy = EditorState.panels.hierarchy.get()
    
    EditorActions.togglePanel('hierarchy')
    
    expect(EditorState.panels.hierarchy.get()).toBe(!initialHierarchy)
  })

  it('should set loading state', () => {
    EditorActions.setLoading(true)
    expect(EditorState.loading.get()).toBe(true)
    
    EditorActions.setLoading(false)
    expect(EditorState.loading.get()).toBe(false)
  })

  it('should set scene information', () => {
    const scenePath = '/test/scene.scene'
    const sceneName = 'Test Scene'
    const projectName = 'Test Project'
    const sceneAssetID = 'asset-123'
    
    EditorActions.setScene(scenePath, sceneName, projectName, sceneAssetID)
    
    expect(EditorState.scenePath.get()).toBe(scenePath)
    expect(EditorState.sceneName.get()).toBe(sceneName)
    expect(EditorState.projectName.get()).toBe(projectName)
    expect(EditorState.sceneAssetID.get()).toBe(sceneAssetID)
  })
})
