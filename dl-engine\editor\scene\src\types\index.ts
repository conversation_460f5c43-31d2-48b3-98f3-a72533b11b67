/**
 * 场景编辑器类型定义
 */

import { Vector3, Quaternion, Color, Material, Geometry, Object3D } from 'three'

/**
 * 编辑器模式
 */
export enum EditorMode {
  SELECT = 'select',
  MOVE = 'move',
  ROTATE = 'rotate',
  SCALE = 'scale',
  FLY = 'fly',
  ORBIT = 'orbit',
  PAN = 'pan'
}

/**
 * 视口类型
 */
export enum ViewportType {
  PERSPECTIVE = 'perspective',
  ORTHOGRAPHIC = 'orthographic',
  TOP = 'top',
  FRONT = 'front',
  RIGHT = 'right',
  LEFT = 'left',
  BACK = 'back',
  BOTTOM = 'bottom'
}

/**
 * 坐标空间
 */
export enum CoordinateSpace {
  WORLD = 'world',
  LOCAL = 'local',
  SCREEN = 'screen'
}

/**
 * 捕捉类型
 */
export enum SnapType {
  NONE = 'none',
  GRID = 'grid',
  VERTEX = 'vertex',
  EDGE = 'edge',
  FACE = 'face',
  OBJECT = 'object'
}

/**
 * 对象类型
 */
export enum ObjectType {
  EMPTY = 'empty',
  MESH = 'mesh',
  LIGHT = 'light',
  CAMERA = 'camera',
  GROUP = 'group',
  SPRITE = 'sprite',
  LINE = 'line',
  POINTS = 'points',
  BONE = 'bone',
  HELPER = 'helper'
}

/**
 * 光源类型
 */
export enum LightType {
  AMBIENT = 'ambient',
  DIRECTIONAL = 'directional',
  POINT = 'point',
  SPOT = 'spot',
  HEMISPHERE = 'hemisphere',
  AREA = 'area'
}

/**
 * 材质类型
 */
export enum MaterialType {
  BASIC = 'basic',
  LAMBERT = 'lambert',
  PHONG = 'phong',
  STANDARD = 'standard',
  PHYSICAL = 'physical',
  TOON = 'toon',
  SHADER = 'shader'
}

/**
 * 几何体类型
 */
export enum GeometryType {
  BOX = 'box',
  SPHERE = 'sphere',
  CYLINDER = 'cylinder',
  CONE = 'cone',
  PLANE = 'plane',
  TORUS = 'torus',
  CUSTOM = 'custom'
}

/**
 * 变换组件
 */
export interface Transform {
  position: Vector3
  rotation: Quaternion
  scale: Vector3
}

/**
 * 场景对象
 */
export interface SceneObject {
  /** 对象ID */
  id: string
  
  /** 对象名称 */
  name: string
  
  /** 对象类型 */
  type: ObjectType
  
  /** 父对象ID */
  parentId?: string
  
  /** 子对象ID列表 */
  children: string[]
  
  /** 变换信息 */
  transform: Transform
  
  /** 是否可见 */
  visible: boolean
  
  /** 是否锁定 */
  locked: boolean
  
  /** 是否静态 */
  static: boolean
  
  /** 图层 */
  layer: number
  
  /** 标签 */
  tags: string[]
  
  /** 自定义属性 */
  userData: Record<string, any>
  
  /** Three.js 对象引用 */
  object3D?: Object3D
}

/**
 * 网格对象
 */
export interface MeshObject extends SceneObject {
  type: ObjectType.MESH
  
  /** 几何体类型 */
  geometryType: GeometryType
  
  /** 几何体参数 */
  geometryParams: Record<string, any>
  
  /** 材质ID */
  materialId: string
  
  /** 是否投射阴影 */
  castShadow: boolean
  
  /** 是否接收阴影 */
  receiveShadow: boolean
  
  /** 几何体引用 */
  geometry?: Geometry
  
  /** 材质引用 */
  material?: Material
}

/**
 * 光源对象
 */
export interface LightObject extends SceneObject {
  type: ObjectType.LIGHT
  
  /** 光源类型 */
  lightType: LightType
  
  /** 光源颜色 */
  color: Color
  
  /** 光源强度 */
  intensity: number
  
  /** 光源距离（点光源、聚光灯） */
  distance?: number
  
  /** 光源衰减（点光源、聚光灯） */
  decay?: number
  
  /** 聚光灯角度 */
  angle?: number
  
  /** 聚光灯边缘模糊 */
  penumbra?: number
  
  /** 是否投射阴影 */
  castShadow: boolean
  
  /** 阴影配置 */
  shadowConfig?: ShadowConfig
}

/**
 * 相机对象
 */
export interface CameraObject extends SceneObject {
  type: ObjectType.CAMERA
  
  /** 相机类型 */
  cameraType: 'perspective' | 'orthographic'
  
  /** 视野角度（透视相机） */
  fov?: number
  
  /** 宽高比 */
  aspect: number
  
  /** 近裁剪面 */
  near: number
  
  /** 远裁剪面 */
  far: number
  
  /** 正交相机大小 */
  size?: number
  
  /** 是否为主相机 */
  isMain: boolean
}

/**
 * 阴影配置
 */
export interface ShadowConfig {
  /** 阴影贴图大小 */
  mapSize: { width: number; height: number }
  
  /** 阴影相机近裁剪面 */
  cameraNear: number
  
  /** 阴影相机远裁剪面 */
  cameraFar: number
  
  /** 阴影偏移 */
  bias: number
  
  /** 阴影半径 */
  radius: number
  
  /** 阴影模糊度 */
  blurSamples: number
}

/**
 * 场景配置
 */
export interface SceneConfig {
  /** 背景类型 */
  backgroundType: 'color' | 'skybox' | 'environment'
  
  /** 背景颜色 */
  backgroundColor?: Color
  
  /** 天空盒纹理 */
  skyboxTexture?: string
  
  /** 环境贴图 */
  environmentMap?: string
  
  /** 雾效配置 */
  fog?: FogConfig
  
  /** 全局光照配置 */
  globalIllumination?: GlobalIlluminationConfig
  
  /** 后处理配置 */
  postProcessing?: PostProcessingConfig
}

/**
 * 雾效配置
 */
export interface FogConfig {
  /** 是否启用雾效 */
  enabled: boolean
  
  /** 雾效类型 */
  type: 'linear' | 'exponential'
  
  /** 雾效颜色 */
  color: Color
  
  /** 雾效近距离 */
  near: number
  
  /** 雾效远距离 */
  far: number
  
  /** 雾效密度（指数雾） */
  density?: number
}

/**
 * 全局光照配置
 */
export interface GlobalIlluminationConfig {
  /** 是否启用全局光照 */
  enabled: boolean
  
  /** 环境光强度 */
  ambientIntensity: number
  
  /** 环境光颜色 */
  ambientColor: Color
  
  /** 是否启用光照探针 */
  lightProbes: boolean
  
  /** 是否启用反射探针 */
  reflectionProbes: boolean
}

/**
 * 后处理配置
 */
export interface PostProcessingConfig {
  /** 是否启用后处理 */
  enabled: boolean
  
  /** 抗锯齿 */
  antialiasing: {
    enabled: boolean
    type: 'FXAA' | 'SMAA' | 'TAA'
  }
  
  /** 色调映射 */
  toneMapping: {
    enabled: boolean
    type: 'linear' | 'reinhard' | 'cineon' | 'aces'
    exposure: number
  }
  
  /** 泛光效果 */
  bloom: {
    enabled: boolean
    threshold: number
    strength: number
    radius: number
  }
  
  /** 景深效果 */
  depthOfField: {
    enabled: boolean
    focusDistance: number
    focalLength: number
    bokehScale: number
  }
  
  /** 屏幕空间环境光遮蔽 */
  ssao: {
    enabled: boolean
    radius: number
    intensity: number
    bias: number
  }
}

/**
 * 选择信息
 */
export interface SelectionInfo {
  /** 选中的对象ID列表 */
  selectedObjects: string[]
  
  /** 主选中对象ID */
  primaryObject?: string
  
  /** 选择框 */
  selectionBox?: {
    min: Vector3
    max: Vector3
  }
  
  /** 选择中心点 */
  selectionCenter?: Vector3
}

/**
 * 视口配置
 */
export interface ViewportConfig {
  /** 视口类型 */
  type: ViewportType
  
  /** 是否显示网格 */
  showGrid: boolean
  
  /** 网格大小 */
  gridSize: number
  
  /** 网格分割数 */
  gridDivisions: number
  
  /** 是否显示辅助工具 */
  showGizmos: boolean
  
  /** 是否显示统计信息 */
  showStats: boolean
  
  /** 渲染模式 */
  renderMode: 'solid' | 'wireframe' | 'points'
  
  /** 着色模式 */
  shadingMode: 'flat' | 'smooth'
  
  /** 是否启用光照 */
  enableLighting: boolean
  
  /** 是否启用阴影 */
  enableShadows: boolean
  
  /** 是否启用后处理 */
  enablePostProcessing: boolean
}

/**
 * 编辑器设置
 */
export interface EditorSettings {
  /** 捕捉设置 */
  snap: {
    enabled: boolean
    type: SnapType
    increment: number
  }
  
  /** 自动保存设置 */
  autoSave: {
    enabled: boolean
    interval: number
  }
  
  /** 撤销重做设置 */
  undoRedo: {
    maxSteps: number
  }
  
  /** 性能设置 */
  performance: {
    maxFPS: number
    enableLOD: boolean
    enableFrustumCulling: boolean
    enableOcclusionCulling: boolean
  }
  
  /** 调试设置 */
  debug: {
    showBoundingBoxes: boolean
    showWireframes: boolean
    showNormals: boolean
    showLightHelpers: boolean
    showCameraHelpers: boolean
  }
}

/**
 * 操作历史记录
 */
export interface HistoryEntry {
  /** 操作ID */
  id: string
  
  /** 操作类型 */
  type: string
  
  /** 操作描述 */
  description: string
  
  /** 操作时间 */
  timestamp: Date
  
  /** 撤销数据 */
  undoData: any
  
  /** 重做数据 */
  redoData: any
}

/**
 * 场景事件
 */
export interface SceneEvent {
  /** 事件类型 */
  type: string
  
  /** 事件数据 */
  data: any
  
  /** 事件时间 */
  timestamp: Date
  
  /** 事件源 */
  source?: string
}
