/**
 * 检查器面板组件
 * 
 * 显示详细的对象信息和调试数据
 */

import React from 'react'
import { useHookstate } from '@hookstate/core'
import { EditorState } from '../../services/EditorState'

/**
 * 检查器面板属性
 */
export interface InspectorPanelProps {
  className?: string
  style?: React.CSSProperties
}

/**
 * 检查器面板组件
 */
const InspectorPanel: React.FC<InspectorPanelProps> = ({
  className = '',
  style = {}
}) => {
  const editorState = useHookstate(EditorState)
  const selection = editorState.selection.get()

  return (
    <div className={`inspector-panel ${className}`} style={style}>
      <div className="panel-header">
        <span>检查器</span>
      </div>
      
      <div className="panel-content">
        {selection.selectedEntities.length > 0 ? (
          <div className="space-y-4">
            {/* 基本信息 */}
            <div className="property-group">
              <div className="property-group-title">基本信息</div>
              <div className="pl-4 space-y-2 text-sm">
                <div>实体ID: {selection.primaryEntity}</div>
                <div>类型: 网格对象</div>
                <div>状态: 活跃</div>
                <div>层级: 0</div>
              </div>
            </div>

            {/* 性能信息 */}
            <div className="property-group">
              <div className="property-group-title">性能信息</div>
              <div className="pl-4 space-y-2 text-sm">
                <div>顶点数: 8</div>
                <div>三角形数: 12</div>
                <div>材质数: 1</div>
                <div>纹理数: 1</div>
              </div>
            </div>

            {/* 渲染信息 */}
            <div className="property-group">
              <div className="property-group-title">渲染信息</div>
              <div className="pl-4 space-y-2 text-sm">
                <div>可见: 是</div>
                <div>投射阴影: 是</div>
                <div>接收阴影: 是</div>
                <div>视锥剔除: 是</div>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center text-gray-400 py-8">
            <div className="text-4xl mb-2">🔍</div>
            <div>未选择对象</div>
            <div className="text-sm">选择一个对象来查看详细信息</div>
          </div>
        )}
      </div>
    </div>
  )
}

export default InspectorPanel
