/**
 * 教育专用节点库
 * 
 * 专为数字化学习场景设计的可视化编程节点
 */

import { NodeDefinition, NodeType, DataType, SocketType } from '../../types'

/**
 * 显示消息节点
 */
export const ShowMessageNode: NodeDefinition = {
  type: NodeType.SHOW_MESSAGE,
  name: '显示消息',
  description: '在屏幕上显示一条消息',
  category: '教育',
  icon: '💬',
  color: '#4CAF50',
  educational: true,
  educationLevel: 'beginner',
  inputs: [
    {
      id: 'exec_in',
      name: '执行',
      type: SocketType.INPUT,
      dataType: DataType.VOID,
      isExecution: true,
      required: true
    },
    {
      id: 'message',
      name: '消息内容',
      type: SocketType.INPUT,
      dataType: DataType.STRING,
      defaultValue: '你好，世界！',
      description: '要显示的消息文本'
    },
    {
      id: 'duration',
      name: '显示时长',
      type: SocketType.INPUT,
      dataType: DataType.NUMBER,
      defaultValue: 3,
      description: '消息显示的秒数'
    }
  ],
  outputs: [
    {
      id: 'exec_out',
      name: '完成',
      type: SocketType.OUTPUT,
      dataType: DataType.VOID,
      isExecution: true
    }
  ],
  execute: (inputs, context) => {
    const message = inputs.message || '你好，世界！'
    const duration = inputs.duration || 3
    
    // TODO: 实现消息显示逻辑
    console.log(`显示消息: ${message} (${duration}秒)`)
    
    return {}
  }
}

/**
 * 移动对象节点
 */
export const MoveObjectNode: NodeDefinition = {
  type: NodeType.MOVE_OBJECT,
  name: '移动对象',
  description: '移动一个3D对象到指定位置',
  category: '教育',
  icon: '🚀',
  color: '#2196F3',
  educational: true,
  educationLevel: 'beginner',
  inputs: [
    {
      id: 'exec_in',
      name: '执行',
      type: SocketType.INPUT,
      dataType: DataType.VOID,
      isExecution: true,
      required: true
    },
    {
      id: 'object',
      name: '对象',
      type: SocketType.INPUT,
      dataType: DataType.OBJECT,
      required: true,
      description: '要移动的对象'
    },
    {
      id: 'position',
      name: '目标位置',
      type: SocketType.INPUT,
      dataType: DataType.VECTOR3,
      defaultValue: { x: 0, y: 0, z: 0 },
      description: '对象的目标位置'
    },
    {
      id: 'speed',
      name: '移动速度',
      type: SocketType.INPUT,
      dataType: DataType.NUMBER,
      defaultValue: 1,
      description: '移动的速度（单位/秒）'
    }
  ],
  outputs: [
    {
      id: 'exec_out',
      name: '完成',
      type: SocketType.OUTPUT,
      dataType: DataType.VOID,
      isExecution: true
    },
    {
      id: 'final_position',
      name: '最终位置',
      type: SocketType.OUTPUT,
      dataType: DataType.VECTOR3,
      description: '对象的最终位置'
    }
  ],
  execute: (inputs, context) => {
    const object = inputs.object
    const position = inputs.position || { x: 0, y: 0, z: 0 }
    const speed = inputs.speed || 1
    
    // TODO: 实现对象移动逻辑
    console.log(`移动对象到位置: (${position.x}, ${position.y}, ${position.z})，速度: ${speed}`)
    
    return {
      final_position: position
    }
  }
}

/**
 * 改变颜色节点
 */
export const ChangeColorNode: NodeDefinition = {
  type: NodeType.CHANGE_COLOR,
  name: '改变颜色',
  description: '改变对象的颜色',
  category: '教育',
  icon: '🎨',
  color: '#FF9800',
  educational: true,
  educationLevel: 'beginner',
  inputs: [
    {
      id: 'exec_in',
      name: '执行',
      type: SocketType.INPUT,
      dataType: DataType.VOID,
      isExecution: true,
      required: true
    },
    {
      id: 'object',
      name: '对象',
      type: SocketType.INPUT,
      dataType: DataType.OBJECT,
      required: true,
      description: '要改变颜色的对象'
    },
    {
      id: 'color',
      name: '新颜色',
      type: SocketType.INPUT,
      dataType: DataType.COLOR,
      defaultValue: '#FF0000',
      description: '对象的新颜色'
    }
  ],
  outputs: [
    {
      id: 'exec_out',
      name: '完成',
      type: SocketType.OUTPUT,
      dataType: DataType.VOID,
      isExecution: true
    },
    {
      id: 'old_color',
      name: '原颜色',
      type: SocketType.OUTPUT,
      dataType: DataType.COLOR,
      description: '对象的原始颜色'
    }
  ],
  execute: (inputs, context) => {
    const object = inputs.object
    const color = inputs.color || '#FF0000'
    
    // TODO: 实现颜色改变逻辑
    console.log(`改变对象颜色为: ${color}`)
    
    return {
      old_color: '#FFFFFF' // 假设原色为白色
    }
  }
}

/**
 * 等待节点
 */
export const WaitNode: NodeDefinition = {
  type: NodeType.WAIT,
  name: '等待',
  description: '等待指定的时间',
  category: '教育',
  icon: '⏰',
  color: '#9C27B0',
  educational: true,
  educationLevel: 'beginner',
  inputs: [
    {
      id: 'exec_in',
      name: '执行',
      type: SocketType.INPUT,
      dataType: DataType.VOID,
      isExecution: true,
      required: true
    },
    {
      id: 'seconds',
      name: '等待秒数',
      type: SocketType.INPUT,
      dataType: DataType.NUMBER,
      defaultValue: 1,
      description: '等待的秒数'
    }
  ],
  outputs: [
    {
      id: 'exec_out',
      name: '完成',
      type: SocketType.OUTPUT,
      dataType: DataType.VOID,
      isExecution: true
    }
  ],
  execute: (inputs, context) => {
    const seconds = inputs.seconds || 1
    
    // TODO: 实现等待逻辑
    console.log(`等待 ${seconds} 秒`)
    
    return {}
  }
}

/**
 * 重复节点
 */
export const RepeatNode: NodeDefinition = {
  type: NodeType.REPEAT,
  name: '重复',
  description: '重复执行指定次数',
  category: '教育',
  icon: '🔄',
  color: '#607D8B',
  educational: true,
  educationLevel: 'intermediate',
  inputs: [
    {
      id: 'exec_in',
      name: '执行',
      type: SocketType.INPUT,
      dataType: DataType.VOID,
      isExecution: true,
      required: true
    },
    {
      id: 'count',
      name: '重复次数',
      type: SocketType.INPUT,
      dataType: DataType.NUMBER,
      defaultValue: 3,
      description: '重复执行的次数'
    }
  ],
  outputs: [
    {
      id: 'exec_loop',
      name: '循环体',
      type: SocketType.OUTPUT,
      dataType: DataType.VOID,
      isExecution: true,
      description: '每次循环执行的内容'
    },
    {
      id: 'exec_out',
      name: '完成',
      type: SocketType.OUTPUT,
      dataType: DataType.VOID,
      isExecution: true,
      description: '所有循环完成后执行'
    },
    {
      id: 'current_index',
      name: '当前索引',
      type: SocketType.OUTPUT,
      dataType: DataType.NUMBER,
      description: '当前循环的索引（从0开始）'
    }
  ],
  execute: (inputs, context) => {
    const count = inputs.count || 3
    
    // TODO: 实现重复逻辑
    console.log(`重复执行 ${count} 次`)
    
    return {
      current_index: 0
    }
  }
}

/**
 * 播放声音节点
 */
export const PlaySoundNode: NodeDefinition = {
  type: NodeType.PLAY_SOUND,
  name: '播放声音',
  description: '播放一个音频文件',
  category: '教育',
  icon: '🔊',
  color: '#E91E63',
  educational: true,
  educationLevel: 'beginner',
  inputs: [
    {
      id: 'exec_in',
      name: '执行',
      type: SocketType.INPUT,
      dataType: DataType.VOID,
      isExecution: true,
      required: true
    },
    {
      id: 'sound_file',
      name: '音频文件',
      type: SocketType.INPUT,
      dataType: DataType.STRING,
      required: true,
      description: '要播放的音频文件路径'
    },
    {
      id: 'volume',
      name: '音量',
      type: SocketType.INPUT,
      dataType: DataType.NUMBER,
      defaultValue: 1.0,
      description: '播放音量（0.0-1.0）'
    }
  ],
  outputs: [
    {
      id: 'exec_out',
      name: '完成',
      type: SocketType.OUTPUT,
      dataType: DataType.VOID,
      isExecution: true
    }
  ],
  execute: (inputs, context) => {
    const soundFile = inputs.sound_file
    const volume = inputs.volume || 1.0
    
    // TODO: 实现声音播放逻辑
    console.log(`播放声音: ${soundFile}，音量: ${volume}`)
    
    return {}
  }
}

/**
 * 教育节点库
 */
export const EducationNodes: NodeDefinition[] = [
  ShowMessageNode,
  MoveObjectNode,
  ChangeColorNode,
  WaitNode,
  RepeatNode,
  PlaySoundNode
]

export default EducationNodes
