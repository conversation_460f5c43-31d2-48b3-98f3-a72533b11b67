/**
 * DL-Engine 国际化提供者
 * 
 * 管理应用的多语言状态
 */

import React, { createContext, useContext, useEffect } from 'react'
import { I18nextProvider } from 'react-i18next'
import { useHookstate } from '@hookstate/core'
import i18n, { SupportedLanguage, defaultLanguage, i18nUtils } from '../i18n'

/**
 * 国际化上下文类型
 */
interface I18nContextType {
  /** 当前语言 */
  currentLanguage: SupportedLanguage
  /** 切换语言 */
  changeLanguage: (language: SupportedLanguage) => Promise<void>
  /** 获取支持的语言列表 */
  getSupportedLanguages: () => SupportedLanguage[]
  /** 获取语言显示名称 */
  getLanguageDisplayName: (language: SupportedLanguage) => string
  /** 获取语言本地名称 */
  getLanguageNativeName: (language: SupportedLanguage) => string
  /** 获取语言标志 */
  getLanguageFlag: (language: SupportedLanguage) => string
}

/**
 * 国际化上下文
 */
const I18nContext = createContext<I18nContextType | undefined>(undefined)

/**
 * 国际化提供者属性
 */
export interface I18nProviderProps {
  /** 初始语言 */
  initialLanguage?: SupportedLanguage
  /** 子组件 */
  children: React.ReactNode
}

/**
 * 语言状态
 */
const languageState = hookstate<SupportedLanguage>(defaultLanguage)

/**
 * DL-Engine 国际化提供者
 */
const I18nProvider: React.FC<I18nProviderProps> = ({
  initialLanguage = defaultLanguage,
  children
}) => {
  const currentLanguageState = useHookstate(languageState)
  
  // 初始化语言
  useEffect(() => {
    // 从本地存储加载语言设置
    const savedLanguage = localStorage.getItem('dl-editor-language') as SupportedLanguage
    const language = savedLanguage || initialLanguage
    
    if (i18nUtils.isSupportedLanguage(language)) {
      currentLanguageState.set(language)
      i18n.changeLanguage(language)
    }
  }, [initialLanguage])
  
  // 监听语言变化
  useEffect(() => {
    const language = currentLanguageState.get()
    
    // 保存到本地存储
    localStorage.setItem('dl-editor-language', language)
    
    // 更新 HTML lang 属性
    document.documentElement.lang = language
    
    // 更新页面标题的语言
    const title = i18n.t('editor.title')
    if (title && title !== 'editor.title') {
      document.title = title
    }
  }, [currentLanguageState.get()])
  
  /**
   * 切换语言
   */
  const changeLanguage = async (language: SupportedLanguage): Promise<void> => {
    if (!i18nUtils.isSupportedLanguage(language)) {
      throw new Error(`Unsupported language: ${language}`)
    }
    
    try {
      await i18n.changeLanguage(language)
      currentLanguageState.set(language)
    } catch (error) {
      console.error('Failed to change language:', error)
      throw error
    }
  }
  
  const contextValue: I18nContextType = {
    currentLanguage: currentLanguageState.get(),
    changeLanguage,
    getSupportedLanguages: i18nUtils.getSupportedLanguages,
    getLanguageDisplayName: i18nUtils.getLanguageDisplayName,
    getLanguageNativeName: i18nUtils.getLanguageNativeName,
    getLanguageFlag: i18nUtils.getLanguageFlag
  }
  
  return (
    <I18nContext.Provider value={contextValue}>
      <I18nextProvider i18n={i18n}>
        {children}
      </I18nextProvider>
    </I18nContext.Provider>
  )
}

/**
 * 使用国际化钩子
 */
export const useI18n = (): I18nContextType => {
  const context = useContext(I18nContext)
  if (!context) {
    throw new Error('useI18n must be used within an I18nProvider')
  }
  return context
}

export default I18nProvider
