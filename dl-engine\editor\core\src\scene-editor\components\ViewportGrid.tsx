/**
 * 视口网格组件
 * 
 * 在3D场景中显示网格辅助线
 */

import React from 'react'
import { GridHelper } from 'three'
import { useHelper } from '@react-three/drei'

/**
 * 视口网格属性
 */
export interface ViewportGridProps {
  /** 网格大小 */
  size?: number
  /** 网格分割数 */
  divisions?: number
  /** 网格颜色 */
  color?: string
  /** 中心线颜色 */
  centerLineColor?: string
  /** 是否可见 */
  visible?: boolean
}

/**
 * 视口网格组件
 */
const ViewportGrid: React.FC<ViewportGridProps> = ({
  size = 10,
  divisions = 10,
  color = '#888888',
  centerLineColor = '#444444',
  visible = true
}) => {
  const gridRef = React.useRef()

  // 使用 drei 的 useHelper 来显示网格
  useHelper(gridRef, GridHelper, [size, divisions, centerLineColor, color])

  if (!visible) return null

  return (
    <group ref={gridRef}>
      <gridHelper args={[size, divisions, centerLineColor, color]} />
    </group>
  )
}

export default ViewportGrid
