/**
 * 材质面板组件
 * 
 * 显示和管理项目中的材质资源
 */

import React, { useState } from 'react'

/**
 * 材质面板属性
 */
export interface MaterialsPanelProps {
  className?: string
  style?: React.CSSProperties
}

/**
 * 材质面板组件
 */
const MaterialsPanel: React.FC<MaterialsPanelProps> = ({
  className = '',
  style = {}
}) => {
  const [materials] = useState([
    { id: '1', name: '默认材质', type: 'Standard' },
    { id: '2', name: '金属材质', type: 'Metallic' },
    { id: '3', name: '玻璃材质', type: 'Glass' }
  ])

  return (
    <div className={`materials-panel ${className}`} style={style}>
      <div className="panel-header">
        <span>材质</span>
        <button className="text-xs px-2 py-1 bg-blue-600 hover:bg-blue-500 rounded">
          + 新建
        </button>
      </div>
      
      <div className="panel-content">
        {materials.map(material => (
          <div key={material.id} className="p-2 border-b border-gray-600 hover:bg-gray-700 cursor-pointer">
            <div className="font-medium">{material.name}</div>
            <div className="text-sm text-gray-400">{material.type}</div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default MaterialsPanel
