/**
 * DL-Engine 游戏化学习组件
 * 通过游戏机制提高学习动机和参与度
 */

import { defineComponent } from '../ComponentFunctions'

/**
 * 成就类型
 */
export enum AchievementType {
  PROGRESS = 'progress',         // 进度成就
  SKILL = 'skill',              // 技能成就
  TIME = 'time',                // 时间成就
  SOCIAL = 'social',            // 社交成就
  SPECIAL = 'special',          // 特殊成就
  STREAK = 'streak',            // 连续成就
  CHALLENGE = 'challenge'       // 挑战成就
}

/**
 * 徽章稀有度
 */
export enum BadgeRarity {
  COMMON = 'common',           // 普通
  UNCOMMON = 'uncommon',       // 不常见
  RARE = 'rare',              // 稀有
  EPIC = 'epic',              // 史诗
  LEGENDARY = 'legendary'      // 传奇
}

/**
 * 任务状态
 */
export enum QuestStatus {
  LOCKED = 'locked',           // 锁定
  AVAILABLE = 'available',     // 可用
  IN_PROGRESS = 'in_progress', // 进行中
  COMPLETED = 'completed',     // 已完成
  FAILED = 'failed',          // 失败
  EXPIRED = 'expired'         // 过期
}

/**
 * 游戏化成就
 */
export interface Achievement {
  /** 成就ID */
  id: string
  
  /** 成就名称 */
  name: string
  
  /** 成就描述 */
  description: string
  
  /** 成就类型 */
  type: AchievementType
  
  /** 成就图标 */
  iconUrl: string
  
  /** 奖励积分 */
  points: number
  
  /** 徽章稀有度 */
  rarity: BadgeRarity
  
  /** 解锁条件 */
  unlockConditions: Array<{
    type: 'score' | 'time' | 'count' | 'streak' | 'custom'
    target: number
    current: number
    description: string
  }>
  
  /** 是否已解锁 */
  unlocked: boolean
  
  /** 解锁时间 */
  unlockedAt?: Date
  
  /** 解锁进度 (0-100) */
  progress: number
  
  /** 是否隐藏 */
  hidden: boolean
  
  /** 前置成就 */
  prerequisites: string[]
}

/**
 * 游戏化任务
 */
export interface Quest {
  /** 任务ID */
  id: string
  
  /** 任务标题 */
  title: string
  
  /** 任务描述 */
  description: string
  
  /** 任务状态 */
  status: QuestStatus
  
  /** 任务类型 */
  type: 'daily' | 'weekly' | 'monthly' | 'story' | 'side' | 'challenge'
  
  /** 任务目标 */
  objectives: Array<{
    id: string
    description: string
    target: number
    current: number
    completed: boolean
  }>
  
  /** 任务奖励 */
  rewards: Array<{
    type: 'points' | 'badge' | 'item' | 'unlock'
    value: string | number
    description: string
  }>
  
  /** 任务难度 */
  difficulty: 'easy' | 'medium' | 'hard' | 'expert'
  
  /** 开始时间 */
  startTime?: Date
  
  /** 截止时间 */
  deadline?: Date
  
  /** 完成时间 */
  completedAt?: Date
  
  /** 前置任务 */
  prerequisites: string[]
  
  /** 任务进度 (0-100) */
  progress: number
}

/**
 * 排行榜条目
 */
export interface LeaderboardEntry {
  /** 用户ID */
  userId: string
  
  /** 用户名 */
  username: string
  
  /** 用户头像 */
  avatar?: string
  
  /** 分数 */
  score: number
  
  /** 排名 */
  rank: number
  
  /** 等级 */
  level: number
  
  /** 变化趋势 */
  trend: 'up' | 'down' | 'same'
  
  /** 上次更新时间 */
  lastUpdated: Date
}

/**
 * 等级系统
 */
export interface LevelSystem {
  /** 当前等级 */
  currentLevel: number
  
  /** 当前经验值 */
  currentXP: number
  
  /** 当前等级所需经验值 */
  currentLevelXP: number
  
  /** 下一等级所需经验值 */
  nextLevelXP: number
  
  /** 等级进度 (0-100) */
  levelProgress: number
  
  /** 等级标题 */
  levelTitle: string
  
  /** 等级图标 */
  levelIcon: string
  
  /** 等级特权 */
  levelPerks: string[]
}

/**
 * 游戏化统计
 */
export interface GamificationStats {
  /** 总积分 */
  totalPoints: number
  
  /** 今日积分 */
  todayPoints: number
  
  /** 本周积分 */
  weekPoints: number
  
  /** 本月积分 */
  monthPoints: number
  
  /** 连续学习天数 */
  streakDays: number
  
  /** 最长连续天数 */
  maxStreakDays: number
  
  /** 完成的成就数 */
  achievementsUnlocked: number
  
  /** 完成的任务数 */
  questsCompleted: number
  
  /** 总学习时间（分钟） */
  totalStudyTime: number
  
  /** 平均每日学习时间 */
  averageDailyTime: number
  
  /** 学习效率评分 */
  efficiencyScore: number
}

/**
 * 游戏化学习数据
 */
export interface GamificationData {
  /** 学习者ID */
  learnerId: string
  
  /** 课程ID */
  courseId: string
  
  /** 等级系统 */
  levelSystem: LevelSystem
  
  /** 成就列表 */
  achievements: Achievement[]
  
  /** 任务列表 */
  quests: Quest[]
  
  /** 游戏化统计 */
  statistics: GamificationStats
  
  /** 排行榜数据 */
  leaderboard: LeaderboardEntry[]
  
  /** 虚拟物品库存 */
  inventory: Array<{
    itemId: string
    itemName: string
    itemType: string
    quantity: number
    description: string
    iconUrl: string
  }>
  
  /** 游戏化设置 */
  settings: {
    enableNotifications: boolean
    enableSounds: boolean
    enableAnimations: boolean
    showProgress: boolean
    shareAchievements: boolean
    competitiveMode: boolean
  }
  
  /** 每日签到 */
  dailyCheckin: {
    lastCheckinDate?: Date
    consecutiveDays: number
    totalCheckins: number
    checkinRewards: Array<{
      day: number
      reward: string
      claimed: boolean
    }>
  }
  
  /** 活动历史 */
  activityHistory: Array<{
    timestamp: Date
    type: 'achievement' | 'quest' | 'level_up' | 'checkin' | 'reward'
    description: string
    points: number
    metadata: Record<string, any>
  }>
  
  /** 创建时间 */
  createdAt: Date
  
  /** 更新时间 */
  updatedAt: Date
}

/**
 * 游戏化学习组件
 */
export const GamificationComponent = defineComponent({
  name: 'Gamification',
  schema: {
    learnerId: '',
    courseId: '',
    levelSystem: {
      currentLevel: 1,
      currentXP: 0,
      currentLevelXP: 0,
      nextLevelXP: 100,
      levelProgress: 0,
      levelTitle: '新手学习者',
      levelIcon: 'novice.png',
      levelPerks: []
    } as LevelSystem,
    achievements: [] as Achievement[],
    quests: [] as Quest[],
    statistics: {
      totalPoints: 0,
      todayPoints: 0,
      weekPoints: 0,
      monthPoints: 0,
      streakDays: 0,
      maxStreakDays: 0,
      achievementsUnlocked: 0,
      questsCompleted: 0,
      totalStudyTime: 0,
      averageDailyTime: 0,
      efficiencyScore: 0
    } as GamificationStats,
    leaderboard: [] as LeaderboardEntry[],
    inventory: [],
    settings: {
      enableNotifications: true,
      enableSounds: true,
      enableAnimations: true,
      showProgress: true,
      shareAchievements: false,
      competitiveMode: true
    },
    dailyCheckin: {
      lastCheckinDate: undefined,
      consecutiveDays: 0,
      totalCheckins: 0,
      checkinRewards: [
        { day: 1, reward: '10 积分', claimed: false },
        { day: 2, reward: '15 积分', claimed: false },
        { day: 3, reward: '20 积分', claimed: false },
        { day: 7, reward: '特殊徽章', claimed: false },
        { day: 14, reward: '经验加倍卡', claimed: false },
        { day: 30, reward: '传奇徽章', claimed: false }
      ]
    },
    activityHistory: [],
    createdAt: new Date(),
    updatedAt: new Date()
  } as GamificationData,
  
  onAdd: (entity, component) => {
    console.log(`Gamification system initialized for learner ${component.learnerId}`)
    component.createdAt = new Date()
    component.updatedAt = new Date()
    
    // 初始化默认成就和任务
    GamificationUtils.initializeDefaultContent(component)
  },
  
  onRemove: (entity, component) => {
    console.log(`Gamification system removed for learner ${component.learnerId}`)
  },
  
  onSet: (entity, component) => {
    component.updatedAt = new Date()
  }
})

/**
 * 游戏化学习工具函数
 */
export const GamificationUtils = {
  /**
   * 初始化默认内容
   */
  initializeDefaultContent: (gamification: GamificationData): void => {
    // 初始化基础成就
    const defaultAchievements: Achievement[] = [
      {
        id: 'first_lesson',
        name: '初学者',
        description: '完成第一个课程',
        type: AchievementType.PROGRESS,
        iconUrl: 'first_lesson.png',
        points: 50,
        rarity: BadgeRarity.COMMON,
        unlockConditions: [
          { type: 'count', target: 1, current: 0, description: '完成课程数量' }
        ],
        unlocked: false,
        progress: 0,
        hidden: false,
        prerequisites: []
      },
      {
        id: 'week_streak',
        name: '坚持不懈',
        description: '连续学习7天',
        type: AchievementType.STREAK,
        iconUrl: 'week_streak.png',
        points: 200,
        rarity: BadgeRarity.UNCOMMON,
        unlockConditions: [
          { type: 'streak', target: 7, current: 0, description: '连续学习天数' }
        ],
        unlocked: false,
        progress: 0,
        hidden: false,
        prerequisites: []
      }
    ]
    
    // 初始化每日任务
    const defaultQuests: Quest[] = [
      {
        id: 'daily_study',
        title: '每日学习',
        description: '今天学习至少30分钟',
        status: QuestStatus.AVAILABLE,
        type: 'daily',
        objectives: [
          {
            id: 'study_time',
            description: '学习时间达到30分钟',
            target: 30,
            current: 0,
            completed: false
          }
        ],
        rewards: [
          { type: 'points', value: 50, description: '50积分' }
        ],
        difficulty: 'easy',
        deadline: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时后
        prerequisites: [],
        progress: 0
      }
    ]
    
    gamification.achievements = defaultAchievements
    gamification.quests = defaultQuests
  },
  
  /**
   * 添加经验值
   */
  addExperience: (gamification: GamificationData, xp: number, reason: string): void => {
    gamification.levelSystem.currentXP += xp
    gamification.statistics.totalPoints += xp
    
    // 检查是否升级
    while (gamification.levelSystem.currentXP >= gamification.levelSystem.nextLevelXP) {
      GamificationUtils.levelUp(gamification)
    }
    
    // 更新等级进度
    const xpInCurrentLevel = gamification.levelSystem.currentXP - gamification.levelSystem.currentLevelXP
    const xpNeededForLevel = gamification.levelSystem.nextLevelXP - gamification.levelSystem.currentLevelXP
    gamification.levelSystem.levelProgress = Math.round((xpInCurrentLevel / xpNeededForLevel) * 100)
    
    // 记录活动
    GamificationUtils.recordActivity(gamification, 'reward', `获得 ${xp} 经验值: ${reason}`, xp)
    
    gamification.updatedAt = new Date()
  },
  
  /**
   * 升级
   */
  levelUp: (gamification: GamificationData): void => {
    gamification.levelSystem.currentLevel++
    gamification.levelSystem.currentLevelXP = gamification.levelSystem.nextLevelXP
    gamification.levelSystem.nextLevelXP = GamificationUtils.calculateNextLevelXP(gamification.levelSystem.currentLevel)
    
    // 更新等级标题和图标
    const levelInfo = GamificationUtils.getLevelInfo(gamification.levelSystem.currentLevel)
    gamification.levelSystem.levelTitle = levelInfo.title
    gamification.levelSystem.levelIcon = levelInfo.icon
    gamification.levelSystem.levelPerks = levelInfo.perks
    
    // 记录升级活动
    GamificationUtils.recordActivity(
      gamification, 
      'level_up', 
      `升级到 ${gamification.levelSystem.currentLevel} 级: ${levelInfo.title}`, 
      0
    )
    
    console.log(`Level up! New level: ${gamification.levelSystem.currentLevel}`)
  },
  
  /**
   * 计算下一等级所需经验值
   */
  calculateNextLevelXP: (level: number): number => {
    // 指数增长公式
    return Math.floor(100 * Math.pow(1.5, level - 1))
  },
  
  /**
   * 获取等级信息
   */
  getLevelInfo: (level: number): { title: string; icon: string; perks: string[] } => {
    if (level < 5) return { title: '新手学习者', icon: 'novice.png', perks: [] }
    if (level < 10) return { title: '初级学者', icon: 'beginner.png', perks: ['解锁高级课程'] }
    if (level < 20) return { title: '中级学者', icon: 'intermediate.png', perks: ['解锁专家模式', '获得学习加速'] }
    if (level < 35) return { title: '高级学者', icon: 'advanced.png', perks: ['解锁导师功能', '获得双倍经验'] }
    if (level < 50) return { title: '专家学者', icon: 'expert.png', perks: ['解锁创作工具', '获得特殊徽章'] }
    return { title: '大师学者', icon: 'master.png', perks: ['解锁所有功能', '获得传奇地位'] }
  },
  
  /**
   * 解锁成就
   */
  unlockAchievement: (gamification: GamificationData, achievementId: string): boolean => {
    const achievement = gamification.achievements.find(a => a.id === achievementId)
    if (!achievement || achievement.unlocked) return false
    
    achievement.unlocked = true
    achievement.unlockedAt = new Date()
    achievement.progress = 100
    
    // 添加奖励积分
    GamificationUtils.addExperience(gamification, achievement.points, `解锁成就: ${achievement.name}`)
    
    // 更新统计
    gamification.statistics.achievementsUnlocked++
    
    // 记录活动
    GamificationUtils.recordActivity(
      gamification, 
      'achievement', 
      `解锁成就: ${achievement.name}`, 
      achievement.points
    )
    
    console.log(`Achievement unlocked: ${achievement.name}`)
    return true
  },
  
  /**
   * 完成任务
   */
  completeQuest: (gamification: GamificationData, questId: string): boolean => {
    const quest = gamification.quests.find(q => q.id === questId)
    if (!quest || quest.status === QuestStatus.COMPLETED) return false
    
    // 检查所有目标是否完成
    const allObjectivesCompleted = quest.objectives.every(obj => obj.completed)
    if (!allObjectivesCompleted) return false
    
    quest.status = QuestStatus.COMPLETED
    quest.completedAt = new Date()
    quest.progress = 100
    
    // 发放奖励
    quest.rewards.forEach(reward => {
      if (reward.type === 'points') {
        GamificationUtils.addExperience(gamification, reward.value as number, `完成任务: ${quest.title}`)
      }
    })
    
    // 更新统计
    gamification.statistics.questsCompleted++
    
    // 记录活动
    GamificationUtils.recordActivity(
      gamification, 
      'quest', 
      `完成任务: ${quest.title}`, 
      quest.rewards.find(r => r.type === 'points')?.value as number || 0
    )
    
    console.log(`Quest completed: ${quest.title}`)
    return true
  },
  
  /**
   * 每日签到
   */
  dailyCheckin: (gamification: GamificationData): boolean => {
    const today = new Date()
    const lastCheckin = gamification.dailyCheckin.lastCheckinDate
    
    // 检查是否已经签到
    if (lastCheckin && lastCheckin.toDateString() === today.toDateString()) {
      return false
    }
    
    // 检查连续签到
    if (lastCheckin) {
      const daysDiff = Math.floor((today.getTime() - lastCheckin.getTime()) / (1000 * 60 * 60 * 24))
      if (daysDiff === 1) {
        gamification.dailyCheckin.consecutiveDays++
      } else if (daysDiff > 1) {
        gamification.dailyCheckin.consecutiveDays = 1
      }
    } else {
      gamification.dailyCheckin.consecutiveDays = 1
    }
    
    gamification.dailyCheckin.lastCheckinDate = today
    gamification.dailyCheckin.totalCheckins++
    
    // 发放签到奖励
    const reward = gamification.dailyCheckin.checkinRewards.find(r => 
      r.day === gamification.dailyCheckin.consecutiveDays && !r.claimed
    )
    
    if (reward) {
      reward.claimed = true
      if (reward.reward.includes('积分')) {
        const points = parseInt(reward.reward.match(/\d+/)?.[0] || '10')
        GamificationUtils.addExperience(gamification, points, '每日签到奖励')
      }
    } else {
      // 默认签到奖励
      GamificationUtils.addExperience(gamification, 10, '每日签到')
    }
    
    // 记录活动
    GamificationUtils.recordActivity(
      gamification, 
      'checkin', 
      `每日签到 (连续${gamification.dailyCheckin.consecutiveDays}天)`, 
      10
    )
    
    return true
  },
  
  /**
   * 记录活动
   */
  recordActivity: (
    gamification: GamificationData, 
    type: 'achievement' | 'quest' | 'level_up' | 'checkin' | 'reward',
    description: string,
    points: number
  ): void => {
    gamification.activityHistory.push({
      timestamp: new Date(),
      type,
      description,
      points,
      metadata: {}
    })
    
    // 限制历史记录数量
    if (gamification.activityHistory.length > 100) {
      gamification.activityHistory = gamification.activityHistory.slice(-100)
    }
  },
  
  /**
   * 更新任务进度
   */
  updateQuestProgress: (gamification: GamificationData, questId: string, objectiveId: string, progress: number): void => {
    const quest = gamification.quests.find(q => q.id === questId)
    if (!quest) return
    
    const objective = quest.objectives.find(o => o.id === objectiveId)
    if (!objective) return
    
    objective.current = Math.min(objective.target, objective.current + progress)
    objective.completed = objective.current >= objective.target
    
    // 更新任务总进度
    const completedObjectives = quest.objectives.filter(o => o.completed).length
    quest.progress = Math.round((completedObjectives / quest.objectives.length) * 100)
    
    // 检查任务是否完成
    if (quest.objectives.every(o => o.completed)) {
      GamificationUtils.completeQuest(gamification, questId)
    }
    
    gamification.updatedAt = new Date()
  },
  
  /**
   * 获取排行榜
   */
  updateLeaderboard: (gamification: GamificationData, allPlayers: GamificationData[]): void => {
    const leaderboard = allPlayers
      .map(player => ({
        userId: player.learnerId,
        username: `学习者${player.learnerId.slice(-4)}`,
        score: player.statistics.totalPoints,
        rank: 0,
        level: player.levelSystem.currentLevel,
        trend: 'same' as const,
        lastUpdated: new Date()
      }))
      .sort((a, b) => b.score - a.score)
      .map((entry, index) => ({ ...entry, rank: index + 1 }))
    
    gamification.leaderboard = leaderboard.slice(0, 50) // 只保留前50名
  }
}
