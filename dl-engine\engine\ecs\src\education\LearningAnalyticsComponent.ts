/**
 * DL-Engine 学习分析组件
 * 收集、分析和可视化学习数据，提供个性化学习建议
 */

import { defineComponent } from '../ComponentFunctions'

/**
 * 学习行为类型
 */
export enum LearningBehaviorType {
  VIEW_CONTENT = 'view_content',           // 查看内容
  COMPLETE_TASK = 'complete_task',         // 完成任务
  SUBMIT_ANSWER = 'submit_answer',         // 提交答案
  ASK_QUESTION = 'ask_question',           // 提问
  TAKE_NOTES = 'take_notes',              // 记笔记
  REVIEW_CONTENT = 'review_content',       // 复习内容
  COLLABORATE = 'collaborate',             // 协作
  SEEK_HELP = 'seek_help',                // 寻求帮助
  PAUSE_LEARNING = 'pause_learning',       // 暂停学习
  RESUME_LEARNING = 'resume_learning'      // 恢复学习
}

/**
 * 学习行为记录
 */
export interface LearningBehavior {
  /** 行为ID */
  id: string
  
  /** 学习者ID */
  learnerId: string
  
  /** 行为类型 */
  type: LearningBehaviorType
  
  /** 行为发生时间 */
  timestamp: Date
  
  /** 相关内容ID */
  contentId?: string
  
  /** 行为持续时间（秒） */
  duration: number
  
  /** 行为结果 */
  result?: 'success' | 'failure' | 'partial'
  
  /** 行为得分 */
  score?: number
  
  /** 行为上下文 */
  context: {
    deviceType: string
    browserType: string
    screenResolution: string
    location?: string
  }
  
  /** 额外数据 */
  metadata: Record<string, any>
}

/**
 * 学习模式
 */
export interface LearningPattern {
  /** 模式ID */
  id: string
  
  /** 模式名称 */
  name: string
  
  /** 模式描述 */
  description: string
  
  /** 模式特征 */
  characteristics: string[]
  
  /** 置信度 (0-1) */
  confidence: number
  
  /** 发现时间 */
  discoveredAt: Date
}

/**
 * 学习建议
 */
export interface LearningRecommendation {
  /** 建议ID */
  id: string
  
  /** 建议类型 */
  type: 'content' | 'strategy' | 'pace' | 'difficulty' | 'resource' | 'social'
  
  /** 建议标题 */
  title: string
  
  /** 建议描述 */
  description: string
  
  /** 建议优先级 */
  priority: 'low' | 'medium' | 'high' | 'urgent'
  
  /** 建议理由 */
  reasoning: string
  
  /** 预期效果 */
  expectedOutcome: string
  
  /** 置信度 (0-1) */
  confidence: number
  
  /** 生成时间 */
  generatedAt: Date
  
  /** 是否已应用 */
  applied: boolean
  
  /** 应用时间 */
  appliedAt?: Date
  
  /** 效果评估 */
  effectiveness?: number
}

/**
 * 学习统计数据
 */
export interface LearningStatistics {
  /** 总学习时间（分钟） */
  totalStudyTime: number
  
  /** 活跃学习天数 */
  activeDays: number
  
  /** 完成的任务数 */
  completedTasks: number
  
  /** 平均得分 */
  averageScore: number
  
  /** 学习频率（次/周） */
  learningFrequency: number
  
  /** 平均学习时长（分钟/次） */
  averageSessionDuration: number
  
  /** 知识点掌握率 */
  knowledgeMasteryRate: number
  
  /** 错误率 */
  errorRate: number
  
  /** 寻求帮助频率 */
  helpSeekingFrequency: number
  
  /** 协作参与度 */
  collaborationLevel: number
}

/**
 * 学习预测
 */
export interface LearningPrediction {
  /** 预测类型 */
  type: 'performance' | 'completion' | 'difficulty' | 'engagement' | 'dropout'
  
  /** 预测值 */
  value: number
  
  /** 置信区间 */
  confidenceInterval: [number, number]
  
  /** 预测时间范围 */
  timeHorizon: number // 天数
  
  /** 影响因素 */
  factors: Array<{
    factor: string
    importance: number
    direction: 'positive' | 'negative'
  }>
  
  /** 预测时间 */
  predictedAt: Date
}

/**
 * 学习分析数据
 */
export interface LearningAnalyticsData {
  /** 学习者ID */
  learnerId: string
  
  /** 课程ID */
  courseId: string
  
  /** 分析开始时间 */
  analysisStartTime: Date
  
  /** 最后更新时间 */
  lastUpdateTime: Date
  
  /** 学习行为记录 */
  behaviors: LearningBehavior[]
  
  /** 学习模式 */
  patterns: LearningPattern[]
  
  /** 学习建议 */
  recommendations: LearningRecommendation[]
  
  /** 学习统计 */
  statistics: LearningStatistics
  
  /** 学习预测 */
  predictions: LearningPrediction[]
  
  /** 学习风格分析 */
  learningStyleAnalysis: {
    visual: number
    auditory: number
    kinesthetic: number
    reading: number
  }
  
  /** 认知负荷分析 */
  cognitiveLoadAnalysis: {
    intrinsic: number
    extraneous: number
    germane: number
  }
  
  /** 情感状态分析 */
  emotionalStateAnalysis: {
    engagement: number
    frustration: number
    confidence: number
    motivation: number
  }
  
  /** 社交网络分析 */
  socialNetworkAnalysis: {
    connections: Array<{
      peerId: string
      strength: number
      type: 'study_partner' | 'mentor' | 'mentee' | 'collaborator'
    }>
    centrality: number
    clustering: number
  }
}

/**
 * 学习分析组件
 */
export const LearningAnalyticsComponent = defineComponent({
  name: 'LearningAnalytics',
  schema: {
    learnerId: '',
    courseId: '',
    analysisStartTime: new Date(),
    lastUpdateTime: new Date(),
    behaviors: [] as LearningBehavior[],
    patterns: [] as LearningPattern[],
    recommendations: [] as LearningRecommendation[],
    statistics: {
      totalStudyTime: 0,
      activeDays: 0,
      completedTasks: 0,
      averageScore: 0,
      learningFrequency: 0,
      averageSessionDuration: 0,
      knowledgeMasteryRate: 0,
      errorRate: 0,
      helpSeekingFrequency: 0,
      collaborationLevel: 0
    } as LearningStatistics,
    predictions: [] as LearningPrediction[],
    learningStyleAnalysis: {
      visual: 0,
      auditory: 0,
      kinesthetic: 0,
      reading: 0
    },
    cognitiveLoadAnalysis: {
      intrinsic: 0,
      extraneous: 0,
      germane: 0
    },
    emotionalStateAnalysis: {
      engagement: 0,
      frustration: 0,
      confidence: 0,
      motivation: 0
    },
    socialNetworkAnalysis: {
      connections: [],
      centrality: 0,
      clustering: 0
    }
  } as LearningAnalyticsData,
  
  onAdd: (entity, component) => {
    console.log(`Learning analytics started for learner ${component.learnerId}`)
    component.analysisStartTime = new Date()
    component.lastUpdateTime = new Date()
  },
  
  onRemove: (entity, component) => {
    console.log(`Learning analytics removed for learner ${component.learnerId}`)
  },
  
  onSet: (entity, component) => {
    component.lastUpdateTime = new Date()
  }
})

/**
 * 学习分析工具函数
 */
export const LearningAnalyticsUtils = {
  /**
   * 记录学习行为
   */
  recordBehavior: (analytics: LearningAnalyticsData, behavior: LearningBehavior): void => {
    analytics.behaviors.push(behavior)
    analytics.lastUpdateTime = new Date()
    
    // 限制行为记录数量，保留最近的1000条
    if (analytics.behaviors.length > 1000) {
      analytics.behaviors = analytics.behaviors.slice(-1000)
    }
  },
  
  /**
   * 分析学习模式
   */
  analyzePatterns: (analytics: LearningAnalyticsData): LearningPattern[] => {
    const patterns: LearningPattern[] = []
    
    // 分析学习时间模式
    const timePattern = LearningAnalyticsUtils.analyzeTimePattern(analytics.behaviors)
    if (timePattern) patterns.push(timePattern)
    
    // 分析学习序列模式
    const sequencePattern = LearningAnalyticsUtils.analyzeSequencePattern(analytics.behaviors)
    if (sequencePattern) patterns.push(sequencePattern)
    
    analytics.patterns = patterns
    return patterns
  },
  
  /**
   * 生成学习建议
   */
  generateRecommendations: (analytics: LearningAnalyticsData): LearningRecommendation[] => {
    const recommendations: LearningRecommendation[] = []
    
    // 基于学习统计生成建议
    if (analytics.statistics.errorRate > 0.3) {
      recommendations.push({
        id: `rec_${Date.now()}_1`,
        type: 'strategy',
        title: '建议调整学习策略',
        description: '您的错误率较高，建议放慢学习节奏，加强基础知识复习',
        priority: 'high',
        reasoning: `当前错误率为 ${(analytics.statistics.errorRate * 100).toFixed(1)}%，超过了30%的阈值`,
        expectedOutcome: '降低错误率，提高学习效果',
        confidence: 0.8,
        generatedAt: new Date(),
        applied: false
      })
    }
    
    if (analytics.statistics.averageSessionDuration < 15) {
      recommendations.push({
        id: `rec_${Date.now()}_2`,
        type: 'pace',
        title: '建议延长学习时间',
        description: '您的平均学习时长较短，建议每次学习至少20-30分钟以提高效果',
        priority: 'medium',
        reasoning: `当前平均学习时长为 ${analytics.statistics.averageSessionDuration} 分钟`,
        expectedOutcome: '提高学习深度和记忆效果',
        confidence: 0.7,
        generatedAt: new Date(),
        applied: false
      })
    }
    
    analytics.recommendations = recommendations
    return recommendations
  },
  
  /**
   * 更新学习统计
   */
  updateStatistics: (analytics: LearningAnalyticsData): void => {
    const behaviors = analytics.behaviors
    const now = new Date()
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    
    // 计算总学习时间
    analytics.statistics.totalStudyTime = behaviors
      .filter(b => b.type === LearningBehaviorType.VIEW_CONTENT)
      .reduce((total, b) => total + b.duration, 0) / 60 // 转换为分钟
    
    // 计算活跃天数
    const activeDates = new Set(
      behaviors.map(b => b.timestamp.toDateString())
    )
    analytics.statistics.activeDays = activeDates.size
    
    // 计算完成任务数
    analytics.statistics.completedTasks = behaviors
      .filter(b => b.type === LearningBehaviorType.COMPLETE_TASK && b.result === 'success')
      .length
    
    // 计算平均得分
    const scoredBehaviors = behaviors.filter(b => b.score !== undefined)
    if (scoredBehaviors.length > 0) {
      analytics.statistics.averageScore = scoredBehaviors
        .reduce((sum, b) => sum + (b.score || 0), 0) / scoredBehaviors.length
    }
    
    // 计算学习频率（最近一周）
    const recentBehaviors = behaviors.filter(b => b.timestamp >= oneWeekAgo)
    analytics.statistics.learningFrequency = recentBehaviors.length / 7
    
    analytics.lastUpdateTime = new Date()
  },
  
  /**
   * 分析时间模式
   */
  analyzeTimePattern: (behaviors: LearningBehavior[]): LearningPattern | null => {
    const hourCounts = new Array(24).fill(0)
    
    behaviors.forEach(b => {
      const hour = b.timestamp.getHours()
      hourCounts[hour]++
    })
    
    const maxCount = Math.max(...hourCounts)
    const peakHour = hourCounts.indexOf(maxCount)
    
    if (maxCount > behaviors.length * 0.2) { // 如果某个时段占比超过20%
      return {
        id: `pattern_time_${Date.now()}`,
        name: '学习时间偏好',
        description: `倾向于在 ${peakHour}:00 时段学习`,
        characteristics: [`peak_hour_${peakHour}`, 'time_preference'],
        confidence: maxCount / behaviors.length,
        discoveredAt: new Date()
      }
    }
    
    return null
  },
  
  /**
   * 分析序列模式
   */
  analyzeSequencePattern: (behaviors: LearningBehavior[]): LearningPattern | null => {
    // 简单的序列模式分析：查看内容 -> 完成任务
    let viewToTaskCount = 0
    
    for (let i = 0; i < behaviors.length - 1; i++) {
      if (behaviors[i].type === LearningBehaviorType.VIEW_CONTENT &&
          behaviors[i + 1].type === LearningBehaviorType.COMPLETE_TASK) {
        viewToTaskCount++
      }
    }
    
    if (viewToTaskCount > behaviors.length * 0.3) {
      return {
        id: `pattern_sequence_${Date.now()}`,
        name: '顺序学习模式',
        description: '倾向于先查看内容再完成任务',
        characteristics: ['sequential_learner', 'methodical'],
        confidence: viewToTaskCount / behaviors.length,
        discoveredAt: new Date()
      }
    }
    
    return null
  }
}
